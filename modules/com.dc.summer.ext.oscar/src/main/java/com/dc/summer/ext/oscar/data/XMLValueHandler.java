package com.dc.summer.ext.oscar.data;

import com.dc.code.NotNull;
import com.dc.summer.model.data.DBDContent;
import com.dc.summer.model.exec.DBCException;
import com.dc.summer.model.exec.DBCSession;
import com.dc.summer.model.exec.jdbc.JDBCResultSet;
import com.dc.summer.model.impl.jdbc.data.handlers.JDBCContentValueHandler;
import com.dc.summer.model.struct.DBSTypedObject;

import java.sql.SQLException;
import java.sql.SQLXML;

public class XMLValueHandler extends JDBC<PERSON>ontentValueHandler {
   public static final XMLValueHandler INSTANCE = new XMLValueHandler();

   @NotNull
   public String getValueContentType(@NotNull DBSTypedObject attribute) {
      return "text/xml";
   }

   protected DBDContent fetchColumnValue(DBCSession session, JDBCResultSet resultSet, DBSTypedObject type, int index) throws DBCException, SQLException {
      Object object;
      try {
         object = resultSet.getSQLXML(index);
      } catch (SQLException var7) {
         try {
            object = resultSet.getObject(index);
         } catch (SQLException var6) {
            object = null;
         }
      }

      if (object == null) {
         return new ContentXML(session.getExecutionContext(), (SQLXML)null);
      } else if (object.getClass().getName().equals("st.xdb.XMLType")) {
         return new ContentXML(session.getExecutionContext(), new XMLWrapper(object));
      } else if (object instanceof SQLXML) {
         return new ContentXML(session.getExecutionContext(), (SQLXML)object);
      } else {
         throw new DBCException("Unsupported object type: " + object.getClass().getName());
      }
   }

   @Override
   public DBDContent getValueFromObject(@NotNull DBCSession session, @NotNull DBSTypedObject type, Object object, boolean copy, boolean validateValue) throws DBCException {
      return (DBDContent)(object == null ? new ContentXML(session.getExecutionContext(), (SQLXML)null) : super.getValueFromObject(session, type, object, copy, validateValue));
   }
}
