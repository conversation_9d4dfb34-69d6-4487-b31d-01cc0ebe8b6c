
package com.dc.summer.ext.datavirtuality;

import com.dc.summer.model.DBPDataSource;
import com.dc.code.NotNull;
import com.dc.summer.DBException;
import com.dc.summer.Log;
import com.dc.summer.ext.datavirtuality.model.DataVirtualityDataSource;
import com.dc.summer.ext.datavirtuality.model.DataVirtualityMetaModel;
import com.dc.summer.model.DBPDataSourceContainer;
import com.dc.summer.model.connection.DBPConnectionConfiguration;
import com.dc.summer.model.connection.DBPDriver;
import com.dc.summer.model.impl.jdbc.JDBCDataSourceProvider;
import com.dc.summer.model.runtime.DBRProgressMonitor;
import com.dc.utils.CommonUtils;

public class DataVirtualityDataSourceProvider extends JDBCDataSourceProvider {

    private static final Log log = Log.getLog(DataVirtualityDataSourceProvider.class);

    @Override
    public long getFeatures()
    {
        return FEATURE_SCHEMAS;
    }

    @Override
    public String getConnectionURL(DBPDriver driver, DBPConnectionConfiguration connectionInfo)
    {
        StringBuilder url = new StringBuilder();
        url.append("jdbc:datavirtuality:");

        url.append(connectionInfo.getDatabaseName());
        url.append("@");
        if (!CommonUtils.isEmpty(connectionInfo.getProviderProperty(DataVirtualityConstants.PROP_SSL))) {
            url.append(connectionInfo.getProviderProperty(DataVirtualityConstants.PROP_SSL));
        }
        else {
            url.append("mm");
        }

        url.append("://");
        url.append(connectionInfo.getHostName());
        if (!CommonUtils.isEmpty(connectionInfo.getHostPort())) {
            url.append(":").append(connectionInfo.getHostPort());
        }

        log.debug("getConnectionURL" + url.toString());

        return url.toString();
    }

    private static boolean addParameter(StringBuilder url, String name, String value, boolean hasParam) {
        if (!CommonUtils.isEmpty(value)) {
            if (hasParam) url.append("&");
            url.append(name).append("=").append(value);
            return true;
        }
        return hasParam;
    }

    @NotNull
    @Override
    public DBPDataSource openDataSource(
        @NotNull DBRProgressMonitor monitor,
        @NotNull DBPDataSourceContainer container)
        throws DBException
    {
        return new DataVirtualityDataSource(monitor, container, new DataVirtualityMetaModel());
    }

}
