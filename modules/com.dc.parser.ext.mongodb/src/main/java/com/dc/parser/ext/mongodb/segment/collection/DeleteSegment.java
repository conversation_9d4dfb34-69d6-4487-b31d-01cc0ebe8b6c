package com.dc.parser.ext.mongodb.segment.collection;

import com.dc.parser.ext.mongodb.segment.BsonObjectSegment;
import com.dc.parser.ext.mongodb.segment.MethodSegment;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class DeleteSegment extends MethodSegment {
    private BsonObjectSegment filter;
    private BsonObjectSegment options;
    public DeleteSegment(int startIndex, int stopIndex) {
        super(startIndex, stopIndex);
    }
}