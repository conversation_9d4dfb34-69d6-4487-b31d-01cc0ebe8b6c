package com.dc.parser.ext.mongodb.segment.collection;

import com.dc.parser.ext.mongodb.segment.BsonArraySegment;
import com.dc.parser.ext.mongodb.segment.BsonObjectSegment;
import com.dc.parser.ext.mongodb.segment.MethodSegment;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class AggregateSegment extends MethodSegment {
    private BsonArraySegment pipeline;
    private BsonObjectSegment options;

    public AggregateSegment(int startIndex, int stopIndex) {
        super(startIndex, stopIndex);
    }
}
