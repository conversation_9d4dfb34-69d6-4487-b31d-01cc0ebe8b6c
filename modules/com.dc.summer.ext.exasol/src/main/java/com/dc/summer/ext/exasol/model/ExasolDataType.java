/*
 * DBeaver - Universal Database Manager
 * Copyright (C) 2016 <PERSON> (<EMAIL>)
 * Copyright (C) 2010-2022 DBeaver Corp and others
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.dc.summer.ext.exasol.model;

import com.dc.summer.DBException;
import com.dc.summer.Log;
import com.dc.summer.model.DBPDataKind;
import com.dc.summer.model.DBPEvaluationContext;
import com.dc.summer.model.DBPQualifiedObject;
import com.dc.summer.model.DBUtils;
import com.dc.summer.model.exec.DBCLogicalOperator;
import com.dc.summer.model.impl.DBObjectNameCaseTransformer;
import com.dc.summer.model.impl.jdbc.JDBCUtils;
import com.dc.summer.model.meta.Property;
import com.dc.summer.model.runtime.DBRProgressMonitor;
import com.dc.summer.model.struct.DBSDataType;
import com.dc.summer.model.struct.DBSTypedObject;
import com.dc.code.NotNull;
import com.dc.code.Nullable;
import com.dc.summer.ext.exasol.ExasolConstants;
import com.dc.summer.model.meta.PropertyLength;
import com.dc.summer.model.struct.DBSObject;

import java.sql.ResultSet;
import java.sql.Types;

/**
 * Exasol data types
 *
 * <AUTHOR> Griesser
 */
public class ExasolDataType extends ExasolObject<DBSObject> implements DBSDataType, DBPQualifiedObject {


    private static final Log LOG = Log.getLog(ExasolDataType.class);


    private DBSObject parentNode; // see below

    private ExasolSchema exasolSchema;


    private TypeDesc typeDesc;

    private long exasolTypeId;


    private Integer length;
    private Integer scale;


    private String name;


    // -----------------------
    // Constructors
    // -----------------------
    protected ExasolDataType(DBSObject parent, String name, boolean persisted) {
        super(parent, name, persisted);

    }

    public ExasolDataType(DBSObject owner, ResultSet dbResult) throws DBException {
        super(owner, JDBCUtils.safeGetString(dbResult, "TYPE_NAME"), true);

        this.exasolTypeId = JDBCUtils.safeGetLong(dbResult, "TYPE_ID");
        this.length = JDBCUtils.safeGetInt(dbResult, "PRECISION");
        this.scale = JDBCUtils.safeGetInt(dbResult, "MINIMUM_SCALE");

        TypeDesc tempTypeDesc = null;
        String typeName = JDBCUtils.safeGetString(dbResult, "TYPE_NAME");
        int precision = JDBCUtils.safeGetInt(dbResult, "PRECISION");
        int minimumScale = JDBCUtils.safeGetInt(dbResult, "MINIMUM_SCALE");
        int maximumScale = JDBCUtils.safeGetInt(dbResult, "MAXIMUM_SCALE");

        this.name = typeName;
        switch (name) {
            case "BIGINT":
                tempTypeDesc = new TypeDesc(DBPDataKind.NUMERIC, Types.BIGINT, precision, minimumScale, maximumScale, typeName);
                break;
            case "INTEGER":
                tempTypeDesc = new TypeDesc(DBPDataKind.NUMERIC, Types.INTEGER, precision, minimumScale, maximumScale, typeName);
                break;
            case ExasolConstants.TYPE_DECIMAL:
                tempTypeDesc = new TypeDesc(DBPDataKind.NUMERIC, Types.DECIMAL, precision, minimumScale, maximumScale, typeName);
                break;
            case "DOUBLE PRECISION":
                tempTypeDesc = new TypeDesc(DBPDataKind.NUMERIC, Types.DOUBLE, precision, minimumScale, maximumScale, typeName);
                break;
            case "FLOAT":
                tempTypeDesc = new TypeDesc(DBPDataKind.NUMERIC, Types.FLOAT, precision, minimumScale, maximumScale, typeName);
                break;
            case "INTERVAL DAY TO SECOND":
                tempTypeDesc = new TypeDesc(DBPDataKind.STRING, Types.VARCHAR, precision, minimumScale, maximumScale, typeName);
                break;
            case "INTERVAL YEAR TO MONTH":
                tempTypeDesc = new TypeDesc(DBPDataKind.STRING, Types.VARCHAR, precision, minimumScale, maximumScale, typeName);
                break;
            case "SMALLINT":
                tempTypeDesc = new TypeDesc(DBPDataKind.NUMERIC, Types.SMALLINT, precision, minimumScale, maximumScale, typeName);
                break;
            case "TINYINT":
                tempTypeDesc = new TypeDesc(DBPDataKind.NUMERIC, Types.TINYINT, precision, minimumScale, maximumScale, typeName);
                break;
            case "GEOMETRY":
                tempTypeDesc = new TypeDesc(DBPDataKind.STRING, Types.VARCHAR, precision, minimumScale, maximumScale, typeName);
                break;
            case "BOOLEAN":
                tempTypeDesc = new TypeDesc(DBPDataKind.BOOLEAN, Types.BOOLEAN, precision, minimumScale, maximumScale, typeName);
                break;
            case ExasolConstants.TYPE_CHAR:
                tempTypeDesc = new TypeDesc(DBPDataKind.STRING, Types.CHAR, precision, minimumScale, maximumScale, typeName);
                break;
            case ExasolConstants.TYPE_VARCHAR:
                tempTypeDesc = new TypeDesc(DBPDataKind.STRING, Types.VARCHAR, precision, minimumScale, maximumScale, typeName);
                break;
            case "LONG VARCHAR":
                tempTypeDesc = new TypeDesc(DBPDataKind.STRING, Types.LONGNVARCHAR, precision, minimumScale, maximumScale, typeName);
                break;
            case "DATE":
                tempTypeDesc = new TypeDesc(DBPDataKind.DATETIME, Types.DATE, precision, minimumScale, maximumScale, typeName);
                break;
            case "TIMESTAMP":
                tempTypeDesc = new TypeDesc(DBPDataKind.DATETIME, Types.TIMESTAMP, precision, minimumScale, maximumScale, typeName);
                break;
            case "TIMESTAMP WITH LOCAL TIME ZONE":
                tempTypeDesc = new TypeDesc(DBPDataKind.DATETIME, Types.TIMESTAMP_WITH_TIMEZONE, precision, minimumScale, maximumScale, typeName);
                break;
            case ExasolConstants.TYPE_HASHTYPE:
                tempTypeDesc = new TypeDesc(DBPDataKind.STRING, Types.BINARY, precision, minimumScale, maximumScale, typeName);
                break;
            default:
                LOG.error("DataType '" + name + "' is unknown to DBeaver");
        }

        this.typeDesc = tempTypeDesc;
    }

    @Override
    public DBSObject getParentObject() {
        return parentNode;
    }

    @Override
    public String getTypeName() {
        return name;
    }

    @Override
    public String getFullTypeName() {
        return DBUtils.getFullTypeName(this);
    }


    public int getEquivalentSqlType() {
        return typeDesc.sqlType;
    }

    @Override
    public Integer getPrecision() {
        if (typeDesc.precision != null) {
            return typeDesc.precision;
        } else {
            return 0;
        }
    }


    @Nullable
    @Override
    public DBSDataType getComponentType(@NotNull DBRProgressMonitor monitor) throws DBException {
        return null;
    }

    @Override
    public int getMinScale() {
        if (typeDesc.minScale != null) {
            return typeDesc.minScale;
        } else {
            return 0;
        }
    }

    @Override
    public int getMaxScale() {
        if (typeDesc.maxScale != null) {
            return typeDesc.maxScale;
        } else {
            return 0;
        }
    }

    @NotNull
    @Override
    public DBCLogicalOperator[] getSupportedOperators(DBSTypedObject attribute) {
        return DBUtils.getDefaultOperators(this);
    }

    // -----------------
    // Properties
    // -----------------

    @NotNull
    @Override
    @Property(viewable = true, editable = false, valueTransformer = DBObjectNameCaseTransformer.class, order = 1)
    public String getName() {
        return name;
    }

    @Property(viewable = true, editable = false, order = 2)
    public ExasolSchema getSchema() {
        return exasolSchema;
    }


    @Override
    @Property(viewable = true, editable = false, order = 4)
    public DBPDataKind getDataKind() {
    	if (typeDesc == null)
    	{
    		return DBPDataKind.UNKNOWN;
    	} else {
    		return typeDesc.dataKind;
    	}
    }


    @Override
    @Property(viewable = true, editable = false, order = 5)
    public long getMaxLength() {
        return length;
    }

    @Override
    public long getTypeModifiers() {
        return 0;
    }

    @Override
    @Property(viewable = true, editable = false, order = 6)
    public Integer getScale() {
        return scale;
    }

    @Override
    @Property(viewable = false, editable = false, order = 10)
    public int getTypeID() {
        return typeDesc.sqlType;
    }

    @Property(viewable = false, editable = false, order = 11)
    public long getExasolTypeId() {
        return exasolTypeId;
    }

    @Nullable
    @Override
    @Property(viewable = false, editable = false, length = PropertyLength.MULTILINE)
    public String getDescription() {
        return null;
    }


    // --------------
    // Helper Objects
    // --------------
    private static final class TypeDesc {
        private final DBPDataKind dataKind;
        private final Integer sqlType;
        private final Integer precision;
        private final Integer minScale;
        private final Integer maxScale;
        @SuppressWarnings("unused")
		private final String name;

        private TypeDesc(DBPDataKind dataKind, Integer sqlType, Integer precision, Integer minScale, Integer maxScale, String name) {
            this.name = name;
            this.dataKind = dataKind;
            this.sqlType = sqlType;
            this.precision = precision;
            this.minScale = minScale;
            this.maxScale = maxScale;
        }
    }


    @Override
    public boolean isPersisted() {
        return true;
    }


    @Override
    public Object geTypeExtension() {
        // TODO Auto-generated method stub
        return null;
    }

    @Override
    public String getFullyQualifiedName(DBPEvaluationContext context) {
        return name;
    }


}
