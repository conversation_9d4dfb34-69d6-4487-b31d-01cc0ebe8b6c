
package com.dc.summer.ext.exasol.model;

import com.dc.summer.model.impl.jdbc.JDBCUtils;
import com.dc.summer.model.meta.Property;
import com.dc.summer.model.struct.DBSObject;

import java.sql.ResultSet;

public class ExasolVirtualSchemaParameter implements DBSObject {
	
	private String name;
	private String value;
	private ExasolVirtualSchema schema;
	private Boolean isPersisted;

	public ExasolVirtualSchemaParameter(ExasolVirtualSchema schema, ResultSet dbResult)
	{
		this.schema = schema;
		this.name = JDBCUtils.safeGetString(dbResult, "PROPERTY_NAME");
		this.value = JDBCUtils.safeGetString(dbResult, "PROPERTY_VALUE");
		isPersisted = true;
	}

	@Override
	@Property(viewable = true, order = 10)
	public String getName()
	{
		return name;
	}
	
	@Property(viewable = true, order = 20)
	public String getValue()
	{
		return value;
	}

	@Override
	public boolean isPersisted()
	{
		return isPersisted;
	}

	@Override
	public String getDescription()
	{
		return null;
	}

	@Override
	public ExasolVirtualSchema getParentObject()
	{
		return schema;
	}

	@Override
	public ExasolDataSource getDataSource()
	{
		return schema.getDataSource();
	}

}
