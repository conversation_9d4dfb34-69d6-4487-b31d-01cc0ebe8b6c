package com.dc.summer.ext.dm.tree;

public interface ITreeNode {
   ITreeNode fetchParent();

   void setParent(ITreeNode var1);

   ITreeNode[] getChildren();

   ITreeNode[] getChildren(boolean var1);

   int getChildCount();

   void addChild(ITreeNode var1);

   void removeChild(ITreeNode var1);

   void removeChildren();

   boolean hasChildren();

   void reload();

   public Object getPlanName();

   public void setPlanName(Object planName);

   String getText();

   String getToolTip();

   String getCollapseImage();

   String getExpandImage();

   void setTree(Object var1);

   Object getTree();

   void setData(Object var1, Object var2);

   Object getData(Object var1);

   boolean getToolTipVisible();
}
