package com.dc.summer.ext.dm.model;

import java.util.ArrayList;
import java.util.List;

import com.dc.code.NotNull;
import com.dc.summer.Log;
import com.dc.summer.model.impl.DBObjectNameCaseTransformer;
import com.dc.summer.model.impl.jdbc.struct.JDBCTableConstraint;
import com.dc.summer.model.meta.Property;
import com.dc.summer.model.runtime.DBRProgressMonitor;
import com.dc.summer.model.struct.DBSEntityConstraint;
import com.dc.summer.model.struct.DBSEntityConstraintType;

/**
 * DM Table Constraint Base
 * 
 * <AUTHOR>
 *
 */
public abstract class DmTableConstraintBase extends JDBCTableConstraint<DmTableBase> {

	private static final Log log = Log.getLog(DmTableConstraintBase.class);

	private DmObjectStatus status;
	private List<DmTableConstraintColumn> columns;

	public DmTableConstraintBase(DmTableBase dmTable, String name, DBSEntityConstraintType constraintType,
			DmObjectStatus status, boolean persisted) {
		super(dmTable, name, null, constraintType, persisted);
		this.status = status;
	}

	protected DmTableConstraintBase(DmTableBase dmTable, String name, String description,
			DBSEntityConstraintType constraintType, boolean persisted) {
		super(dmTable, name, description, constraintType, persisted);
	}

	@NotNull
	@Override
	public DmDataSource getDataSource() {
		return getTable().getDataSource();
	}

	@NotNull
	@Property(viewable = true, editable = false, valueTransformer = DBObjectNameCaseTransformer.class, order = 3)
	@Override
	public DBSEntityConstraintType getConstraintType() {
		return constraintType;
	}

	@Property(viewable = true, editable = false, order = 9)
	public DmObjectStatus getStatus() {
		return status;
	}

	@Override
	public List<DmTableConstraintColumn> getAttributeReferences(DBRProgressMonitor monitor) {
		return columns;
	}

	public void addColumn(DmTableConstraintColumn column) {
		if (columns == null) {
			columns = new ArrayList<>();
		}
		this.columns.add(column);
	}

	void setColumns(List<DmTableConstraintColumn> columns) {
		this.columns = columns;
	}
}
