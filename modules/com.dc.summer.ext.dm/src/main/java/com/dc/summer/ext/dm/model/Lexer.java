package com.dc.summer.ext.dm.model;

import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.Reader;

public class Lexer {
   public static final int YYEOF = -1;
   private static final int ZZ_BUFFERSIZE = 16384;
   public static final int xdq = 6;
   public static final int xhint = 12;
   public static final int YYINITIAL = 0;
   public static final int xq = 4;
   public static final int xbin = 8;
   public static final int xc = 2;
   public static final int xhex = 10;
   private static final int[] ZZ_LEXSTATE = new int[]{0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6};
   private static final String ZZ_CMAP_PACKED = "\t\u0000\u0001\u0013\u0001\u0012\u0001\u0000\u0001\u0013\u0001\u0012\u0002\u0000\u0001\u0000\u000f\u0000\u0001\u0013\u0001\t\u0001\u0002\u0002\u0000\u0002\t\u0001\u0001\u0002\t\u0001\u0004\u0001\u0010\u0001\t\u0001\u0014\u0001\f\u0001\u0003\u0001\r\u0006\u0007\u0001\b\u0002\u0007\u0001\n\u0001\t\u0001\t\u0001\u000b\u0003\t\u0001\u000e\u0001\u0005\u0001\u000e\u0001\u0011\u0001\u000f\u0001\u0011\u0011\u0000\u0001\u0006\u0002\u0000\u0001\t\u0001\u0000\u0001\t\u0001\t\u0001\u0000\u0001\u0000\u0001\u000e\u0001\u0005\u0001\u000e\u0001\u0011\u0001\u000f\u0001\u0011\u0011\u0000\u0001\u0006\u0002\u0000\u0001\t\u0001\t\u0002\tﾁ\u0000";
   private static final char[] ZZ_CMAP = zzUnpackCMap("\t\u0000\u0001\u0013\u0001\u0012\u0001\u0000\u0001\u0013\u0001\u0012\u0002\u0000\u0001\u0000\u000f\u0000\u0001\u0013\u0001\t\u0001\u0002\u0002\u0000\u0002\t\u0001\u0001\u0002\t\u0001\u0004\u0001\u0010\u0001\t\u0001\u0014\u0001\f\u0001\u0003\u0001\r\u0006\u0007\u0001\b\u0002\u0007\u0001\n\u0001\t\u0001\t\u0001\u000b\u0003\t\u0001\u000e\u0001\u0005\u0001\u000e\u0001\u0011\u0001\u000f\u0001\u0011\u0011\u0000\u0001\u0006\u0002\u0000\u0001\t\u0001\u0000\u0001\t\u0001\t\u0001\u0000\u0001\u0000\u0001\u000e\u0001\u0005\u0001\u000e\u0001\u0011\u0001\u000f\u0001\u0011\u0011\u0000\u0001\u0006\u0002\u0000\u0001\t\u0001\t\u0002\tﾁ\u0000");
   private static final int[] ZZ_ACTION = zzUnpackAction();
   private static final String ZZ_ACTION_PACKED_0 = "\u0007\u0000\u0001\u0001\u0001\u0002\u0001\u0003\u0002\u0004\u0002\u0001\u0002\u0005\u0002\u0004\u0001\u0005\u0001\u0006\u0001\u0004\u0001\u0007\u0002\b\u0001\t\u0001\n\u0001\u000b\u0001\f\u0001\r\u0001\u000e\u0001\u000f\u0001\u0010\u0001\u0011\u0001\u0012\u0001\u0013\u0001\u0014\u0001\u0015\u0001\u0000\u0001\u0016\u0001\u0001\u0001\u0016\u0001\u0017\u0001\u0018\u0001\u0015\u0001\u0019\u0001\u0000\u0001\u001a\u0001\u001b\u0001\u0000\u0001\u001c\u0002\u0000\u0001\u001d\u0004\u0000\u0001\u001e\u0001\u0016\u0001\u0000\u0001\u0016\u0001\u001f\u0001 \u0001!\u0001\"";
   private static final int[] ZZ_ROWMAP = zzUnpackRowMap();
   private static final String ZZ_ROWMAP_PACKED_0 = "\u0000\u0000\u0000\u0015\u0000*\u0000?\u0000T\u0000i\u0000~\u0000\u0093\u0000~\u0000~\u0000¨\u0000~\u0000½\u0000Ò\u0000ç\u0000ü\u0000đ\u0000Ħ\u0000Ļ\u0000Ő\u0000ť\u0000ź\u0000Ə\u0000Ƥ\u0000ƹ\u0000ǎ\u0000ǣ\u0000Ǹ\u0000ȍ\u0000Ȣ\u0000ȷ\u0000Ɍ\u0000ɡ\u0000~\u0000~\u0000~\u0000ɶ\u0000ʋ\u0000~\u0000ʠ\u0000\u0093\u0000~\u0000~\u0000ʵ\u0000~\u0000ˊ\u0000~\u0000~\u0000Ƥ\u0000~\u0000˟\u0000˴\u0000~\u0000̉\u0000Ȣ\u0000̞\u0000Ɍ\u0000~\u0000̳\u0000̳\u0000͈\u0000ˊ\u0000~\u0000~\u0000~";
   private static final int[] ZZ_TRANS = zzUnpackTrans();
   private static final String ZZ_TRANS_PACKED_0 = "\u0001\b\u0001\t\u0001\n\u0001\u000b\u0001\f\u0001\r\u0001\u000e\u0001\u000f\u0001\u0010\u0001\f\u0001\u0011\u0001\f\u0001\u0012\u0001\u0013\u0002\b\u0001\f\u0001\b\u0002\u0014\u0001\u0015\u0003\u0016\u0001\u0017\u0001\u0018\u0010\u0016\u0001\u0019\u0001\u001a\u0013\u0019\u0002\u001b\u0001\u001c\u0012\u001b\u0001\u001d\u0001\u001e\u0013\u001d\u0001\u001f\u0001 \u0013\u001f\u0015\u0000\u0001\b\u0004\u0000\u0004\b\u0004\u0000\u0003\b\u0001\u0000\u0001\b\u0006\u0000\u0001!\u0001\"\u0010\u0000\u0001\b\u0001#\u0003\u0000\u0004\b\u0004\u0000\u0003\b\u0001\u0000\u0001\b\u0003\u0000\u0001\b\u0001$\u0003\u0000\u0004\b\u0004\u0000\u0003\b\u0001\u0000\u0001\b\n\u0000\u0002\u000f\u0003\u0000\u0001%\u0001\u000f\u0001\u0000\u0001&\u0001\u0000\u0001'\u0003\u0000\u0001\b\u0004\u0000\u0002\b\u0002\u0010\u0003\u0000\u0001%\u0001\u0010\u0001\b\u0001(\u0001\u0000\u0001)\u000e\u0000\u0001*\r\u0000\u0001+\u0002\u0000\u0002,\u0003\u0000\u0001-\u0001,\r\u0000\u0001.\u0002\u000f\u0003\u0000\u0001%\u0001\u000f\u0001\u0000\u0001&\u0001\u0000\u0001'\u0015\u0000\u0002\u0014\u0015\u0000\u0001!\u0003\u0016\u0002\u0000\u0010\u0016\u0004\u0000\u0001/\u0013\u0000\u00010\u00011\u0010\u0000\u0001\u0019\u0001\u0000\u0013\u0019\u0001\u0000\u00012\u0010\u0000\u00013\u00014\u0001\u0000\u0002\u001b\u0001\u0000\u0012\u001b\u0002\u0000\u00015\u0012\u0000\u0001\u001d\u0001\u0000\u0013\u001d\u0012\u0000\u00016\u00017\u0001\u0000\u0001\u001f\u0001\u0000\u0013\u001f\u0012\u0000\u00018\u00019\u0001\u0000\u0012!\u0001\u0000\u0002!\u0007\u0000\u0002,\u0003\u0000\u0001:\u0001,\u0001\u0000\u0001&\u0001\u0000\u0001'\n\u0000\u0002;\u0004\u0000\u0001;\u0002\u0000\u0001<\u0003\u0000\u0001<\u0001\b\u0004\u0000\u0002\b\u0002=\u0004\u0000\u0001=\u0002\b\u0001<\u0001\b\u0002\u0000\u0001<\u0007\u0000\u0002,\u0004\u0000\u0001,\u0001\u0000\u0001&\u0001\u0000\u0001'\b\u0000\u0001>\u0001\u0000\u0002>\u0004\u0000\u0003>\u0001\u0000\u0001>\u0004\u0000\u0001?\u0010\u0000\u00023\u0013\u0000\u00013\u00014\u0002\u0000\u0001@\u0010\u0000\u00026\u0002\u0000\u0001A\u0010\u0000\u00028\b\u0000\u0002;\u0004\u0000\u0001;\u0007\u0000\u0001\b\u0004\u0000\u0002\b\u0002=\u0004\u0000\u0001=\u0002\b\u0001\u0000\u0001\b\u0003\u0000";
   private static final int ZZ_UNKNOWN_ERROR = 0;
   private static final int ZZ_NO_MATCH = 1;
   private static final int ZZ_PUSHBACK_2BIG = 2;
   private static final String[] ZZ_ERROR_MSG = new String[]{"Unkown internal scanner error", "Error: could not match input", "Error: pushback value was too large"};
   private static final int[] ZZ_ATTRIBUTE = zzUnpackAttribute();
   private static final String ZZ_ATTRIBUTE_PACKED_0 = "\u0006\u0000\u0001\b\u0001\u0001\u0002\t\u0001\u0001\u0001\t\u0015\u0001\u0003\t\u0001\u0001\u0001\u0000\u0001\t\u0002\u0001\u0002\t\u0001\u0001\u0001\t\u0001\u0000\u0002\t\u0001\u0000\u0001\t\u0002\u0000\u0001\t\u0004\u0000\u0001\t\u0001\u0001\u0001\u0000\u0002\u0001\u0003\t";
   private Reader zzReader;
   private int zzState;
   private int zzLexicalState;
   private char[] zzBuffer;
   private int zzMarkedPos;
   private int zzCurrentPos;
   private int zzStartRead;
   private int zzEndRead;
   private int yyline;
   private int yychar;
   private int yycolumn;
   private boolean zzAtBOL;
   private boolean zzAtEOF;
   private boolean zzEOFDone;
   public int start;
   public String ltstr;
   public boolean debug;
   private boolean keepQuot;

   private static int[] zzUnpackAction() {
      int[] result = new int[65];
      int offset = 0;
      zzUnpackAction("\u0007\u0000\u0001\u0001\u0001\u0002\u0001\u0003\u0002\u0004\u0002\u0001\u0002\u0005\u0002\u0004\u0001\u0005\u0001\u0006\u0001\u0004\u0001\u0007\u0002\b\u0001\t\u0001\n\u0001\u000b\u0001\f\u0001\r\u0001\u000e\u0001\u000f\u0001\u0010\u0001\u0011\u0001\u0012\u0001\u0013\u0001\u0014\u0001\u0015\u0001\u0000\u0001\u0016\u0001\u0001\u0001\u0016\u0001\u0017\u0001\u0018\u0001\u0015\u0001\u0019\u0001\u0000\u0001\u001a\u0001\u001b\u0001\u0000\u0001\u001c\u0002\u0000\u0001\u001d\u0004\u0000\u0001\u001e\u0001\u0016\u0001\u0000\u0001\u0016\u0001\u001f\u0001 \u0001!\u0001\"", offset, result);
      return result;
   }

   private static int zzUnpackAction(String packed, int offset, int[] result) {
      int i = 0;
      int j = offset;
      int l = packed.length();

      while(i < l) {
         int count = packed.charAt(i++);
         int value = packed.charAt(i++);

         while(true) {
            result[j++] = value;
            --count;
            if (count <= 0) {
               break;
            }
         }
      }

      return j;
   }

   private static int[] zzUnpackRowMap() {
      int[] result = new int[65];
      int offset = 0;
      zzUnpackRowMap("\u0000\u0000\u0000\u0015\u0000*\u0000?\u0000T\u0000i\u0000~\u0000\u0093\u0000~\u0000~\u0000¨\u0000~\u0000½\u0000Ò\u0000ç\u0000ü\u0000đ\u0000Ħ\u0000Ļ\u0000Ő\u0000ť\u0000ź\u0000Ə\u0000Ƥ\u0000ƹ\u0000ǎ\u0000ǣ\u0000Ǹ\u0000ȍ\u0000Ȣ\u0000ȷ\u0000Ɍ\u0000ɡ\u0000~\u0000~\u0000~\u0000ɶ\u0000ʋ\u0000~\u0000ʠ\u0000\u0093\u0000~\u0000~\u0000ʵ\u0000~\u0000ˊ\u0000~\u0000~\u0000Ƥ\u0000~\u0000˟\u0000˴\u0000~\u0000̉\u0000Ȣ\u0000̞\u0000Ɍ\u0000~\u0000̳\u0000̳\u0000͈\u0000ˊ\u0000~\u0000~\u0000~", offset, result);
      return result;
   }

   private static int zzUnpackRowMap(String packed, int offset, int[] result) {
      int i = 0;
      int j = offset;

      int high;
      for(int l = packed.length(); i < l; result[j++] = high | packed.charAt(i++)) {
         high = packed.charAt(i++) << 16;
      }

      return j;
   }

   private static int[] zzUnpackTrans() {
      int[] result = new int[861];
      int offset = 0;
      zzUnpackTrans("\u0001\b\u0001\t\u0001\n\u0001\u000b\u0001\f\u0001\r\u0001\u000e\u0001\u000f\u0001\u0010\u0001\f\u0001\u0011\u0001\f\u0001\u0012\u0001\u0013\u0002\b\u0001\f\u0001\b\u0002\u0014\u0001\u0015\u0003\u0016\u0001\u0017\u0001\u0018\u0010\u0016\u0001\u0019\u0001\u001a\u0013\u0019\u0002\u001b\u0001\u001c\u0012\u001b\u0001\u001d\u0001\u001e\u0013\u001d\u0001\u001f\u0001 \u0013\u001f\u0015\u0000\u0001\b\u0004\u0000\u0004\b\u0004\u0000\u0003\b\u0001\u0000\u0001\b\u0006\u0000\u0001!\u0001\"\u0010\u0000\u0001\b\u0001#\u0003\u0000\u0004\b\u0004\u0000\u0003\b\u0001\u0000\u0001\b\u0003\u0000\u0001\b\u0001$\u0003\u0000\u0004\b\u0004\u0000\u0003\b\u0001\u0000\u0001\b\n\u0000\u0002\u000f\u0003\u0000\u0001%\u0001\u000f\u0001\u0000\u0001&\u0001\u0000\u0001'\u0003\u0000\u0001\b\u0004\u0000\u0002\b\u0002\u0010\u0003\u0000\u0001%\u0001\u0010\u0001\b\u0001(\u0001\u0000\u0001)\u000e\u0000\u0001*\r\u0000\u0001+\u0002\u0000\u0002,\u0003\u0000\u0001-\u0001,\r\u0000\u0001.\u0002\u000f\u0003\u0000\u0001%\u0001\u000f\u0001\u0000\u0001&\u0001\u0000\u0001'\u0015\u0000\u0002\u0014\u0015\u0000\u0001!\u0003\u0016\u0002\u0000\u0010\u0016\u0004\u0000\u0001/\u0013\u0000\u00010\u00011\u0010\u0000\u0001\u0019\u0001\u0000\u0013\u0019\u0001\u0000\u00012\u0010\u0000\u00013\u00014\u0001\u0000\u0002\u001b\u0001\u0000\u0012\u001b\u0002\u0000\u00015\u0012\u0000\u0001\u001d\u0001\u0000\u0013\u001d\u0012\u0000\u00016\u00017\u0001\u0000\u0001\u001f\u0001\u0000\u0013\u001f\u0012\u0000\u00018\u00019\u0001\u0000\u0012!\u0001\u0000\u0002!\u0007\u0000\u0002,\u0003\u0000\u0001:\u0001,\u0001\u0000\u0001&\u0001\u0000\u0001'\n\u0000\u0002;\u0004\u0000\u0001;\u0002\u0000\u0001<\u0003\u0000\u0001<\u0001\b\u0004\u0000\u0002\b\u0002=\u0004\u0000\u0001=\u0002\b\u0001<\u0001\b\u0002\u0000\u0001<\u0007\u0000\u0002,\u0004\u0000\u0001,\u0001\u0000\u0001&\u0001\u0000\u0001'\b\u0000\u0001>\u0001\u0000\u0002>\u0004\u0000\u0003>\u0001\u0000\u0001>\u0004\u0000\u0001?\u0010\u0000\u00023\u0013\u0000\u00013\u00014\u0002\u0000\u0001@\u0010\u0000\u00026\u0002\u0000\u0001A\u0010\u0000\u00028\b\u0000\u0002;\u0004\u0000\u0001;\u0007\u0000\u0001\b\u0004\u0000\u0002\b\u0002=\u0004\u0000\u0001=\u0002\b\u0001\u0000\u0001\b\u0003\u0000", offset, result);
      return result;
   }

   private static int zzUnpackTrans(String packed, int offset, int[] result) {
      int i = 0;
      int j = offset;
      int l = packed.length();

      while(i < l) {
         int count = packed.charAt(i++);
         int value = packed.charAt(i++);
         --value;

         while(true) {
            result[j++] = value;
            --count;
            if (count <= 0) {
               break;
            }
         }
      }

      return j;
   }

   private static int[] zzUnpackAttribute() {
      int[] result = new int[65];
      int offset = 0;
      zzUnpackAttribute("\u0006\u0000\u0001\b\u0001\u0001\u0002\t\u0001\u0001\u0001\t\u0015\u0001\u0003\t\u0001\u0001\u0001\u0000\u0001\t\u0002\u0001\u0002\t\u0001\u0001\u0001\t\u0001\u0000\u0002\t\u0001\u0000\u0001\t\u0002\u0000\u0001\t\u0004\u0000\u0001\t\u0001\u0001\u0001\u0000\u0002\u0001\u0003\t", offset, result);
      return result;
   }

   private static int zzUnpackAttribute(String packed, int offset, int[] result) {
      int i = 0;
      int j = offset;
      int l = packed.length();

      while(i < l) {
         int count = packed.charAt(i++);
         int value = packed.charAt(i++);

         while(true) {
            result[j++] = value;
            --count;
            if (count <= 0) {
               break;
            }
         }
      }

      return j;
   }

   private void debug(String info) {
      if (this.debug) {
         System.out.println("[DEBUG] - " + info);
      }
   }

   public void yyerror(String msg) {
      String locInfo = "(line: " + this.yyline + ", column: " + this.yycolumn + ", char: " + this.yychar + ")";
      if (msg != null && msg.length() != 0) {
         throw new RuntimeException("syntex error" + locInfo + ": " + msg);
      } else {
         throw new RuntimeException("syntex error" + locInfo);
      }
   }

   public LVal.Position yyposition() {
      return new LVal.Position(this.yychar, this.yyline, this.yycolumn, this.yychar);
   }

   public LVal.Position yyposition(int start) {
      return new LVal.Position(this.yychar, this.yyline, this.yycolumn, start);
   }

   public Lexer(Reader in, boolean keepQuot) {
      this.zzLexicalState = 0;
      this.zzBuffer = new char[16384];
      this.zzAtBOL = true;
      this.keepQuot = keepQuot;
      this.zzReader = in;
   }

   public Lexer(InputStream in, boolean keepQuot) {
      this((Reader)(new InputStreamReader(in)), keepQuot);
   }

   private static char[] zzUnpackCMap(String packed) {
      char[] map = new char[65536];
      int i = 0;
      int j = 0;

      while(i < 118) {
         int count = packed.charAt(i++);
         char value = packed.charAt(i++);

         while(true) {
            map[j++] = value;
            --count;
            if (count <= 0) {
               break;
            }
         }
      }

      return map;
   }

   private boolean zzRefill() throws IOException {
      if (this.zzStartRead > 0) {
         System.arraycopy(this.zzBuffer, this.zzStartRead, this.zzBuffer, 0, this.zzEndRead - this.zzStartRead);
         this.zzEndRead -= this.zzStartRead;
         this.zzCurrentPos -= this.zzStartRead;
         this.zzMarkedPos -= this.zzStartRead;
         this.zzStartRead = 0;
      }

      if (this.zzCurrentPos >= this.zzBuffer.length) {
         char[] newBuffer = new char[this.zzCurrentPos * 2];
         System.arraycopy(this.zzBuffer, 0, newBuffer, 0, this.zzBuffer.length);
         this.zzBuffer = newBuffer;
      }

      int numRead = this.zzReader.read(this.zzBuffer, this.zzEndRead, this.zzBuffer.length - this.zzEndRead);
      if (numRead > 0) {
         this.zzEndRead += numRead;
         return false;
      } else if (numRead == 0) {
         int c = this.zzReader.read();
         if (c == -1) {
            return true;
         } else {
            this.zzBuffer[this.zzEndRead++] = (char)c;
            return false;
         }
      } else {
         return true;
      }
   }

   public final void yyclose() throws IOException {
      this.zzAtEOF = true;
      this.zzEndRead = this.zzStartRead;
      if (this.zzReader != null) {
         this.zzReader.close();
      }

   }

   public final void yyreset(Reader reader) {
      this.zzReader = reader;
      this.zzAtBOL = true;
      this.zzAtEOF = false;
      this.zzEOFDone = false;
      this.zzEndRead = this.zzStartRead = 0;
      this.zzCurrentPos = this.zzMarkedPos = 0;
      this.yyline = this.yychar = this.yycolumn = 0;
      this.zzLexicalState = 0;
   }

   public final int yystate() {
      return this.zzLexicalState;
   }

   public final void yybegin(int newState) {
      this.zzLexicalState = newState;
   }

   public final String yytext() {
      return new String(this.zzBuffer, this.zzStartRead, this.zzMarkedPos - this.zzStartRead);
   }

   public final char yycharat(int pos) {
      return this.zzBuffer[this.zzStartRead + pos];
   }

   public final int yylength() {
      return this.zzMarkedPos - this.zzStartRead;
   }

   private void zzScanError(int errorCode) {
      String message;
      try {
         message = ZZ_ERROR_MSG[errorCode];
      } catch (ArrayIndexOutOfBoundsException var3) {
         message = ZZ_ERROR_MSG[0];
      }

      throw new Error(message);
   }

   public void yypushback(int number) {
      if (number > this.yylength()) {
         this.zzScanError(2);
      }

      this.zzMarkedPos -= number;
   }

   public LVal yylex() throws IOException {
      int zzEndReadL = this.zzEndRead;
      char[] zzBufferL = this.zzBuffer;
      char[] zzCMapL = ZZ_CMAP;
      int[] zzTransL = ZZ_TRANS;
      int[] zzRowMapL = ZZ_ROWMAP;
      int[] zzAttrL = ZZ_ATTRIBUTE;

      while(true) {
         int zzMarkedPosL = this.zzMarkedPos;
         this.yychar += zzMarkedPosL - this.zzStartRead;
         boolean zzR = false;

         int zzCurrentPosL;
         for(zzCurrentPosL = this.zzStartRead; zzCurrentPosL < zzMarkedPosL; ++zzCurrentPosL) {
            switch (zzBufferL[zzCurrentPosL]) {
               case '\n':
                  if (zzR) {
                     zzR = false;
                  } else {
                     ++this.yyline;
                     this.yycolumn = 0;
                  }
                  break;
               case '\u000b':
               case '\f':
               case '\u0085':
               case '\u2028':
               case '\u2029':
                  ++this.yyline;
                  this.yycolumn = 0;
                  zzR = false;
                  break;
               case '\r':
                  ++this.yyline;
                  this.yycolumn = 0;
                  zzR = true;
                  break;
               default:
                  zzR = false;
                  ++this.yycolumn;
            }
         }

         boolean eof;
         if (zzR) {
            if (zzMarkedPosL < zzEndReadL) {
               eof = zzBufferL[zzMarkedPosL] == '\n';
            } else if (this.zzAtEOF) {
               eof = false;
            } else {
               eof = this.zzRefill();
               zzEndReadL = this.zzEndRead;
               zzMarkedPosL = this.zzMarkedPos;
               zzBufferL = this.zzBuffer;
               if (eof) {
                  eof = false;
               } else {
                  eof = zzBufferL[zzMarkedPosL] == '\n';
               }
            }

            if (eof) {
               --this.yyline;
            }
         }

         int zzAction = -1;
         zzCurrentPosL = this.zzCurrentPos = this.zzStartRead = zzMarkedPosL;
         this.zzState = ZZ_LEXSTATE[this.zzLexicalState];

         int zzInput;
         while(true) {
            if (zzCurrentPosL < zzEndReadL) {
               zzInput = zzBufferL[zzCurrentPosL++];
            } else {
               if (this.zzAtEOF) {
                  zzInput = -1;
                  break;
               }

               this.zzCurrentPos = zzCurrentPosL;
               this.zzMarkedPos = zzMarkedPosL;
               eof = this.zzRefill();
               zzCurrentPosL = this.zzCurrentPos;
               zzMarkedPosL = this.zzMarkedPos;
               zzBufferL = this.zzBuffer;
               zzEndReadL = this.zzEndRead;
               if (eof) {
                  zzInput = -1;
                  break;
               }

               zzInput = zzBufferL[zzCurrentPosL++];
            }

            int zzNext = zzTransL[zzRowMapL[this.zzState] + zzCMapL[zzInput]];
            if (zzNext == -1) {
               break;
            }

            this.zzState = zzNext;
            int zzAttributes = zzAttrL[this.zzState];
            if ((zzAttributes & 1) == 1) {
               zzAction = this.zzState;
               zzMarkedPosL = zzCurrentPosL;
               if ((zzAttributes & 8) == 8) {
                  break;
               }
            }
         }

         this.zzMarkedPos = zzMarkedPosL;
         switch (zzAction < 0 ? zzAction : ZZ_ACTION[zzAction]) {
            case 1:
               this.debug("{identifier}");
               return new LVal(this.yytext(), LVal.Type.LITERAL, this.yyposition());
            case 2:
               this.debug("{xq_start}");
               this.yybegin(4);
               this.start = this.yychar;
               this.ltstr = this.keepQuot ? "'" : "";
               break;
            case 3:
               this.debug("{xdq_start}");
               this.yybegin(6);
               this.start = this.yychar;
               this.ltstr = "";
               this.ltstr = this.ltstr + this.yytext();
               break;
            case 4:
               this.debug("{self} | {op_chars}");
               return new LVal(this.yytext(), LVal.Type.LITERAL, this.yyposition());
            case 5:
               this.debug("{numeric}");
               return new LVal(this.yytext(), LVal.Type.NUMERIC, this.yyposition());
            case 6:
               this.debug("{comment} | {c_line_comment}");
               return new LVal(this.yytext(), LVal.Type.SPACE, this.yyposition());
            case 7:
               this.debug("<xc>{xc_inside}");
               this.ltstr = this.ltstr + this.yytext();
               break;
            case 8:
               this.debug("<xc>[\\/] | <xc>[\\*]");
               this.ltstr = this.ltstr + this.yytext();
               break;
            case 9:
               this.debug("<xq>{xq_inside}");
               this.ltstr = this.ltstr + this.yytext();
               break;
            case 10:
               this.debug("<xq>{xq_stop}");
               this.yybegin(0);
               this.ltstr = this.ltstr + (this.keepQuot ? "'" : "");
               return new LVal(this.ltstr, LVal.Type.STRING, this.yyposition(this.start));
            case 11:
               this.debug("<xdq>{xdq_inside}");
               this.ltstr = this.ltstr + this.yytext();
               break;
            case 12:
               this.debug("<xdq>{xdq_stop}");
               this.yybegin(0);
               this.ltstr = this.ltstr + this.yytext();
               return new LVal(this.ltstr, LVal.Type.LITERAL, this.yyposition(this.start));
            case 13:
               this.debug("<xbin>{xbin_inside}");
               this.ltstr = this.ltstr + this.yytext();
               break;
            case 14:
               this.debug("<xbin>{xbin_stop}");
               this.yybegin(0);
               this.ltstr = this.ltstr + this.yytext();
               return new LVal(this.ltstr, LVal.Type.BIN_STR, this.yyposition(this.start));
            case 15:
               this.debug("<xhex>{xhex_inside}");
               this.ltstr = this.ltstr + this.yytext();
               break;
            case 16:
               this.debug("<xhex>{xhex_stop}");
               this.yybegin(0);
               this.ltstr = this.ltstr + this.yytext();
               return new LVal(this.ltstr, LVal.Type.HEX_STR, this.yyposition(this.start));
            case 17:
               this.debug("{comment} | {c_line_comment}");
               return new LVal(this.yytext(), LVal.Type.COMMENT, this.yyposition());
            case 18:
               this.debug("{xc_start}");
               this.yybegin(2);
               this.start = this.yychar;
               this.ltstr = this.yytext();
               break;
            case 19:
               this.debug("{xbin_start}");
               this.yybegin(8);
               this.start = this.yychar;
               this.ltstr = "";
               this.ltstr = this.ltstr + this.yytext();
               break;
            case 20:
               this.debug("{xhex_start}");
               this.yybegin(10);
               this.start = this.yychar;
               this.ltstr = "";
               this.ltstr = this.ltstr + this.yytext();
               break;
            case 21:
               this.debug("{decimal}");
               return new LVal(this.yytext(), LVal.Type.DECIMAL, this.yyposition());
            case 22:
               this.debug("{real}");
               return new LVal(this.yytext(), LVal.Type.DECIMAL, this.yyposition());
            case 23:
               this.debug("{assign}");
               return new LVal(this.yytext(), LVal.Type.LITERAL, this.yyposition());
            case 24:
               this.debug("{selstar}");
               return new LVal(this.yytext(), LVal.Type.LITERAL, this.yyposition());
            case 25:
               this.debug("{boundary}");
               return new LVal(this.yytext(), LVal.Type.LITERAL, this.yyposition());
            case 26:
               this.debug("<xc>{xc_start}");
               this.ltstr = this.ltstr + this.yytext();
               break;
            case 27:
               this.debug("<xc>{xc_stop}");
               this.yybegin(0);
               this.ltstr = this.ltstr + this.yytext();
               return new LVal(this.ltstr, LVal.Type.COMMENT, this.yyposition(this.start));
            case 28:
               this.debug("<xq>{xq_double}");
               this.ltstr = this.ltstr + (this.keepQuot ? this.yytext() : "'");
               break;
            case 29:
               this.debug("<xdq>{xdq_double}");
               this.ltstr = this.ltstr + this.yytext();
               break;
            case 30:
               this.debug("{numeric_with_boundary}");
               return new LVal(this.yytext(), LVal.Type.LITERAL, this.yyposition());
            case 31:
               this.debug("{hex_numeric}");
               return new LVal(this.yytext(), LVal.Type.HEX_NUMERIC, this.yyposition());
            case 32:
               this.debug("<xq>{xq_cat}");
               break;
            case 33:
               this.debug("<xbin>{xbin_cat}");
               break;
            case 34:
               this.debug("<xhex>{xhex_cat}");
            case 35:
            case 36:
            case 37:
            case 38:
            case 39:
            case 40:
            case 41:
            case 42:
            case 43:
            case 44:
            case 45:
            case 46:
            case 47:
            case 48:
            case 49:
            case 50:
            case 51:
            case 52:
            case 53:
            case 54:
            case 55:
            case 56:
            case 57:
            case 58:
            case 59:
            case 60:
            case 61:
            case 62:
            case 63:
            case 64:
            case 65:
            case 66:
            case 67:
            case 68:
               break;
            default:
               if (zzInput == -1 && this.zzStartRead == this.zzCurrentPos) {
                  this.zzAtEOF = true;
                  switch (this.zzLexicalState) {
                     case 2:
                        this.debug("<xc><<EOF>>");
                        this.yybegin(0);
                        this.yyerror("unterminated /* comment");
                        break;
                     case 4:
                        this.debug("<xq><<EOF>>");
                        this.yybegin(0);
                        this.yyerror("unterminated quoted string");
                        break;
                     case 6:
                        this.debug("<xdq><<EOF>>");
                        this.yybegin(0);
                        this.yyerror("unterminated quoted identifier");
                        break;
                     case 8:
                        this.debug("<xbin><<EOF>>");
                        this.yybegin(0);
                        this.yyerror("unterminated binary string literal");
                        break;
                     case 10:
                        this.debug("<xhex><<EOF>>");
                        this.yybegin(0);
                        this.yyerror("unterminated hexadecimal integer");
                     case 66:
                     case 67:
                     case 68:
                     case 69:
                     case 70:
                        break;
                     default:
                        return null;
                  }
               } else {
                  this.zzScanError(1);
               }
         }
      }
   }
}
