package com.dc.summer.ext.dm.model;

import com.dc.code.NotNull;
import com.dc.code.Nullable;
import com.dc.summer.Log;
import com.dc.summer.model.DBPSaveableObject;
import com.dc.summer.model.meta.Property;
import com.dc.summer.model.struct.DBSObject;
import com.dc.utils.CommonUtils;

public abstract class DmObject<PARENT extends DBSObject> implements DBSObject, DBPSaveableObject {

	private static final Log log = Log.getLog(DmObject.class);

	protected final PARENT parent;
	protected String name;
	private boolean persisted;
	private long objectId;

	protected DmObject(PARENT parent, String name, long objectId, boolean persisted) {
		this.parent = parent;
		this.name = CommonUtils.notEmpty(name);
		this.objectId = objectId;
		this.persisted = persisted;
	}

	protected DmObject(PARENT parent, String name, boolean persisted) {
		this.parent = parent;
		this.name = name;
		this.persisted = persisted;
	}

	@Nullable
	@Override
	public String getDescription() {
		return null;
	}

	@Override
	public PARENT getParentObject() {
		return parent;
	}

	@NotNull
	@Override
	public DmDataSource getDataSource() {
		return (DmDataSource) parent.getDataSource();
	}

	@NotNull
	@Override
	@Property(viewable = true, editable = true, order = 1)
	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public long getObjectId() {
		return objectId;
	}

	@Override
	public boolean isPersisted() {
		return persisted;
	}

	@Override
	public void setPersisted(boolean persisted) {
		this.persisted = persisted;
	}
}
