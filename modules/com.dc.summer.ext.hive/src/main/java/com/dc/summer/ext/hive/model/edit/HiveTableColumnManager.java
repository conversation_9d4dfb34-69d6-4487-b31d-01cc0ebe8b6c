
package com.dc.summer.ext.hive.model.edit;

import com.dc.summer.Log;
import com.dc.summer.ext.generic.edit.GenericTableColumnManager;
import com.dc.summer.ext.generic.model.GenericTableBase;
import com.dc.summer.ext.hive.model.HiveTable;
import com.dc.summer.ext.hive.model.HiveTableColumn;
import com.dc.summer.model.DBPEvaluationContext;
import com.dc.summer.model.DBUtils;
import com.dc.summer.model.edit.DBEPersistAction;
import com.dc.summer.model.exec.DBCExecutionContext;
import com.dc.summer.model.impl.edit.DBECommandAbstract;
import com.dc.summer.model.impl.sql.edit.SQLObjectEditor;
import com.dc.summer.DBException;
import com.dc.summer.ext.generic.model.GenericTableColumn;
import com.dc.summer.model.impl.edit.SQLDatabasePersistAction;
import com.dc.summer.model.runtime.DBRProgressMonitor;
import com.dc.summer.model.sql.SQLUtils;

import java.util.List;
import java.util.Map;

/**
 * HiveTableColumnManager
 */
public class HiveTableColumnManager extends GenericTableColumnManager {

    private static final Log log = Log.getLog(HiveTableColumnManager.class);

    @Override
    public boolean canCreateObject(Object container) {
        return true;
    }

    @Override
    public boolean canDeleteObject(GenericTableColumn object) {
        return true;
    }

    @Override
    protected void addObjectCreateActions(DBRProgressMonitor monitor, DBCExecutionContext executionContext, List<DBEPersistAction> actions, SQLObjectEditor<GenericTableColumn, GenericTableBase>.ObjectCreateCommand command, Map<String, Object> options) {
        HiveTable table = (HiveTable) command.getObject().getParentObject();
        actions.add(
                new SQLDatabasePersistAction(
                        "Add table column",
                        "ALTER TABLE " + DBUtils.getObjectFullName(table, DBPEvaluationContext.DDL) + " ADD COLUMNS ("  +
                                getNestedDeclaration(monitor, table, command, options) + ")") );
    }

    @Override
    public StringBuilder getNestedDeclaration(DBRProgressMonitor monitor, GenericTableBase owner, DBECommandAbstract<GenericTableColumn> command, Map<String, Object> options) {
        StringBuilder decl = super.getNestedDeclaration(monitor, owner, command, options);
        return decl;
    }

    @Override
    protected void addObjectDeleteActions(DBRProgressMonitor monitor, DBCExecutionContext executionContext, List<DBEPersistAction> actions, SQLObjectEditor<GenericTableColumn, GenericTableBase>.ObjectDeleteCommand command, Map<String, Object> options) throws DBException {
        HiveTableColumn hiveTableColumn = (HiveTableColumn) command.getObject();
        HiveTable table = (HiveTable) hiveTableColumn.getParentObject();
        try {
            List<? extends GenericTableColumn> attributes = table.getAttributes(monitor);
            //It may not be the best option. Some of the column data may still be lost. It might be worth using a temporary table
            StringBuilder ddl = new StringBuilder();
            ddl.append("ALTER TABLE ").append(DBUtils.getObjectFullName(table, DBPEvaluationContext.DDL)).append(" REPLACE COLUMNS (");
            if (attributes != null) {
                for (int i = 0; i < attributes.size(); i++) {
                    GenericTableColumn column = attributes.get(i);
                    if (column != hiveTableColumn) {
                        if (i != 0) {
                            ddl.append(" ");
                        }
                        ddl.append(DBUtils.getQuotedIdentifier(column)).append(" ").append(column.getTypeName());
                        String typeModifiers = SQLUtils.getColumnTypeModifiers(table.getDataSource(), column, column.getTypeName(), column.getDataKind());
                        if (typeModifiers != null) {
                            ddl.append(typeModifiers);
                        }
                        String description = column.getDescription();
                        if (column.getDescription() != null) {
                            ddl.append(" COMMENT '").append(description).append("'");
                        }
                        if (i != attributes.size() - 1) {
                            ddl.append(",");
                        }
                    }
                }
            }
            ddl.append(")");
            actions.add(new SQLDatabasePersistAction("Drop table column", ddl.toString()));
        } catch (DBException e) {
            log.debug("Columns not found in table: " + table.getName(), e);
        }
    }

}
