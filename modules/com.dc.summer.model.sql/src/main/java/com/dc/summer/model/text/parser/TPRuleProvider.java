
package com.dc.summer.model.text.parser;

import com.dc.summer.model.DBPDataSourceContainer;
import com.dc.code.NotNull;
import com.dc.code.Nullable;

import java.util.List;

/**
 * SQL parser rules provider.
 * It is an extension for SQL dialect implementors.
 */
public interface TPRuleProvider {

    enum RulePosition {
        PARTITION,
        INITIAL,
        FINAL,
        CONTROL,
        QUOTES,
        KEYWORDS
    }

    void extendRules(@Nullable DBPDataSourceContainer dataSource, @NotNull List<TPRule> rules, @NotNull RulePosition position);

}
