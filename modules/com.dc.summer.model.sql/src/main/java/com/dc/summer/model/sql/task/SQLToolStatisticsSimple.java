
package com.dc.summer.model.sql.task;

import com.dc.summer.model.DBPObject;
import com.dc.summer.model.meta.Property;

public class SQLToolStatisticsSimple extends SQLToolStatistics{
    private String statusMessage;

    @Property(viewable = true, editable = true, updatable = true)
    public String getStatusMessage() {
        return statusMessage;
    }

    public void setStatusMessage(String message) {
        this.statusMessage = message;
    }

    protected SQLToolStatisticsSimple(DBPObject object, boolean isError) {
        super(object);
        if(isError){
            statusMessage = "ERROR";
        }
        else{
            statusMessage = "OK";
        }
    }
}
