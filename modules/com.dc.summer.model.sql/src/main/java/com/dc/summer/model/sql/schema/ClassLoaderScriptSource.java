

package com.dc.summer.model.sql.schema;

import com.dc.summer.DBException;
import com.dc.summer.model.runtime.DBRProgressMonitor;
import com.dc.code.NotNull;
import com.dc.code.Nullable;

import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.Reader;

/**
 * Script source which reads scripts from class loader
 */
public class ClassLoaderScriptSource implements SQLSchemaScriptSource {

    private final ClassLoader classLoader;
    private final String createScriptPath;
    private final String updateScriptPrefix;

    public ClassLoaderScriptSource(ClassLoader classLoader, String createScriptPath, String updateScriptPrefix) {
        this.classLoader = classLoader;
        this.createScriptPath = createScriptPath;
        this.updateScriptPrefix = updateScriptPrefix;
    }

    @NotNull
    @Override
    public Reader openSchemaCreateScript(DBRProgressMonitor monitor) throws IOException, DBException {
        InputStream resource = classLoader.getResourceAsStream(createScriptPath);
        if (resource == null) {
            throw new IOException("Resource '" + createScriptPath + "' not found in " + this.classLoader.getClass().getName());
        }
        return new InputStreamReader(resource);
    }

    @Nullable
    @Override
    public Reader openSchemaUpdateScript(DBRProgressMonitor monitor, int versionNumber) throws IOException, DBException {
        InputStream resource = classLoader.getResourceAsStream(updateScriptPrefix + versionNumber + ".sql");
        if (resource == null) {
            return null;
        }
        return new InputStreamReader(resource);
    }

}
