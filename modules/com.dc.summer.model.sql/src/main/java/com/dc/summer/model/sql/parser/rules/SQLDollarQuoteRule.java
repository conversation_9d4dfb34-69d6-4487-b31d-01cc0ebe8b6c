
package com.dc.summer.model.sql.parser.rules;

import com.dc.summer.model.sql.parser.tokens.SQLBlockToggleToken;
import com.dc.summer.model.text.parser.*;
import com.dc.summer.model.sql.parser.tokens.SQLTokenType;
import com.dc.summer.model.text.parser.*;

public class SQLDollarQuoteRule implements TPPredicateRule {

    private final boolean partitionRule;
    private final boolean allowNamedQuotes;
    private final boolean fullyConsumeNamed;
    private final boolean fullyConsumeUnnamed;
    private final TPToken stringToken, delimiterToken;

    /**
     * @param partitionRule       whether this rule is a partition rule or not
     * @param allowNamedQuotes    whether this rule supports named quotes ({@code $named$}) or not
     * @param fullyConsumeNamed   whether this rule should stop after consuming named quote
     *                            or continue until matching the closing one, treating everything between as a string
     * @param fullyConsumeUnnamed same as {@code fullyConsumeNamed}, but for unnamed quotes
     */
    public SQLDollarQuoteRule(boolean partitionRule, boolean allowNamedQuotes, boolean fullyConsumeNamed, boolean fullyConsumeUnnamed) {
        this.partitionRule = partitionRule;
        this.allowNamedQuotes = allowNamedQuotes;
        this.fullyConsumeNamed = fullyConsumeNamed;
        this.fullyConsumeUnnamed = fullyConsumeUnnamed;

        this.stringToken = new TPTokenDefault(SQLTokenType.T_STRING);
        this.delimiterToken = new SQLBlockToggleToken();
    }

    @Override
    public TPToken evaluate(TPCharacterScanner scanner) {
        return evaluate(scanner, false);
    }

    @Override
    public TPToken getSuccessToken() {
        return stringToken;
    }

    @Override
    public TPToken evaluate(TPCharacterScanner scanner, boolean resume) {
        int totalRead = 0;
        int c = scanner.read();
        totalRead++;
        if (c == '$') {
            int charsRead = 0;
            do {
                c = scanner.read();
                charsRead++;
                totalRead++;
                if (c == '$') {

                    if (charsRead > 1 ? fullyConsumeNamed : fullyConsumeUnnamed) {
                        // Here is a trick - dollar quote without preceding AS or DO and without tag is a string.
                        // Quote with tag is just a block toggle.
                        // I'm afraid we can't do more (#6608, #7183)
                        boolean stringEndFound = false;
                        //StringBuilder stringValue = new StringBuilder();
                        for (;;) {
                            c = scanner.read();
                            totalRead++;
                            if (c == TPCharacterScanner.EOF) {
                                break;
                            }
                            if (c == '$') {
                                int c2 = scanner.read();
                                totalRead++;
                                if (c2 == '$') {
                                    stringEndFound = true;
                                    break;
                                } else {
                                    scanner.unread();
                                    totalRead--;
                                }
                            }
                            //stringValue.append((char)c);
                        }

                        if (!stringEndFound) {
                            if (!partitionRule) {
                                unread(scanner, totalRead - 2);
                                return delimiterToken;
                            } else {
                                break;
                            }
                        }
/*
                        String encString = stringValue.toString().toUpperCase(Locale.ENGLISH);
                        if (encString.contains(SQLConstants.BLOCK_BEGIN) && encString.contains(SQLConstants.BLOCK_END)) {
                            // Seems to be a code block
                            if (!partitionRule) {
                                unread(scanner, totalRead - 2);
                                return delimiterToken;
                            } else {
                                return partCodeToken;
                            }
                        }
*/
                        // Find the end of the string
                        return stringToken;
                    }
                    if (!partitionRule) {
                        return delimiterToken;
                    } else {
                        break;
                    }
                }
            } while ((Character.isLetterOrDigit(c) || c == '_') && allowNamedQuotes);
        }

        unread(scanner, totalRead);

        return TPTokenAbstract.UNDEFINED;
    }

    private static void unread(TPCharacterScanner scanner, int totalRead) {
        while (totalRead-- > 0) {
            scanner.unread();
        }
    }

}
