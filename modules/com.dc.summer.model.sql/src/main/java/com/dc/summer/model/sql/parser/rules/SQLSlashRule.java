package com.dc.summer.model.sql.parser.rules;

import com.dc.summer.model.sql.parser.tokens.SQLSlashToken;
import com.dc.summer.model.text.parser.TPCharacterScanner;
import com.dc.summer.model.text.parser.TPRule;
import com.dc.summer.model.text.parser.TPToken;
import com.dc.summer.model.text.parser.TPTokenAbstract;

public class SQLSlashRule implements TPRule {

    private final SQLSlashToken sqlSlashToken;

    public SQLSlashRule(SQLSlashToken sqlSlashToken) {
        this.sqlSlashToken = sqlSlashToken;
    }

    @Override
    public TPToken evaluate(TPCharacterScanner scanner) {
        int c;
        c = scanner.read();
        if ((char) c == '/') {
            return sqlSlashToken;
        }
        scanner.unread();
        return TPTokenAbstract.UNDEFINED;
    }
}
