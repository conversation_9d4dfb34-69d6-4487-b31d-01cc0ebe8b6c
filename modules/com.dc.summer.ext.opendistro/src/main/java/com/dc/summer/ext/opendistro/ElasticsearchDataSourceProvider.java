
package com.dc.summer.ext.opendistro;

import com.dc.summer.Log;
import com.dc.summer.ext.generic.GenericDataSourceProvider;
import com.dc.summer.model.app.DBPPlatform;
import com.dc.code.NotNull;

public class ElasticsearchDataSourceProvider extends GenericDataSourceProvider {

    private static final Log log = Log.getLog(ElasticsearchDataSourceProvider.class);

    public ElasticsearchDataSourceProvider()
    {
    }

    @Override
    public void init(@NotNull DBPPlatform platform) {

    }

}
