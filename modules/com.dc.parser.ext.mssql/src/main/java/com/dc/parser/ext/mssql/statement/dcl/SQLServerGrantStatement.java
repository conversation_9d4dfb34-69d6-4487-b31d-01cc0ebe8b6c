

package com.dc.parser.ext.mssql.statement.dcl;

import lombok.Getter;
import com.dc.parser.model.segment.dml.column.ColumnSegment;
import com.dc.parser.model.statement.dcl.GrantStatement;
import com.dc.parser.ext.mssql.statement.SQLServerStatement;

import java.util.LinkedList;
import java.util.List;

/**
 * SQLServer grant statement.
 */
@Getter
public final class SQLServerGrantStatement extends GrantStatement implements SQLServerStatement {
    
    private final List<ColumnSegment> columns = new LinkedList<>();
}
