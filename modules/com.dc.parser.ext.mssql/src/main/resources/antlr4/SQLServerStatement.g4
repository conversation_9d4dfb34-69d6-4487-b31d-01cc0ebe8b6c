
grammar SQLServerStatement;

import Comments, TCLStatement, StoreProcedure, DALStatement;

execute
    : (select
    | insert
    | update
    | delete
    | createIndex
    | alterIndex
    | dropIndex
    | createTable
    | createDatabase
    | createProcedure
    | createView
    | createTrigger
    | createSequence
    | createService
    | createSchema
    | alterTable
    | alterTrigger
    | alterSequence
    | alterDatabase
    | alterService
    | alterSchema
    | alterView
    | dropTable
    | dropDatabase
    | dropFunction
    | dropProcedure
    | dropView
    | dropTrigger
    | dropSequence
    | dropService
    | dropSchema
    | truncateTable
    | createFunction
    | setTransaction
    | beginTransaction
    | beginDistributedTransaction
    | setImplicitTransactions
    | commit
    | commitWork
    | rollback
    | rollbackWork
    | savepoint
    | grant
    | revoke
    | deny
    | createUser
    | dropUser
    | alterUser
    | createRole
    | dropRole
    | alterRole
    | createLogin
    | dropLogin
    | alterLogin
    | call
    | explain
    | setUser
    | revert
    | updateStatistics
    | merge
    ) SEMI_? EOF
    ;
