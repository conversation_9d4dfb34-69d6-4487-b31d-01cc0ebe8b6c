package com.dc.summer.ext.elasticsearch.exec;

import com.dc.summer.model.Types;
import com.dc.summer.model.exec.DBCException;
import com.dc.utils.StringUtils;

public class ESRestResultSet extends ESBaseResultSet {

    private Object value;

    private final int typeId;

    public ESRestResultSet(ESBaseStatement statement, String result) {
        super(statement, result);
        this.typeId = StringUtils.isJson(result) ? Types.JSON : Types.STRING;
    }

    @Override
    public Object getAttributeValue(int index) throws DBCException {
        this.checkRowFetched();
        if (index == 0) {
            return this.value;
        } else {
            throw new DBCException("Index out of range (" + index + ")");
        }
    }

    @Override
    public Object getAttributeValue(String name) throws DBCException {
        return null;
    }

    @Override
    public boolean nextRow() throws DBCException {
        value = result;
        result = null;
        boolean nextRow = value != null;
        if (nextRow) {
            ++itemNumber;
        }
        return nextRow;
    }

    @Override
    public boolean overMaxRow() throws DBCException {
        return false;
    }

    @Override
    public boolean moveTo(int position) throws DBCException {
        return false;
    }

    @Override
    public int getTypeId() {
        return typeId;
    }
    @Override
    public void close() {
        value = null;
        result = null;
        super.close();
    }

}
