package com.dc.summer.exec.pool;

import com.alibaba.druid.pool.DruidDataSource;
import com.dc.summer.exec.pool.model.DruidExternalConnection;
import com.dc.summer.model.DBPDataSourceContainer;
import com.dc.summer.model.DBPExternalConnection;
import com.dc.summer.model.DBPExternalConnectionProvider;
import com.dc.summer.model.impl.connection.ConnectionPool;

import java.sql.Driver;
import java.util.Map;

public class DruidExternalConnectionProvider implements DBPExternalConnectionProvider {

    @Override
    public DBPExternalConnection openExternalConnection(DBPDataSourceContainer container,
                                                        Driver driver,
                                                        Map<String, String> ignoreProperties) {
        return new DruidExternalConnection(container, driver, ignoreProperties);
    }

    @Override
    public void closeExternalConnection(DBPDataSourceContainer container) {
        ConnectionPool pool = ConnectionPool.getInstance();
        Object lock = container.getExclusiveLock().acquireExclusiveLock();
        try (DruidDataSource ignored = (DruidDataSource) pool.removeDataSource(container.getId())) {
            // nothing to do here
        } finally {
            container.getExclusiveLock().releaseExclusiveLock(lock);
        }
    }

}
