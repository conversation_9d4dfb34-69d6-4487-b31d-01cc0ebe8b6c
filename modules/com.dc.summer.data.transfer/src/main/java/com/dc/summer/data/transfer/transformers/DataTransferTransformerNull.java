
package com.dc.summer.data.transfer.transformers;

import com.dc.code.NotNull;
import com.dc.summer.DBException;
import com.dc.summer.model.data.DBDAttributeBinding;
import com.dc.summer.model.exec.DBCSession;
import com.dc.summer.data.transfer.IDataTransferAttributeTransformer;

import java.util.Map;

/**
 * Null attribute transformer
 */
public class DataTransferTransformerNull implements IDataTransferAttributeTransformer {

    @Override
    public Object transformAttribute(@NotNull DBCSession session, @NotNull DBDAttributeBinding[] dataAttributes, @NotNull Object[] dataRow, @NotNull DBDAttributeBinding attribute, Object attrValue, @NotNull Map<String, Object> options) throws DBException {
        return null;
    }

}
