package com.dc.parser.exec.engine.statement.dal;

import com.dc.infra.utils.CaseInsensitiveMap.CaseInsensitiveString;
import com.dc.parser.exec.engine.segment.dml.from.context.TableSegmentBinderContext;
import com.dc.parser.exec.engine.segment.dml.from.type.SimpleTableSegmentBinder;
import com.dc.parser.exec.engine.segment.filter.ShowFilterSegmentBinder;
import com.dc.parser.exec.engine.statement.SQLStatementBinder;
import com.dc.parser.exec.engine.statement.SQLStatementBinderContext;
import com.dc.parser.model.statement.dal.ShowColumnsStatement;
import com.dc.summer.parser.sql.constants.SqlConstant;
import com.google.common.collect.LinkedHashMultimap;
import com.google.common.collect.Multimap;
import lombok.SneakyThrows;

/**
 * Show columns statement binder.
 */
public final class ShowColumnsStatementBinder implements SQLStatementBinder<ShowColumnsStatement> {

    @Override
    public ShowColumnsStatement bind(final ShowColumnsStatement sqlStatement, final SQLStatementBinderContext binderContext) {
        ShowColumnsStatement result = copy(sqlStatement);
        binderContext.setOperation(SqlConstant.KEY_SHOW);
        Multimap<CaseInsensitiveString, TableSegmentBinderContext> tableBinderContexts = LinkedHashMultimap.create();
        result.setTable(SimpleTableSegmentBinder.bind(sqlStatement.getTable(), binderContext, tableBinderContexts));
        sqlStatement.getFromDatabase().ifPresent(result::setFromDatabase);
        sqlStatement.getFilter().ifPresent(optional -> result.setFilter(ShowFilterSegmentBinder.bind(optional, binderContext, tableBinderContexts, LinkedHashMultimap.create())));
        return result;
    }

    @SneakyThrows(ReflectiveOperationException.class)
    private static ShowColumnsStatement copy(final ShowColumnsStatement sqlStatement) {
        ShowColumnsStatement result = sqlStatement.getClass().getDeclaredConstructor().newInstance();
        result.addParameterMarkerSegments(sqlStatement.getParameterMarkerSegments());
        result.getCommentSegments().addAll(sqlStatement.getCommentSegments());
        result.getVariableNames().addAll(sqlStatement.getVariableNames());
        return result;
    }
}
