package com.dc.parser.exec.engine.segment.dml.combine;

import com.dc.infra.utils.CaseInsensitiveMap;
import com.dc.parser.exec.engine.segment.dml.from.context.TableSegmentBinderContext;
import com.dc.parser.exec.engine.statement.SQLStatementBinderContext;
import com.dc.parser.exec.engine.statement.dml.SelectStatementBinder;
import com.dc.parser.model.segment.dml.combine.CombineSegment;
import com.dc.parser.model.segment.dml.expr.subquery.SubquerySegment;
import com.google.common.collect.Multimap;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

/**
 * Combine segment binder.
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class CombineSegmentBinder {

    /**
     * Bind combine segment.
     *
     * @param segment                  table segment
     * @param binderContext            SQL statement binder context
     * @param outerTableBinderContexts outer table binder contexts
     * @return bound combine segment
     */
    public static CombineSegment bind(final CombineSegment segment, final SQLStatementBinderContext binderContext,
                                      final Multimap<CaseInsensitiveMap.CaseInsensitiveString, TableSegmentBinderContext> outerTableBinderContexts) {
        return new CombineSegment(segment.getStartIndex(), segment.getStopIndex(),
                bindSubquerySegment(segment.getLeft(), binderContext, outerTableBinderContexts), segment.getCombineType(),
                bindSubquerySegment(segment.getRight(), binderContext, outerTableBinderContexts));
    }

    private static SubquerySegment bindSubquerySegment(final SubquerySegment segment, final SQLStatementBinderContext binderContext,
                                                       final Multimap<CaseInsensitiveMap.CaseInsensitiveString, TableSegmentBinderContext> outerTableBinderContexts) {
        SubquerySegment result = new SubquerySegment(segment.getStartIndex(), segment.getStopIndex(), segment.getText());
        SQLStatementBinderContext subqueryBinderContext =
                new SQLStatementBinderContext(binderContext.getMetaData(), binderContext.getCurrentDatabaseName(), segment.getSelect());
        subqueryBinderContext.getExternalTableBinderContexts().putAll(binderContext.getExternalTableBinderContexts());
        subqueryBinderContext.getCommonTableExpressionsSegmentsUniqueAliases().addAll(binderContext.getCommonTableExpressionsSegmentsUniqueAliases());
        result.setSelect(new SelectStatementBinder(outerTableBinderContexts).bind(segment.getSelect(), subqueryBinderContext));
        binderContext.getCommonTableExpressionsSegmentsUniqueAliases().addAll(subqueryBinderContext.getCommonTableExpressionsSegmentsUniqueAliases());
        return result;
    }
}
