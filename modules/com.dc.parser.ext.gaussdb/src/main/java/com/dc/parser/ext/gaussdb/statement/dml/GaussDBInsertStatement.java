
package com.dc.parser.ext.gaussdb.statement.dml;

import com.dc.parser.ext.gaussdb.statement.GaussDBStatement;
import com.dc.parser.model.segment.dml.ReturningSegment;
import com.dc.parser.model.segment.dml.column.OnDuplicateKeyColumnsSegment;
import com.dc.parser.model.segment.generic.WithSegment;
import com.dc.parser.model.statement.dml.InsertStatement;
import lombok.Setter;

import java.util.Optional;

/**
 * GaussDB insert statement.
 */
@Setter
public final class GaussDBInsertStatement extends InsertStatement implements GaussDBStatement {
    
    private WithSegment withSegment;
    
    private OnDuplicateKeyColumnsSegment onDuplicateKeyColumnsSegment;
    
    private ReturningSegment returningSegment;
    
    /**
     * Get with segment.
     *
     * @return with segment.
     */
    public Optional<WithSegment> getWithSegment() {
        return Optional.ofNullable(withSegment);
    }
    
    /**
     * Get on duplicate key columns segment.
     *
     * @return on duplicate key columns segment
     */
    @Override
    public Optional<OnDuplicateKeyColumnsSegment> getOnDuplicateKeyColumns() {
        return Optional.ofNullable(onDuplicateKeyColumnsSegment);
    }
    
    /**
     * Get returning segment.
     *
     * @return returning segment
     */
    public Optional<ReturningSegment> getReturningSegment() {
        return Optional.ofNullable(returningSegment);
    }
}
