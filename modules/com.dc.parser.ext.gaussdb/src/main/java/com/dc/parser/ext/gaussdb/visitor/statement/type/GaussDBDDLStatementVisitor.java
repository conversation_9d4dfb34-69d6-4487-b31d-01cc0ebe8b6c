package com.dc.parser.ext.gaussdb.visitor.statement.type;

import com.dc.parser.ext.gaussdb.parser.autogen.GaussDBStatementParser.*;
import com.dc.parser.ext.gaussdb.statement.ddl.*;
import com.dc.parser.ext.gaussdb.visitor.statement.GaussDBStatementVisitor;
import com.dc.parser.model.api.ASTNode;
import com.dc.parser.model.api.visitor.statement.type.DDLStatementVisitor;
import com.dc.parser.model.enums.DirectionType;
import com.dc.parser.model.segment.ddl.AlterDefinitionSegment;
import com.dc.parser.model.segment.ddl.CreateDefinitionSegment;
import com.dc.parser.model.segment.ddl.column.ColumnDefinitionSegment;
import com.dc.parser.model.segment.ddl.column.alter.AddColumnDefinitionSegment;
import com.dc.parser.model.segment.ddl.column.alter.DropColumnDefinitionSegment;
import com.dc.parser.model.segment.ddl.column.alter.ModifyColumnDefinitionSegment;
import com.dc.parser.model.segment.ddl.column.alter.RenameColumnSegment;
import com.dc.parser.model.segment.ddl.constraint.ConstraintDefinitionSegment;
import com.dc.parser.model.segment.ddl.constraint.ConstraintSegment;
import com.dc.parser.model.segment.ddl.constraint.alter.AddConstraintDefinitionSegment;
import com.dc.parser.model.segment.ddl.constraint.alter.DropConstraintDefinitionSegment;
import com.dc.parser.model.segment.ddl.constraint.alter.ModifyConstraintDefinitionSegment;
import com.dc.parser.model.segment.ddl.constraint.alter.ValidateConstraintDefinitionSegment;
import com.dc.parser.model.segment.ddl.cursor.CursorNameSegment;
import com.dc.parser.model.segment.ddl.cursor.DirectionSegment;
import com.dc.parser.model.segment.ddl.index.IndexNameSegment;
import com.dc.parser.model.segment.ddl.index.IndexSegment;
import com.dc.parser.model.segment.ddl.table.RenameTableDefinitionSegment;
import com.dc.parser.model.segment.ddl.tablespace.TablespaceSegment;
import com.dc.parser.model.segment.dml.column.ColumnSegment;
import com.dc.parser.model.segment.dml.expr.ExpressionSegment;
import com.dc.parser.model.segment.dml.expr.FunctionSegment;
import com.dc.parser.model.segment.generic.DataTypeSegment;
import com.dc.parser.model.segment.generic.DatabaseSegment;
import com.dc.parser.model.segment.generic.NameSegment;
import com.dc.parser.model.segment.generic.OwnerSegment;
import com.dc.parser.model.segment.generic.table.SimpleTableSegment;
import com.dc.parser.model.segment.generic.table.TableNameSegment;
import com.dc.parser.model.statement.dml.DeleteStatement;
import com.dc.parser.model.statement.dml.InsertStatement;
import com.dc.parser.model.statement.dml.SelectStatement;
import com.dc.parser.model.statement.dml.UpdateStatement;
import com.dc.parser.model.value.collection.CollectionValue;
import com.dc.parser.model.value.identifier.IdentifierValue;
import com.dc.parser.model.value.literal.impl.NumberLiteralValue;
import org.antlr.v4.runtime.ParserRuleContext;
import org.antlr.v4.runtime.misc.Interval;

import java.util.*;
import java.util.stream.Collectors;

/**
 * DDL statement visitor for openGauss.
 */
public final class GaussDBDDLStatementVisitor extends GaussDBStatementVisitor implements DDLStatementVisitor {
    
    @SuppressWarnings("unchecked")
    @Override
    public ASTNode visitCreateTable(final CreateTableContext ctx) {
        GaussDBCreateTableStatement result = new GaussDBCreateTableStatement(null != ctx.ifNotExists());
        result.setTable((SimpleTableSegment) visit(ctx.tableName()));
        if (null != ctx.createDefinitionClause()) {
            CollectionValue<CreateDefinitionSegment> createDefinitions = (CollectionValue<CreateDefinitionSegment>) visit(ctx.createDefinitionClause());
            /*for (CreateDefinitionSegment each : createDefinitions.getValue()) {
                if (each instanceof ColumnDefinitionSegment) {
                    result.getColumnDefinitions().add((ColumnDefinitionSegment) each);
                } else if (each instanceof ConstraintDefinitionSegment) {
                    result.getConstraintDefinitions().add((ConstraintDefinitionSegment) each);
                }
            }*/
        }
        return result;
    }
    
    @Override
    public ASTNode visitCreateDefinitionClause(final CreateDefinitionClauseContext ctx) {
        CollectionValue<CreateDefinitionSegment> result = new CollectionValue<>();
        for (CreateDefinitionContext each : ctx.createDefinition()) {
            if (null != each.columnDefinition()) {
                result.getValue().add((ColumnDefinitionSegment) visit(each.columnDefinition()));
            }
            if (null != each.tableConstraint()) {
                result.getValue().add((ConstraintDefinitionSegment) visit(each.tableConstraint()));
            }
        }
        return result;
    }
    
    @SuppressWarnings("unchecked")
    @Override
    public ASTNode visitAlterTable(final AlterTableContext ctx) {
        GaussDBAlterTableStatement result = new GaussDBAlterTableStatement();
        result.setTable((SimpleTableSegment) visit(ctx.tableNameClause().tableName()));
        if (null != ctx.alterDefinitionClause()) {
            for (AlterDefinitionSegment each : ((CollectionValue<AlterDefinitionSegment>) visit(ctx.alterDefinitionClause())).getValue()) {
                if (each instanceof AddColumnDefinitionSegment) {
                    result.getAddColumnDefinitions().add((AddColumnDefinitionSegment) each);
                } else if (each instanceof ModifyColumnDefinitionSegment) {
                    result.getModifyColumnDefinitions().add((ModifyColumnDefinitionSegment) each);
                } else if (each instanceof DropColumnDefinitionSegment) {
                    result.getDropColumnDefinitions().add((DropColumnDefinitionSegment) each);
                } else if (each instanceof AddConstraintDefinitionSegment) {
                    result.getAddConstraintDefinitions().add((AddConstraintDefinitionSegment) each);
                } else if (each instanceof ValidateConstraintDefinitionSegment) {
                    result.getValidateConstraintDefinitions().add((ValidateConstraintDefinitionSegment) each);
                } else if (each instanceof ModifyConstraintDefinitionSegment) {
                    result.getModifyConstraintDefinitions().add((ModifyConstraintDefinitionSegment) each);
                } else if (each instanceof DropConstraintDefinitionSegment) {
                    result.getDropConstraintDefinitions().add((DropConstraintDefinitionSegment) each);
                } else if (each instanceof RenameTableDefinitionSegment) {
                    result.setRenameTable(((RenameTableDefinitionSegment) each).getRenameTable());
                } else if (each instanceof RenameColumnSegment) {
                    result.getRenameColumnDefinitions().add((RenameColumnSegment) each);
                }
            }
        }
        return result;
    }
    
    @Override
    public ASTNode visitAlterAggregate(final AlterAggregateContext ctx) {
        return new GaussDBAlterAggregateStatement();
    }
    
    @Override
    public ASTNode visitAlterDefaultPrivileges(final AlterDefaultPrivilegesContext ctx) {
        return new GaussDBAlterDefaultPrivilegesStatement();
    }
    
    @Override
    public ASTNode visitAlterDefinitionClause(final AlterDefinitionClauseContext ctx) {
        CollectionValue<AlterDefinitionSegment> result = new CollectionValue<>();
        if (null != ctx.alterTableActions()) {
            result.getValue().addAll(ctx.alterTableActions().alterTableAction().stream().flatMap(each -> getAlterDefinitionSegments(each).stream()).collect(Collectors.toList()));
        }
        if (null != ctx.renameColumnSpecification()) {
            result.getValue().add((RenameColumnSegment) visit(ctx.renameColumnSpecification()));
        }
        if (null != ctx.renameTableSpecification()) {
            result.getValue().add((RenameTableDefinitionSegment) visit(ctx.renameTableSpecification()));
        }
        return result;
    }
    
    @SuppressWarnings("unchecked")
    private Collection<AlterDefinitionSegment> getAlterDefinitionSegments(final AlterTableActionContext ctx) {
        Collection<AlterDefinitionSegment> result = new LinkedList<>();
        if (null != ctx.addColumnSpecification()) {
            result.addAll(((CollectionValue<AddColumnDefinitionSegment>) visit(ctx.addColumnSpecification())).getValue());
        }
        if (null != ctx.addConstraintSpecification() && null != ctx.addConstraintSpecification().tableConstraint()) {
            result.add((AddConstraintDefinitionSegment) visit(ctx.addConstraintSpecification()));
        }
        if (null != ctx.validateConstraintSpecification()) {
            result.add((ValidateConstraintDefinitionSegment) visit(ctx.validateConstraintSpecification()));
        }
        if (null != ctx.modifyColumnSpecification()) {
            result.add((ModifyColumnDefinitionSegment) visit(ctx.modifyColumnSpecification()));
        }
        if (null != ctx.modifyConstraintSpecification()) {
            result.add((ModifyConstraintDefinitionSegment) visit(ctx.modifyConstraintSpecification()));
        }
        if (null != ctx.dropColumnSpecification()) {
            result.add((DropColumnDefinitionSegment) visit(ctx.dropColumnSpecification()));
        }
        if (null != ctx.dropConstraintSpecification()) {
            result.add((DropConstraintDefinitionSegment) visit(ctx.dropConstraintSpecification()));
        }
        return result;
    }
    
    @Override
    public ASTNode visitAlterForeignTable(final AlterForeignTableContext ctx) {
        return new GaussDBAlterForeignTableStatement();
    }
    
    @Override
    public ASTNode visitAlterGroup(final AlterGroupContext ctx) {
        return new GaussDBAlterGroupStatement();
    }
    
    @Override
    public ASTNode visitAlterPackage(final AlterPackageContext ctx) {
        return new GaussDBAlterPackageStatement();
    }
    
    @Override
    public ASTNode visitAlterMaterializedView(final AlterMaterializedViewContext ctx) {
        return new GaussDBAlterMaterializedViewStatement();
    }
    
    @Override
    public ASTNode visitAddConstraintSpecification(final AddConstraintSpecificationContext ctx) {
        return new AddConstraintDefinitionSegment(ctx.getStart().getStartIndex(), ctx.getStop().getStopIndex(), (ConstraintDefinitionSegment) visit(ctx.tableConstraint()));
    }
    
    @Override
    public ASTNode visitValidateConstraintSpecification(final ValidateConstraintSpecificationContext ctx) {
        return new ValidateConstraintDefinitionSegment(ctx.getStart().getStartIndex(), ctx.getStop().getStopIndex(), (ConstraintSegment) visit(ctx.constraintName()));
    }
    
    @Override
    public ASTNode visitModifyConstraintSpecification(final ModifyConstraintSpecificationContext ctx) {
        return new ModifyConstraintDefinitionSegment(ctx.getStart().getStartIndex(), ctx.getStop().getStopIndex(), (ConstraintSegment) visit(ctx.constraintName()));
    }
    
    @Override
    public ASTNode visitDropConstraintSpecification(final DropConstraintSpecificationContext ctx) {
        return new DropConstraintDefinitionSegment(ctx.getStart().getStartIndex(), ctx.getStop().getStopIndex(), (ConstraintSegment) visit(ctx.constraintName()));
    }
    
    @Override
    public ASTNode visitAlterDomain(final AlterDomainContext ctx) {
        return new GaussDBAlterDomainStatement();
    }
    
    @Override
    public ASTNode visitRenameTableSpecification(final RenameTableSpecificationContext ctx) {
        RenameTableDefinitionSegment result = new RenameTableDefinitionSegment(ctx.start.getStartIndex(), ctx.stop.getStopIndex());
        TableNameSegment tableName = new TableNameSegment(ctx.identifier().start.getStartIndex(), ctx.identifier().stop.getStopIndex(), (IdentifierValue) visit(ctx.identifier()));
        result.setRenameTable(new SimpleTableSegment(tableName));
        return result;
    }
    
    @Override
    public ASTNode visitAddColumnSpecification(final AddColumnSpecificationContext ctx) {
        CollectionValue<AddColumnDefinitionSegment> result = new CollectionValue<>();
        ColumnDefinitionContext columnDefinition = ctx.columnDefinition();
        if (null != columnDefinition) {
            AddColumnDefinitionSegment addColumnDefinition = new AddColumnDefinitionSegment(
                    ctx.columnDefinition().getStart().getStartIndex(), columnDefinition.getStop().getStopIndex(), Collections.singleton((ColumnDefinitionSegment) visit(columnDefinition)));
            result.getValue().add(addColumnDefinition);
        }
        return result;
    }
    
    @Override
    public ASTNode visitColumnDefinition(final ColumnDefinitionContext ctx) {
        ColumnSegment column = (ColumnSegment) visit(ctx.columnName());
        DataTypeSegment dataType = (DataTypeSegment) visit(ctx.dataType());
        boolean isPrimaryKey = ctx.columnConstraint().stream().anyMatch(each -> null != each.columnConstraintOption() && null != each.columnConstraintOption().primaryKey());
        // TODO parse not null
        ColumnDefinitionSegment result = new ColumnDefinitionSegment(ctx.getStart().getStartIndex(), ctx.getStop().getStopIndex(), getText(ctx));
        result.setColumnName(column);
        result.setPrimaryKey(isPrimaryKey);
        result.setDataType(dataType);
        for (ColumnConstraintContext each : ctx.columnConstraint()) {
            if (null != each.columnConstraintOption().tableName()) {
                result.getReferencedTables().add((SimpleTableSegment) visit(each.columnConstraintOption().tableName()));
            }
        }
        return result;
    }

    private String getText(final ParserRuleContext ctx) {
        return ctx.start.getInputStream().getText(new Interval(ctx.start.getStartIndex(), ctx.stop.getStopIndex()));
    }
    
    @Override
    public ASTNode visitTableConstraintUsingIndex(final TableConstraintUsingIndexContext ctx) {
        ConstraintDefinitionSegment result = new ConstraintDefinitionSegment(ctx.getStart().getStartIndex(), ctx.getStop().getStopIndex());
        if (null != ctx.constraintName()) {
            result.setConstraintName((ConstraintSegment) visit(ctx.constraintName()));
        }
        if (null != ctx.indexName()) {
            result.setIndexName((IndexSegment) visit(ctx.indexName()));
        }
        return result;
    }
    
    @SuppressWarnings("unchecked")
    @Override
    public ASTNode visitTableConstraint(final TableConstraintContext ctx) {
        ConstraintDefinitionSegment result = new ConstraintDefinitionSegment(ctx.getStart().getStartIndex(), ctx.getStop().getStopIndex());
        if (null != ctx.constraintClause()) {
            result.setConstraintName((ConstraintSegment) visit(ctx.constraintClause().constraintName()));
        }
        if (null != ctx.tableConstraintOption().primaryKey()) {
            result.getPrimaryKeyColumns().addAll(((CollectionValue<ColumnSegment>) visit(ctx.tableConstraintOption().columnNames(0))).getValue());
        }
        if (null != ctx.tableConstraintOption().FOREIGN()) {
            result.setReferencedTable((SimpleTableSegment) visit(ctx.tableConstraintOption().tableName()));
        }
        return result;
    }
    
    @Override
    public ASTNode visitModifyColumnSpecification(final ModifyColumnSpecificationContext ctx) {
        // TODO visit pk and table ref
        ColumnSegment column = (ColumnSegment) visit(ctx.modifyColumn().columnName());
        DataTypeSegment dataType = null == ctx.dataType() ? null : (DataTypeSegment) visit(ctx.dataType());
        ColumnDefinitionSegment columnDefinition = new ColumnDefinitionSegment(ctx.getStart().getStartIndex(), ctx.getStop().getStopIndex(), getText(ctx));
        columnDefinition.setColumnName(column);
        columnDefinition.setDataType(dataType);
        return new ModifyColumnDefinitionSegment(ctx.getStart().getStartIndex(), ctx.getStop().getStopIndex(), columnDefinition);
    }
    
    @Override
    public ASTNode visitDropColumnSpecification(final DropColumnSpecificationContext ctx) {
        return new DropColumnDefinitionSegment(ctx.getStart().getStartIndex(), ctx.getStop().getStopIndex(), Collections.singleton((ColumnSegment) visit(ctx.columnName())));
    }
    
    @Override
    public ASTNode visitRenameColumnSpecification(final RenameColumnSpecificationContext ctx) {
        return new RenameColumnSegment(ctx.getStart().getStartIndex(), ctx.getStop().getStopIndex(), (ColumnSegment) visit(ctx.columnName(0)), (ColumnSegment) visit(ctx.columnName(1)));
    }
    
    @SuppressWarnings("unchecked")
    @Override
    public ASTNode visitDropTable(final DropTableContext ctx) {
        boolean containsCascade = null != ctx.dropTableOpt() && null != ctx.dropTableOpt().CASCADE();
        GaussDBDropTableStatement result = new GaussDBDropTableStatement();
        result.setIfExists(null != ctx.ifExists());
        result.setContainsCascade(containsCascade);
        result.getTables().addAll(((CollectionValue<SimpleTableSegment>) visit(ctx.tableNames())).getValue());
        return result;
    }
    
    @SuppressWarnings("unchecked")
    @Override
    public ASTNode visitTruncateTable(final TruncateTableContext ctx) {
        GaussDBTruncateStatement result = new GaussDBTruncateStatement();
        result.getTables().addAll(((CollectionValue<SimpleTableSegment>) visit(ctx.tableNamesClause())).getValue());
        return result;
    }
    
    @SuppressWarnings("unchecked")
    @Override
    public ASTNode visitCreateIndex(final CreateIndexContext ctx) {
        GaussDBCreateIndexStatement result = new GaussDBCreateIndexStatement(null != ctx.ifNotExists());
        result.setTable((SimpleTableSegment) visit(ctx.tableName()));
        result.getColumns().addAll(((CollectionValue<ColumnSegment>) visit(ctx.indexParams())).getValue());
        if (null == ctx.indexName()) {
            result.setGeneratedIndexStartIndex(ctx.ON().getSymbol().getStartIndex() - 1);
        } else {
            result.setIndex((IndexSegment) visit(ctx.indexName()));
        }
        return result;
    }
    
    @Override
    public ASTNode visitIndexParams(final IndexParamsContext ctx) {
        CollectionValue<ColumnSegment> result = new CollectionValue<>();
        for (IndexElemContext each : ctx.indexElem()) {
            if (null != each.colId()) {
                result.getValue().add(new ColumnSegment(each.colId().start.getStartIndex(), each.colId().stop.getStopIndex(), new IdentifierValue(each.colId().getText())));
            }
            if (null != each.functionExprWindowless()) {
                FunctionSegment functionSegment = (FunctionSegment) visit(each.functionExprWindowless());
                functionSegment.getParameters().forEach(param -> {
                    if (param instanceof ColumnSegment) {
                        result.getValue().add((ColumnSegment) param);
                    }
                });
            }
        }
        return result;
    }
    
    @Override
    public ASTNode visitFunctionExprWindowless(final FunctionExprWindowlessContext ctx) {
        FunctionSegment result = new FunctionSegment(ctx.getStart().getStartIndex(), ctx.getStop().getStopIndex(), ctx.funcApplication().funcName().getText(), getOriginalText(ctx));
        result.getParameters().addAll(getExpressions(ctx.funcApplication().funcArgList().funcArgExpr()));
        return result;
    }
    
    private Collection<ExpressionSegment> getExpressions(final Collection<FuncArgExprContext> aExprContexts) {
        if (null == aExprContexts) {
            return Collections.emptyList();
        }
        Collection<ExpressionSegment> result = new ArrayList<>(aExprContexts.size());
        for (FuncArgExprContext each : aExprContexts) {
            result.add((ExpressionSegment) visit(each.aExpr()));
        }
        return result;
    }
    
    @Override
    public ASTNode visitAlterIndex(final AlterIndexContext ctx) {
        GaussDBAlterIndexStatement result = new GaussDBAlterIndexStatement();
        result.setIndex(createIndexSegment((SimpleTableSegment) visit(ctx.qualifiedName())));
        if (null != ctx.alterIndexDefinitionClause().renameIndexSpecification()) {
            result.setRenameIndex((IndexSegment) visit(ctx.alterIndexDefinitionClause().renameIndexSpecification().indexName()));
        }
        return result;
    }
    
    private IndexSegment createIndexSegment(final SimpleTableSegment tableSegment) {
        IndexNameSegment indexName = new IndexNameSegment(tableSegment.getTableName().getStartIndex(), tableSegment.getTableName().getStopIndex(), tableSegment.getTableName().getIdentifier());
        IndexSegment result = new IndexSegment(tableSegment.getStartIndex(), tableSegment.getStopIndex(), indexName);
        tableSegment.getOwner().ifPresent(result::setOwner);
        return result;
    }
    
    @SuppressWarnings("unchecked")
    @Override
    public ASTNode visitDropIndex(final DropIndexContext ctx) {
        GaussDBDropIndexStatement result = new GaussDBDropIndexStatement();
        result.setIfExists(null != ctx.ifExists());
        result.getIndexes().addAll(createIndexSegments(((CollectionValue<SimpleTableSegment>) visit(ctx.qualifiedNameList())).getValue()));
        return result;
    }
    
    private Collection<IndexSegment> createIndexSegments(final Collection<SimpleTableSegment> tableSegments) {
        Collection<IndexSegment> result = new LinkedList<>();
        for (SimpleTableSegment each : tableSegments) {
            result.add(createIndexSegment(each));
        }
        return result;
    }
    
    @Override
    public ASTNode visitIndexNames(final IndexNamesContext ctx) {
        CollectionValue<IndexSegment> result = new CollectionValue<>();
        for (IndexNameContext each : ctx.indexName()) {
            result.getValue().add((IndexSegment) visit(each));
        }
        return result;
    }
    
    @Override
    public ASTNode visitTableNameClause(final TableNameClauseContext ctx) {
        return visit(ctx.tableName());
    }
    
    @Override
    public ASTNode visitTableNamesClause(final TableNamesClauseContext ctx) {
        Collection<SimpleTableSegment> tableSegments = new LinkedList<>();
        for (int i = 0; i < ctx.tableNameClause().size(); i++) {
            tableSegments.add((SimpleTableSegment) visit(ctx.tableNameClause(i)));
        }
        CollectionValue<SimpleTableSegment> result = new CollectionValue<>();
        result.getValue().addAll(tableSegments);
        return result;
    }
    
    @Override
    public ASTNode visitAlterFunction(final AlterFunctionContext ctx) {
        return new GaussDBAlterFunctionStatement();
    }
    
    @Override
    public ASTNode visitAlterProcedure(final AlterProcedureContext ctx) {
        return new GaussDBAlterProcedureStatement();
    }
    
    @Override
    public ASTNode visitCreateFunction(final CreateFunctionContext ctx) {
        return new GaussDBCreateFunctionStatement();
    }
    
    @Override
    public ASTNode visitCreateProcedure(final CreateProcedureContext ctx) {
        return new GaussDBCreateProcedureStatement();
    }
    
    @Override
    public ASTNode visitDropFunction(final DropFunctionContext ctx) {
        return new GaussDBDropFunctionStatement();
    }
    
    @SuppressWarnings("unchecked")
    @Override
    public ASTNode visitDropView(final DropViewContext ctx) {
        GaussDBDropViewStatement result = new GaussDBDropViewStatement();
        result.setIfExists(null != ctx.ifExists());
        result.getViews().addAll(((CollectionValue<SimpleTableSegment>) visit(ctx.qualifiedNameList())).getValue());
        return result;
    }
    
    @Override
    public ASTNode visitCreateView(final CreateViewContext ctx) {
        GaussDBCreateViewStatement result = new GaussDBCreateViewStatement();
        result.setReplaceView(null != ctx.REPLACE());
        result.setView((SimpleTableSegment) visit(ctx.qualifiedName()));
        result.setViewDefinition(getOriginalText(ctx.select()));
        result.setSelect((SelectStatement) visit(ctx.select()));
        return result;
    }
    
    @Override
    public ASTNode visitAlterView(final AlterViewContext ctx) {
        GaussDBAlterViewStatement result = new GaussDBAlterViewStatement();
        result.setView((SimpleTableSegment) visit(ctx.qualifiedName()));
        if (ctx.alterViewClauses() instanceof AlterRenameViewContext) {
            NameContext nameContext = ((AlterRenameViewContext) ctx.alterViewClauses()).name();
            result.setRenameView(
                    new SimpleTableSegment(new TableNameSegment(nameContext.getStart().getStartIndex(), nameContext.getStop().getStopIndex(), (IdentifierValue) visit(nameContext.identifier()))));
        }
        return result;
    }
    
    @Override
    public ASTNode visitDropDatabase(final DropDatabaseContext ctx) {
        GaussDBDropDatabaseStatement result = new GaussDBDropDatabaseStatement();
        IdentifierValue value = (IdentifierValue) visit(ctx.name());
        result.setDatabaseName(new DatabaseSegment(ctx.name().getStart().getStartIndex(), ctx.name().getStop().getStopIndex(), value));
        result.setIfExists(null != ctx.ifExists());
        return result;
    }
    
    @Override
    public ASTNode visitDropServer(final DropServerContext ctx) {
        return new GaussDBDropServerStatement();
    }
    
    @Override
    public ASTNode visitDropProcedure(final DropProcedureContext ctx) {
        return new GaussDBDropProcedureStatement();
    }
    
    @Override
    public ASTNode visitDropPublication(final DropPublicationContext ctx) {
        return new GaussDBDropPublicationStatement();
    }
    
    @Override
    public ASTNode visitDropCast(final DropCastContext ctx) {
        return new GaussDBDropCastStatement();
    }
    
    @Override
    public ASTNode visitDropRule(final DropRuleContext ctx) {
        return new GaussDBDropRuleStatement();
    }
    
    @Override
    public ASTNode visitCreateDatabase(final CreateDatabaseContext ctx) {
        GaussDBCreateDatabaseStatement result = new GaussDBCreateDatabaseStatement();
        IdentifierValue value = (IdentifierValue) visit(ctx.name());
        result.setDatabaseName(new DatabaseSegment(ctx.name().getStart().getStartIndex(), ctx.name().getStop().getStopIndex(), value));
        return result;
    }
    
    @Override
    public ASTNode visitCreateSequence(final CreateSequenceContext ctx) {
        GaussDBCreateSequenceStatement result = new GaussDBCreateSequenceStatement();
        result.setSequenceName(((SimpleTableSegment) visit(ctx.qualifiedName())).getTableName().getIdentifier().getValue());
        return result;
    }
    
    @Override
    public ASTNode visitAlterSequence(final AlterSequenceContext ctx) {
        GaussDBAlterSequenceStatement result = new GaussDBAlterSequenceStatement();
        result.setSequenceName(((SimpleTableSegment) visit(ctx.qualifiedName())).getTableName().getIdentifier().getValue());
        return result;
    }
    
    @SuppressWarnings({"unchecked", "rawtypes"})
    @Override
    public ASTNode visitDropSequence(final DropSequenceContext ctx) {
        GaussDBDropSequenceStatement result = new GaussDBDropSequenceStatement();
        result.setSequenceNames(((CollectionValue) visit(ctx.qualifiedNameList())).getValue());
        return result;
    }
    
    @Override
    public ASTNode visitDropSynonym(final DropSynonymContext ctx) {
        return new GaussDBDropSynonymStatement();
    }
    
    @Override
    public ASTNode visitDropType(final DropTypeContext ctx) {
        return new GaussDBDropTypeStatement();
    }
    
    @Override
    public ASTNode visitDropDirectory(final DropDirectoryContext ctx) {
        return new GaussDBDropDirectoryStatement();
    }
    
    @Override
    public ASTNode visitPrepare(final PrepareContext ctx) {
        GaussDBPrepareStatement result = new GaussDBPrepareStatement();
        if (null != ctx.preparableStmt().select()) {
            result.setSelect((SelectStatement) visit(ctx.preparableStmt().select()));
        }
        if (null != ctx.preparableStmt().insert()) {
            result.setInsert((InsertStatement) visit(ctx.preparableStmt().insert()));
        }
        if (null != ctx.preparableStmt().update()) {
            result.setUpdate((UpdateStatement) visit(ctx.preparableStmt().update()));
        }
        if (null != ctx.preparableStmt().delete()) {
            result.setDelete((DeleteStatement) visit(ctx.preparableStmt().delete()));
        }
        return result;
    }
    
    @Override
    public ASTNode visitDeallocate(final DeallocateContext ctx) {
        return new GaussDBDeallocateStatement();
    }
    
    @Override
    public ASTNode visitCreateSynonym(final CreateSynonymContext ctx) {
        return new GaussDBCreateSynonymStatement();
    }
    
    @Override
    public ASTNode visitCreateAggregate(final CreateAggregateContext ctx) {
        return new GaussDBCreateAggregateStatement();
    }
    
    @Override
    public ASTNode visitCreatePublication(final CreatePublicationContext ctx) {
        return new GaussDBCreatePublicationStatement();
    }
    
    @Override
    public ASTNode visitCreateDirectory(final CreateDirectoryContext ctx) {
        return new GaussDBCreateDirectoryStatement();
    }
    
    @Override
    public ASTNode visitCreateTablespace(final CreateTablespaceContext ctx) {
        TablespaceSegment tablespaceSegment = createTablespaceSegment(ctx.name().identifier());
        GaussDBCreateTablespaceStatement result = new GaussDBCreateTablespaceStatement();
        result.setTablespaceSegment(tablespaceSegment);
        return result;
    }

    private TablespaceSegment createTablespaceSegment(final IdentifierContext ctx) {
        return new TablespaceSegment(ctx.getStart().getStartIndex(), ctx.getStop().getStopIndex(), (IdentifierValue) visit(ctx));
    }
    
    @Override
    public ASTNode visitAlterTablespace(final AlterTablespaceContext ctx) {
        return new GaussDBAlterTablespaceStatement();
    }
    
    @Override
    public ASTNode visitDropTablespace(final DropTablespaceContext ctx) {
        return new GaussDBDropTablespaceStatement();
    }
    
    @Override
    public ASTNode visitDropDomain(final DropDomainContext ctx) {
        return new GaussDBDropDomainStatement();
    }
    
    @Override
    public ASTNode visitCreateDomain(final CreateDomainContext ctx) {
        return new GaussDBCreateDomainStatement();
    }
    
    @Override
    public ASTNode visitCreateRule(final CreateRuleContext ctx) {
        return new GaussDBCreateRuleStatement();
    }
    
    @Override
    public ASTNode visitCreateLanguage(final CreateLanguageContext ctx) {
        return new GaussDBCreateLanguageStatement();
    }
    
    @Override
    public ASTNode visitCreateSchema(final CreateSchemaContext ctx) {
        GaussDBCreateSchemaStatement result = new GaussDBCreateSchemaStatement();
        if (null != ctx.createSchemaClauses().colId()) {
            result.setSchemaName(new IdentifierValue(ctx.createSchemaClauses().colId().getText()));
        }
        if (null != ctx.createSchemaClauses().roleSpec() && null != ctx.createSchemaClauses().roleSpec().identifier()) {
            result.setUsername((IdentifierValue) visit(ctx.createSchemaClauses().roleSpec().identifier()));
        }
        return result;
    }
    
    @Override
    public ASTNode visitAlterSchema(final AlterSchemaContext ctx) {
        GaussDBAlterSchemaStatement result = new GaussDBAlterSchemaStatement();
        result.setSchemaName((IdentifierValue) visit(ctx.name().get(0)));
        if (ctx.name().size() > 1) {
            result.setRenameSchema((IdentifierValue) visit(ctx.name().get(1)));
        }
        return result;
    }
    
    @SuppressWarnings("unchecked")
    @Override
    public ASTNode visitDropSchema(final DropSchemaContext ctx) {
        GaussDBDropSchemaStatement result = new GaussDBDropSchemaStatement();
        result.getSchemaNames().addAll(((CollectionValue<IdentifierValue>) visit(ctx.nameList())).getValue());
        result.setContainsCascade(null != ctx.dropBehavior() && null != ctx.dropBehavior().CASCADE());
        return result;
    }
    
    @SuppressWarnings("unchecked")
    @Override
    public ASTNode visitNameList(final NameListContext ctx) {
        CollectionValue<IdentifierValue> result = new CollectionValue<>();
        if (null != ctx.nameList()) {
            result.combine((CollectionValue<IdentifierValue>) visit(ctx.nameList()));
        }
        if (null != ctx.name()) {
            result.getValue().add((IdentifierValue) visit(ctx.name()));
        }
        return result;
    }
    
    @Override
    public ASTNode visitAlterLanguage(final AlterLanguageContext ctx) {
        return new GaussDBAlterLanguageStatement();
    }
    
    @Override
    public ASTNode visitAlterSynonym(final AlterSynonymContext ctx) {
        return new GaussDBAlterSynonymStatement();
    }
    
    @Override
    public ASTNode visitAlterDirectory(final AlterDirectoryContext ctx) {
        return new GaussDBAlterDirectoryStatement();
    }
    
    @Override
    public ASTNode visitAlterRule(final AlterRuleContext ctx) {
        return new GaussDBAlterRuleStatement();
    }
    
    @Override
    public ASTNode visitAlterType(final AlterTypeContext ctx) {
        return new GaussDBAlterTypeStatement();
    }
    
    @Override
    public ASTNode visitDropLanguage(final DropLanguageContext ctx) {
        return new GaussDBDropLanguageStatement();
    }
    
    @Override
    public ASTNode visitCreateConversion(final CreateConversionContext ctx) {
        return new GaussDBCreateConversionStatement();
    }
    
    @Override
    public ASTNode visitCreateCast(final CreateCastContext ctx) {
        return new GaussDBCreateCastStatement();
    }
    
    @Override
    public ASTNode visitCreateType(final CreateTypeContext ctx) {
        return new GaussDBCreateTypeStatement();
    }
    
    @Override
    public ASTNode visitDropConversion(final DropConversionContext ctx) {
        return new GaussDBDropConversionStatement();
    }
    
    @Override
    public ASTNode visitAlterConversion(final AlterConversionContext ctx) {
        return new GaussDBAlterConversionStatement();
    }
    
    @Override
    public ASTNode visitCreateTextSearch(final CreateTextSearchContext ctx) {
        return new GaussDBCreateTextSearchStatement();
    }
    
    @Override
    public ASTNode visitAlterTextSearchDictionary(final AlterTextSearchDictionaryContext ctx) {
        return new GaussDBAlterTextSearchStatement();
    }
    
    @Override
    public ASTNode visitAlterTextSearchTemplate(final AlterTextSearchTemplateContext ctx) {
        return new GaussDBAlterTextSearchStatement();
    }
    
    @Override
    public ASTNode visitAlterTextSearchParser(final AlterTextSearchParserContext ctx) {
        return new GaussDBAlterTextSearchStatement();
    }
    
    @Override
    public ASTNode visitCreateExtension(final CreateExtensionContext ctx) {
        return new GaussDBCreateExtensionStatement();
    }
    
    @Override
    public ASTNode visitAlterExtension(final AlterExtensionContext ctx) {
        return new GaussDBAlterExtensionStatement();
    }
    
    @Override
    public ASTNode visitDropExtension(final DropExtensionContext ctx) {
        return new GaussDBDropExtensionStatement();
    }
    
    @Override
    public ASTNode visitDeclare(final DeclareContext ctx) {
        GaussDBDeclareStatement result = new GaussDBDeclareStatement();
        result.setCursorName((CursorNameSegment) visit(ctx.cursorName()));
        result.setSelect((SelectStatement) visit(ctx.select()));
        return result;
    }
    
    @Override
    public ASTNode visitComment(final CommentContext ctx) {
        if (null != ctx.commentClauses().objectTypeAnyName() && null != ctx.commentClauses().objectTypeAnyName().TABLE()) {
            return commentOnTable(ctx);
        }
        if (null != ctx.commentClauses().COLUMN()) {
            return commentOnColumn(ctx);
        }
        return new GaussDBCommentStatement();
    }
    
    @SuppressWarnings("unchecked")
    private GaussDBCommentStatement commentOnColumn(final CommentContext ctx) {
        GaussDBCommentStatement result = new GaussDBCommentStatement();
        Iterator<NameSegment> nameSegmentIterator = ((CollectionValue<NameSegment>) visit(ctx.commentClauses().anyName())).getValue().iterator();
        Optional<NameSegment> columnName = nameSegmentIterator.hasNext() ? Optional.of(nameSegmentIterator.next()) : Optional.empty();
        columnName.ifPresent(optional -> result.setColumn(new ColumnSegment(optional.getStartIndex(), optional.getStopIndex(), optional.getIdentifier())));
        result.setComment(new IdentifierValue(ctx.commentClauses().commentText().getText()));
        setTableSegment(result, nameSegmentIterator);
        return result;
    }
    
    @SuppressWarnings("unchecked")
    private GaussDBCommentStatement commentOnTable(final CommentContext ctx) {
        GaussDBCommentStatement result = new GaussDBCommentStatement();
        Iterator<NameSegment> nameSegmentIterator = ((CollectionValue<NameSegment>) visit(ctx.commentClauses().anyName())).getValue().iterator();
        result.setComment(new IdentifierValue(ctx.commentClauses().commentText().getText()));
        setTableSegment(result, nameSegmentIterator);
        return result;
    }
    
    private void setTableSegment(final GaussDBCommentStatement statement, final Iterator<NameSegment> nameSegmentIterator) {
        Optional<NameSegment> tableName = nameSegmentIterator.hasNext() ? Optional.of(nameSegmentIterator.next()) : Optional.empty();
        tableName.ifPresent(optional -> statement.setTable(new SimpleTableSegment(new TableNameSegment(optional.getStartIndex(), optional.getStopIndex(), optional.getIdentifier()))));
        Optional<NameSegment> schemaName = nameSegmentIterator.hasNext() ? Optional.of(nameSegmentIterator.next()) : Optional.empty();
        schemaName.ifPresent(optional -> statement.getTable().setOwner(new OwnerSegment(optional.getStartIndex(), optional.getStopIndex(), optional.getIdentifier())));
        Optional<NameSegment> databaseName = nameSegmentIterator.hasNext() ? Optional.of(nameSegmentIterator.next()) : Optional.empty();
        databaseName.ifPresent(optional -> statement.getTable().getOwner()
                .ifPresent(owner -> owner.setOwner(new OwnerSegment(optional.getStartIndex(), optional.getStopIndex(), optional.getIdentifier()))));
    }
    
    @Override
    public ASTNode visitCursor(final CursorContext ctx) {
        GaussDBCursorStatement result = new GaussDBCursorStatement();
        result.setCursorName((CursorNameSegment) visit(ctx.cursorName()));
        result.setSelect((SelectStatement) visit(ctx.select()));
        return result;
    }
    
    @Override
    public ASTNode visitCursorName(final CursorNameContext ctx) {
        return new CursorNameSegment(ctx.start.getStartIndex(), ctx.stop.getStopIndex(), (IdentifierValue) visit(ctx.name()));
    }
    
    @Override
    public ASTNode visitClose(final CloseContext ctx) {
        GaussDBCloseStatement result = new GaussDBCloseStatement();
        if (null != ctx.cursorName()) {
            result.setCursorName((CursorNameSegment) visit(ctx.cursorName()));
        }
        result.setCloseAll(null != ctx.ALL());
        return result;
    }
    
    @Override
    public ASTNode visitMove(final MoveContext ctx) {
        GaussDBMoveStatement result = new GaussDBMoveStatement();
        result.setCursorName((CursorNameSegment) visit(ctx.cursorName()));
        if (null != ctx.direction()) {
            result.setDirection((DirectionSegment) visit(ctx.direction()));
        }
        return result;
    }
    
    @Override
    public ASTNode visitFetch(final FetchContext ctx) {
        GaussDBFetchStatement result = new GaussDBFetchStatement();
        result.setCursorName((CursorNameSegment) visit(ctx.cursorName()));
        if (null != ctx.direction()) {
            result.setDirection((DirectionSegment) visit(ctx.direction()));
        }
        return result;
    }
    
    @Override
    public ASTNode visitNext(final NextContext ctx) {
        return new DirectionSegment(ctx.start.getStartIndex(), ctx.stop.getStopIndex(), DirectionType.NEXT);
    }
    
    @Override
    public ASTNode visitPrior(final PriorContext ctx) {
        return new DirectionSegment(ctx.start.getStartIndex(), ctx.stop.getStopIndex(), DirectionType.PRIOR);
    }
    
    @Override
    public ASTNode visitFirst(final FirstContext ctx) {
        return new DirectionSegment(ctx.start.getStartIndex(), ctx.stop.getStopIndex(), DirectionType.FIRST);
    }
    
    @Override
    public ASTNode visitLast(final LastContext ctx) {
        return new DirectionSegment(ctx.start.getStartIndex(), ctx.stop.getStopIndex(), DirectionType.LAST);
    }
    
    @Override
    public ASTNode visitAbsoluteCount(final AbsoluteCountContext ctx) {
        return new DirectionSegment(ctx.start.getStartIndex(), ctx.stop.getStopIndex(), DirectionType.ABSOLUTE_COUNT, ((NumberLiteralValue) visit(ctx.signedIconst())).getValue().longValue());
    }
    
    @Override
    public ASTNode visitRelativeCount(final RelativeCountContext ctx) {
        return new DirectionSegment(ctx.start.getStartIndex(), ctx.stop.getStopIndex(), DirectionType.RELATIVE_COUNT, ((NumberLiteralValue) visit(ctx.signedIconst())).getValue().longValue());
    }
    
    @Override
    public ASTNode visitCount(final CountContext ctx) {
        return new DirectionSegment(ctx.start.getStartIndex(), ctx.stop.getStopIndex(), DirectionType.COUNT, ((NumberLiteralValue) visit(ctx.signedIconst())).getValue().longValue());
    }
    
    @Override
    public ASTNode visitAll(final AllContext ctx) {
        return new DirectionSegment(ctx.start.getStartIndex(), ctx.stop.getStopIndex(), DirectionType.ALL);
    }
    
    @Override
    public ASTNode visitForward(final ForwardContext ctx) {
        return new DirectionSegment(ctx.start.getStartIndex(), ctx.stop.getStopIndex(), DirectionType.FORWARD);
    }
    
    @Override
    public ASTNode visitForwardCount(final ForwardCountContext ctx) {
        return new DirectionSegment(ctx.start.getStartIndex(), ctx.stop.getStopIndex(), DirectionType.FORWARD_COUNT, ((NumberLiteralValue) visit(ctx.signedIconst())).getValue().longValue());
    }
    
    @Override
    public ASTNode visitForwardAll(final ForwardAllContext ctx) {
        return new DirectionSegment(ctx.start.getStartIndex(), ctx.stop.getStopIndex(), DirectionType.FORWARD_ALL);
    }
    
    @Override
    public ASTNode visitBackward(final BackwardContext ctx) {
        return new DirectionSegment(ctx.start.getStartIndex(), ctx.stop.getStopIndex(), DirectionType.BACKWARD);
    }
    
    @Override
    public ASTNode visitBackwardCount(final BackwardCountContext ctx) {
        return new DirectionSegment(ctx.start.getStartIndex(), ctx.stop.getStopIndex(), DirectionType.BACKWARD_COUNT, ((NumberLiteralValue) visit(ctx.signedIconst())).getValue().longValue());
    }
    
    @Override
    public ASTNode visitBackwardAll(final BackwardAllContext ctx) {
        return new DirectionSegment(ctx.start.getStartIndex(), ctx.stop.getStopIndex(), DirectionType.BACKWARD_ALL);
    }
}
