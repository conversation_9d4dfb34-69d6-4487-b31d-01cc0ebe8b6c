
package com.dc.summer.model.gis;

import com.dc.summer.model.exec.DBCException;
import com.dc.summer.model.runtime.DBRProgressMonitor;
import com.dc.code.NotNull;
import com.dc.code.Nullable;

/**
 * GisAttribute.
*/
public interface GisAttribute {
    int getAttributeGeometrySRID(DBRProgressMonitor monitor) throws DBCException;

    @NotNull
    DBGeometryDimension getAttributeGeometryDimension(DBRProgressMonitor monitor) throws DBCException;

    @Nullable
    String getAttributeGeometryType(DBRProgressMonitor monitor) throws DBCException;
}
