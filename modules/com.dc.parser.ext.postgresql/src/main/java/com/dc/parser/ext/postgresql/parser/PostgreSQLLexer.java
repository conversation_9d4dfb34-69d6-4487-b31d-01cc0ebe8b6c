
package com.dc.parser.ext.postgresql.parser;

import com.dc.parser.ext.postgresql.parser.autogen.PostgreSQLStatementLexer;
import org.antlr.v4.runtime.CharStream;
import com.dc.parser.model.api.parser.SQLLexer;

/**
 * SQL lexer for PostgreSQL.
 */
public final class PostgreSQLLexer extends PostgreSQLStatementLexer implements SQLLexer {
    
    public PostgreSQLLexer(final CharStream input) {
        super(input);
    }
}
