/*
 * DBeaver - Universal Database Manager
 * Copyright (C) 2010-2022 DBeaver Corp and others
 * Copyright (C) 2019 <PERSON><PERSON><PERSON><PERSON> (<EMAIL>)
 * Copyright (C) 2019 <PERSON> (<EMAIL>)
 * Copyright (C) 2019 <PERSON> (<EMAIL>)
 * Copyright (C) 2019 <PERSON><PERSON> (<EMAIL>)
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.dc.summer.ext.greenplum.model;

import com.dc.annotation.SQL;
import com.dc.code.NotNull;
import com.dc.summer.DBException;
import com.dc.summer.Log;
import com.dc.summer.ext.postgresql.PostgreUtils;
import com.dc.summer.model.DBPDataSourceContainer;
import com.dc.summer.model.DBPDataSourceInfo;
import com.dc.summer.model.DBUtils;
import com.dc.summer.model.exec.jdbc.JDBCDatabaseMetaData;
import com.dc.summer.model.exec.jdbc.JDBCSession;
import com.dc.summer.model.impl.jdbc.JDBCUtils;
import com.dc.summer.model.runtime.DBRProgressMonitor;
import com.dc.summer.ext.postgresql.model.PostgreDataSource;
import com.dc.summer.model.sql.SQLState;
import org.osgi.framework.Version;

import java.sql.SQLException;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class GreenplumDataSource extends PostgreDataSource {

    private static final Log log = Log.getLog(GreenplumDataSource.class);

    private Version gpVersion;
    private Boolean supportsFmterrtblColumn;

    public GreenplumDataSource(DBRProgressMonitor monitor, DBPDataSourceContainer container) throws DBException {
        super(monitor, container);
    }

    public boolean isGreenplumVersionAtLeast(DBRProgressMonitor monitor, int major, int minor) {
        if (gpVersion == null) {
            try (JDBCSession session = DBUtils.openMetaSession(monitor, this, "Read Greenplum server version")) {
                String versionStr = JDBCUtils.queryString(session, "SELECT VERSION()");
                if (versionStr != null) {
                    Matcher matcher = Pattern.compile("Greenplum Database ([0-9\\.]+)").matcher(versionStr);
                    if (matcher.find()) {
                        gpVersion = new Version(matcher.group(1));
                    }
                }
            } catch (Throwable e) {
                log.debug("Error reading GP server version", e);
            }
            if (gpVersion == null) {
                gpVersion = new Version(4, 2, 0);
            }
        }

        if (gpVersion.getMajor() < major) {
            return false;
        } else if (gpVersion.getMajor() == major && gpVersion.getMinor() < minor) {
            return false;
        }
        return true;
    }

    @Override
    protected DBPDataSourceInfo createDataSourceInfo(DBRProgressMonitor monitor, JDBCDatabaseMetaData metaData) {
        return new GreenplumDataSourceInfo(this, metaData);
    }

    @Override
    public ErrorType discoverErrorType(Throwable error) {
        String sqlState = SQLState.getStateFromException(error);
        if (error.getMessage().contains("I/O error") && SQLState.SQL_08006.getCode().equals(sqlState)) {
            return ErrorType.NORMAL;
        }
        return super.discoverErrorType(error);
    }

    boolean isServerSupportFmterrtblColumn(@NotNull JDBCSession session, String prefix) {
        if (supportsFmterrtblColumn == null) {
            try {
                @SQL String sql = PostgreUtils.getQueryForSystemColumnChecking("@_exttable", "fmterrtbl");
                JDBCUtils.queryString(
                        session,
                        sql.replace("@", prefix));
                supportsFmterrtblColumn = true;
            } catch (SQLException e) {
                log.debug("Error reading system information from the pg_exttable table", e);
                supportsFmterrtblColumn = false;
            }
        }
        return supportsFmterrtblColumn;
    }
}
