package com.dc.summer.ext.oracle.data;

import com.dc.summer.Log;
import com.dc.summer.ext.oracle.model.OracleConstants;
import com.dc.summer.ext.oracle.model.OracleDataSource;
import com.dc.summer.ext.oracle.model.OracleSchema;
import com.dc.summer.model.exec.DBCSession;
import com.dc.summer.model.exec.jdbc.JDBCPreparedStatement;
import com.dc.summer.model.exec.jdbc.JDBCResultSet;
import com.dc.summer.model.exec.jdbc.JDBCSession;
import com.dc.summer.model.impl.jdbc.data.handlers.JDBCStringValueHandler;
import com.dc.summer.model.struct.DBSTypedObject;

import java.nio.charset.StandardCharsets;
import java.sql.SQLException;

/**
 * 处理oracle中文字符乱码handler
 */
public class OracleVarcharValue<PERSON>and<PERSON> extends JDBCStringValueHandler {

    private static final Log log = Log.getLog(OracleVarcharValueHandler.class);
    public static final OracleVarcharValueHandler INSTANCE = new OracleVarcharValueHandler();

    /**
     * 对从oracle查出的结果集，针对字符串类型进行转码
     *
     * @param session
     * @param resultSet
     * @param type
     * @param index
     * @return
     * @throws SQLException
     */
    @Override
    protected Object fetchColumnValue(
            DBCSession session,
            JDBCResultSet resultSet,
            DBSTypedObject type,
            int index)
            throws SQLException {
        try {
            if (OracleConstants.SERVER_NLS_CHARACTERSET_US7ASCII.equals(getDataSourceCharset(session))) {
                byte[] bytes = resultSet.getBytes(index);
                String result;
                if (bytes == null || bytes.length == 0) {
                    result = null;
                } else {
                    result = new String(bytes, "GBK");
                }
                return result;
            } else {
                return super.fetchColumnValue(session, resultSet, type, index);
            }
        } catch (Exception e) {
            log.error("解析中文参数时，转码出错",e);
            return null;
        }
    }

    @Override
    public void bindParameter(JDBCSession session, JDBCPreparedStatement statement, DBSTypedObject paramType,
                              int paramIndex, Object value)
            throws SQLException {
        try {
            if (OracleConstants.SERVER_NLS_CHARACTERSET_US7ASCII.equals(getDataSourceCharset(session))) {
                if (value == null) {
                    statement.setNull(paramIndex, paramType.getTypeID());
                } else {
                    statement.setString(paramIndex, new String(value.toString().getBytes("GBK"), StandardCharsets.ISO_8859_1));
                }
            } else {
                super.bindParameter(session, statement, paramType, paramIndex, value);
            }
        } catch (Exception e) {
            log.error("绑定中文参数时，转码出错",e);
        }
    }

    private static String getDataSourceCharset(DBCSession session) {
        if (session.getDataSource() instanceof OracleDataSource) {
            return ((OracleDataSource) session.getDataSource()).getCharset();
        }
        return null;
    }

}
