
package com.dc.summer.ext.mysql.model;

import com.dc.function.RuntimeRunnable;
import com.dc.summer.DBException;
import com.dc.summer.Log;
import com.dc.summer.ModelPreferences;
import com.dc.summer.ext.mysql.MySQLConstants;
import com.dc.summer.ext.mysql.MySQLDataSourceProvider;
import com.dc.summer.model.*;
import com.dc.summer.model.admin.sessions.DBAServerSessionManager;
import com.dc.summer.model.app.DBACertificateStorage;
import com.dc.summer.model.connection.DBPDriver;
import com.dc.summer.model.exec.*;
import com.dc.summer.model.exec.jdbc.*;
import com.dc.summer.model.exec.plan.DBCQueryPlanner;
import com.dc.summer.model.gis.GisConstants;
import com.dc.summer.model.gis.SpatialDataProvider;
import com.dc.summer.model.impl.jdbc.*;
import com.dc.summer.model.impl.jdbc.cache.JDBCObjectCache;
import com.dc.summer.model.impl.jdbc.struct.JDBCDataType;
import com.dc.summer.model.impl.sql.QueryTransformerLimit;
import com.dc.summer.model.net.DBWHandlerConfiguration;
import com.dc.summer.model.runtime.DBRProgressMonitor;
import com.dc.summer.model.runtime.VoidProgressMonitor;
import com.dc.summer.model.sql.SQLDialect;
import com.dc.summer.model.sql.SQLHelpProvider;
import com.dc.summer.model.struct.DBSDataType;
import com.dc.summer.model.struct.DBSObjectFilter;
import com.dc.summer.model.struct.DBSStructureAssistant;
import com.dc.code.NotNull;
import com.dc.code.Nullable;
import com.dc.summer.ext.mysql.model.plan.MySQLPlanAnalyser;
import com.dc.summer.ext.mysql.model.session.MySQLSessionManager;
import com.dc.summer.model.connection.DBPConnectionConfiguration;
import com.dc.summer.model.impl.jdbc.cache.JDBCBasicDataTypeCache;
import com.dc.summer.model.impl.net.SSLHandlerTrustStoreImpl;
import com.dc.summer.model.sql.SQLState;
import com.dc.summer.model.struct.DBSObject;
import com.dc.summer.runtime.DBWorkbench;
import com.dc.utils.CommonUtils;
import com.dc.utils.IOUtils;

import java.io.File;
import java.net.MalformedURLException;
import java.sql.Connection;
import java.sql.Driver;
import java.sql.SQLException;
import java.sql.Types;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * GenericDataSource
 */
public class MySQLDataSource extends JDBCDataSource implements DBPObjectStatisticsCollector {
    private static final Log log = Log.getLog(MySQLDataSource.class);

    private final JDBCBasicDataTypeCache<MySQLDataSource, JDBCDataType> dataTypeCache;
    private List<MySQLEngine> engines = new ArrayList<>();
    private final CatalogCache catalogCache = new CatalogCache();
    private List<MySQLPrivilege> privileges;
    private List<MySQLUser> users;
    private List<MySQLCharset> charsets = new ArrayList<>();
    private List<MySQLPlugin> plugins;
    private Map<String, MySQLCollation> collations = new HashMap<>();
    private String defaultCharset;
    private String defaultCollation;
    private Integer lowerCaseTableNames;
    private SQLHelpProvider helpProvider;
    private volatile boolean hasStatistics;
    private Boolean containsCheckConstraintTable = false;

    private boolean inServerTimezoneHandle;

    public MySQLDataSource(DBRProgressMonitor monitor, DBPDataSourceContainer container)
            throws DBException {
        this(monitor, container, new MySQLDialect());
    }

    protected MySQLDataSource(DBRProgressMonitor monitor, DBPDataSourceContainer container, SQLDialect dialect) throws DBException {
        super(monitor, container, dialect);
        dataTypeCache = new JDBCBasicDataTypeCache<>(this);
        hasStatistics = !container.getPreferenceStore().getBoolean(ModelPreferences.READ_EXPENSIVE_STATISTICS);
    }

    @Override
    public Object getDataSourceFeature(String featureId) {
        switch (featureId) {
            case DBPDataSource.FEATURE_MAX_STRING_LENGTH:
                if (isServerVersionAtLeast(5, 0)) {
                    return 65535;
                } else {
                    return 255;
                }
        }
        return super.getDataSourceFeature(featureId);
    }

    int getLowerCaseTableNames() {
        if (lowerCaseTableNames == null) {
            lowerCaseTableNames = 1;
            try (JDBCSession session = DBUtils.openMetaSession(new VoidProgressMonitor(), this, "Load LowerCaseTableNames")) {
                try (JDBCPreparedStatement dbStat = session.prepareStatement("SHOW VARIABLES LIKE 'lower_case_table_names'")) {
                    try (JDBCResultSet dbResult = dbStat.executeQuery()) {
                        if (dbResult.next()) {
                            lowerCaseTableNames = JDBCUtils.safeGetInt(dbResult, 2);
                        }
                    }
                } catch (Throwable ex) {
                    log.debug("Error reading default server charset/collation", ex);
                }
            } catch (Exception ignored) {
            }
        }
        return lowerCaseTableNames;
    }

    @Override
    protected Map<String, String> getInternalConnectionProperties(DBRProgressMonitor monitor, DBPDriver driver, JDBCExecutionContext context, String purpose, DBPConnectionConfiguration connectionInfo)
            throws DBCException {
        Map<String, String> props = new LinkedHashMap<>(MySQLDataSourceProvider.getConnectionsProps());
        final DBWHandlerConfiguration sslConfig = getContainer().getActualConnectionConfiguration().getHandler(MySQLConstants.HANDLER_SSL);
        if (sslConfig != null && sslConfig.isEnabled()) {
            try {
                initSSL(monitor, props, sslConfig);
            } catch (Exception e) {
                throw new DBCException("Error configuring SSL certificates", e);
            }
        } else {
            // Newer MySQL servers/connectors requires explicit SSL disable
            props.put("useSSL", "false");
        }

        String serverTZ = connectionInfo.getProviderProperty(MySQLConstants.PROP_SERVER_TIMEZONE);
        if (CommonUtils.isEmpty(serverTZ) && inServerTimezoneHandle/*&& getContainer().getDriver().getId().equals(MySQLConstants.DRIVER_ID_MYSQL8)*/) {
            serverTZ = "UTC";
        }
        if (!CommonUtils.isEmpty(serverTZ)) {
            props.put("serverTimezone", serverTZ);
        }

        if (!isMariaDB()) {
            // Hacking different MySQL drivers zeroDateTimeBehavior property (#4103)
            String zeroDateTimeBehavior = connectionInfo.getProperty(MySQLConstants.PROP_ZERO_DATETIME_BEHAVIOR);
            if (zeroDateTimeBehavior == null) {
                try {
                    Driver driverInstance = driver.getDriverInstance(monitor);
                    if (driverInstance != null) {
                        if (driverInstance.getMajorVersion() >= 8) {
                            props.put(MySQLConstants.PROP_ZERO_DATETIME_BEHAVIOR, "CONVERT_TO_NULL");
                        } else {
                            props.put(MySQLConstants.PROP_ZERO_DATETIME_BEHAVIOR, "convertToNull");
                        }
                    }
                } catch (Exception e) {
                    log.debug("Error setting MySQL " + MySQLConstants.PROP_ZERO_DATETIME_BEHAVIOR + " property default");
                }
            }
        }

        return props;
    }

    @Override
    protected DBPDataSourceInfo createDataSourceInfo(DBRProgressMonitor monitor, @NotNull JDBCDatabaseMetaData metaData) {
        return new MySQLDataSourceInfo(metaData);
    }

    private void initSSL(DBRProgressMonitor monitor, Map<String, String> props, DBWHandlerConfiguration sslConfig) throws Exception {
        monitor.subTask("Install SSL certificates");
        final DBACertificateStorage securityManager = DBWorkbench.getPlatform().getCertificateStorage();

        props.put("useSSL", "true");
        if (isMariaDB()) {
            props.put("trustServerCertificate", String.valueOf(!sslConfig.getBooleanProperty(MySQLConstants.PROP_VERIFY_SERVER_SERT)));
        } else {
            props.put("verifyServerCertificate", sslConfig.getStringProperty(MySQLConstants.PROP_VERIFY_SERVER_SERT));
            props.put("requireSSL", sslConfig.getStringProperty(MySQLConstants.PROP_REQUIRE_SSL));
        }

        final String caCertProp;
        final String clientCertProp;
        final String clientCertKeyProp;

        if (CommonUtils.isEmpty(sslConfig.getStringProperty(SSLHandlerTrustStoreImpl.PROP_SSL_METHOD))) {
            // Backward compatibility
            caCertProp = sslConfig.getStringProperty(MySQLConstants.PROP_SSL_CA_CERT);
            clientCertProp = sslConfig.getStringProperty(MySQLConstants.PROP_SSL_CLIENT_CERT);
            clientCertKeyProp = sslConfig.getStringProperty(MySQLConstants.PROP_SSL_CLIENT_KEY);
        } else {
            caCertProp = sslConfig.getStringProperty(SSLHandlerTrustStoreImpl.PROP_SSL_CA_CERT);
            clientCertProp = sslConfig.getStringProperty(SSLHandlerTrustStoreImpl.PROP_SSL_CLIENT_CERT);
            clientCertKeyProp = sslConfig.getStringProperty(SSLHandlerTrustStoreImpl.PROP_SSL_CLIENT_KEY);
        }

        {
            // Trust keystore
            if (!CommonUtils.isEmpty(caCertProp) || !CommonUtils.isEmpty(clientCertProp)) {
                byte[] caCertData = CommonUtils.isEmpty(caCertProp) ? null : IOUtils.readFileToBuffer(new File(caCertProp));
                byte[] clientCertData = CommonUtils.isEmpty(clientCertProp) ? null : IOUtils.readFileToBuffer(new File(clientCertProp));
                byte[] keyData = CommonUtils.isEmpty(clientCertKeyProp) ? null : IOUtils.readFileToBuffer(new File(clientCertKeyProp));
                securityManager.addCertificate(getContainer(), "ssl", caCertData, clientCertData, keyData);
            } else {
                securityManager.deleteCertificate(getContainer(), "ssl");
            }
            final String ksPath = makeKeyStorePath(securityManager.getKeyStorePath(getContainer(), "ssl"));
            final char[] ksPass = securityManager.getKeyStorePassword(getContainer(), "ssl");
            if (isMariaDB()) {
                props.put("trustStore", ksPath);
                props.put("trustStorePassword", String.valueOf(ksPass));
            } else {
                props.put("clientCertificateKeyStoreUrl", ksPath);
                props.put("trustCertificateKeyStoreUrl", ksPath);
                props.put("clientCertificateKeyStorePassword", String.valueOf(ksPass));
                props.put("trustCertificateKeyStorePassword", String.valueOf(ksPass));
            }
        }
        final String cipherSuites = sslConfig.getStringProperty(MySQLConstants.PROP_SSL_CIPHER_SUITES);
        if (!CommonUtils.isEmpty(cipherSuites)) {
            props.put("enabledSSLCipherSuites;", cipherSuites);
        }
        final boolean retrievePublicKey = sslConfig.getBooleanProperty(MySQLConstants.PROP_SSL_PUBLIC_KEY_RETRIEVE);
        if (retrievePublicKey) {
            props.put("allowPublicKeyRetrieval", "true");
        }

        if (sslConfig.getBooleanProperty(MySQLConstants.PROP_SSL_DEBUG)) {
            System.setProperty("javax.net.debug", "all");
        }
    }

    private String makeKeyStorePath(File keyStorePath) throws MalformedURLException {
        if (isMariaDB()) {
            return keyStorePath.getAbsolutePath();
        } else {
            return keyStorePath.toURI().toURL().toString();
        }
    }

    @Override
    protected JDBCExecutionContext createExecutionContext(JDBCRemoteInstance instance, String type) {
        return new MySQLExecutionContext(instance, type);
    }

    protected void initializeContextState(@NotNull DBRProgressMonitor monitor, @NotNull JDBCExecutionContext context, JDBCExecutionContext initFrom) throws DBException {
        if (initFrom != null && !context.getDataSource().getContainer().isConnectionReadOnly()) {
            MySQLCatalog object = ((MySQLExecutionContext) initFrom).getDefaultCatalog();
            if (object != null) {
                ((MySQLExecutionContext) context).setCurrentDatabase(monitor, object);
            }
        } else {
            ((MySQLExecutionContext) context).refreshDefaults(monitor, true);
        }
    }

    public String[] getTableTypes() {
        return MySQLConstants.TABLE_TYPES;
    }

    public CatalogCache getCatalogCache() {
        return catalogCache;
    }

    public Collection<MySQLCatalog> getCatalogs() {
        return catalogCache.getCachedObjects();
    }

    public MySQLCatalog getCatalog(String name) {
        return catalogCache.getCachedObject(name);
    }

    @Override
    public void initialize(@NotNull DBRProgressMonitor monitor)
            throws DBException {
        /*
        初始化父类，父类 JDBCDataSource 算是一个大杂烩，依然可以直接拷，丢弃一些用不到的功能即可
        JDBCDataSource 实现了：数据源、数据类型提供商、错误助手、自定义对象、对象容器、实例容器、查询转换提供商、Eclipse的适配器。
         */
        super.initialize(monitor);

        /*
         从数据类型缓存中拿到所有的对象。
         loadObjects 的过程中，执行 dbStat.executeStatement()，但是是 JDBCFakeStatementImpl 的实现类，并不真正查询。
         但是如同真正的查询一样，调用了结果集换转等等。
         */
        dataTypeCache.getAllObjects(monitor, this);
        if (isServerVersionAtLeast(5, 7) && dataTypeCache.getCachedObject(MySQLConstants.TYPE_JSON) == null) {
            // 把当前对象，缓存进 list 和 map 中。
            dataTypeCache.cacheObject(new JDBCDataType<>(this, java.sql.Types.OTHER, MySQLConstants.TYPE_JSON, MySQLConstants.TYPE_JSON, false, true, 0, 0, 0));
        }
        if (isMariaDB() && isServerVersionAtLeast(10, 7) && dataTypeCache.getCachedObject(MySQLConstants.TYPE_UUID) == null) {
            // Not supported by MariaDB driver for now (3.0.8). Waiting for the driver support
            dataTypeCache.cacheObject(
                    new JDBCDataType<>(
                            this,
                            Types.CHAR,
                            MySQLConstants.TYPE_UUID,
                            MySQLConstants.TYPE_UUID,
                            false,
                            true,
                            0,
                            0,
                            0));
        }

        // 从目录缓存中拿到所有的对象
        // Read catalogs
        catalogCache.getAllObjects(monitor, this);
        sqlDialect.getDataTypes(this);
        //activeCatalogName = MySQLUtils.determineCurrentDatabase(session);
    }

    @Override
    public DBSObject refreshObject(@NotNull DBRProgressMonitor monitor)
            throws DBException {
        super.refreshObject(monitor);

        this.engines = null;
        this.catalogCache.clearCache();
        this.users = null;

        this.initialize(monitor);

        return this;
    }

    MySQLTable findTable(DBRProgressMonitor monitor, String catalogName, String tableName)
            throws DBException {
        if (CommonUtils.isEmpty(catalogName)) {
            return null;
        }
        MySQLCatalog catalog = getCatalog(catalogName);
        if (catalog == null) {
            log.error("Catalog " + catalogName + " not found");
            return null;
        }
        return catalog.getTable(monitor, tableName);
    }

    @Override
    public Collection<? extends MySQLCatalog> getChildren(@NotNull DBRProgressMonitor monitor) {
        return getCatalogs();
    }

    @Override
    public MySQLCatalog getChild(@NotNull DBRProgressMonitor monitor, @NotNull String childName) {
        MySQLCatalog catalog = getCatalog(childName);
        return catalog == null ? new MySQLCatalog(this, null, childName) : catalog;
    }

    @NotNull
    @Override
    public Class<? extends MySQLCatalog> getPrimaryChildType(@Nullable DBRProgressMonitor monitor) {
        return MySQLCatalog.class;
    }

    @Override
    public void cacheStructure(@NotNull DBRProgressMonitor monitor, int scope) {

    }

    @Override
    protected Connection openConnection(@NotNull DBRProgressMonitor monitor, @Nullable JDBCExecutionContext context, @NotNull String purpose, DBPConnectionConfiguration configuration) throws DBCException {
        Connection mysqlConnection;
        try {
            // 调用父类的统一连接方式
            mysqlConnection = super.openConnection(monitor, context, purpose, configuration);
        } catch (DBCException e) {
            // 处理特殊情况产生的连接问题
            if (e.getCause() instanceof SQLException &&
                    SQLState.SQL_01S00.getCode().equals(((SQLException) e.getCause()).getSQLState()) &&
                    CommonUtils.isEmpty(getContainer().getActualConnectionConfiguration().getProviderProperty(MySQLConstants.PROP_SERVER_TIMEZONE))) {
                // MySQL 8驱动程序和服务器时区错误严重问题的解决方法
                // Workaround for nasty problem with MySQL 8 driver and serverTimezone error
                log.debug("Error connecting without serverTimezone. Trying to set serverTimezone=UTC. Original error: " + e.getMessage());
                inServerTimezoneHandle = true;
                try {
                    // 再次调用连接
                    mysqlConnection = super.openConnection(monitor, context, purpose, configuration);
                } catch (DBCException e2) {
                    inServerTimezoneHandle = false;
                    throw e2;
                }
            } else {
                throw e;
            }
        }
        // 如果没有禁用元客户端名称
        if (useMetaClientNameDisable() && !getContainer().getPreferenceStore().getBoolean(ModelPreferences.META_CLIENT_NAME_DISABLE)) {
            // 提供客户信息
            // Provide client info
            try {
                // 设置 ApplicationName = Main
                mysqlConnection.setClientInfo(JDBCConstants.APPLICATION_NAME_CLIENT_PROPERTY, DBUtils.getClientApplicationName(getContainer(), context, purpose));
            } catch (Throwable e) {
                // just ignore
                log.debug(e);
            }
        }

        return mysqlConnection;
    }

    protected boolean useMetaClientNameDisable() {
        return true;
    }

    public List<MySQLUser> getUsers(DBRProgressMonitor monitor)
            throws DBException {
        if (users == null) {
            users = loadUsers(monitor);
        }
        return users;
    }

    public MySQLUser getUser(DBRProgressMonitor monitor, String name)
            throws DBException {
        return DBUtils.findObject(getUsers(monitor), name);
    }

    private List<MySQLUser> loadUsers(DBRProgressMonitor monitor)
            throws DBException {
        try (JDBCSession session = DBUtils.openMetaSession(monitor, this, "Load Users")) {
            try (JDBCPreparedStatement dbStat = session.prepareStatement("SELECT * FROM mysql.user ORDER BY user")) {
                try (JDBCResultSet dbResult = dbStat.executeQuery()) {
                    List<MySQLUser> userList = new ArrayList<>();
                    while (dbResult.next()) {
                        MySQLUser user = new MySQLUser(this, dbResult);
                        userList.add(user);
                    }
                    return userList;
                }
            }
        } catch (SQLException ex) {
            throw new DBException(ex, this);
        }
    }

    public List<MySQLEngine> getEngines() throws DBCException {
        if (engines == null) {
            engines = new ArrayList<>();
            try (JDBCSession session = DBUtils.openMetaSession(new VoidProgressMonitor(), this, "Load Engines")) {
                try (JDBCPreparedStatement dbStat = session.prepareStatement("SHOW ENGINES")) {
                    try (JDBCResultSet dbResult = dbStat.executeQuery()) {
                        while (dbResult.next()) {
                            MySQLEngine engine = new MySQLEngine(this, dbResult);
                            engines.add(engine);
                        }
                    }
                } catch (SQLException ex) {
                    // Engines are not supported. Shame on it. Leave this list empty
                }
            }
        }
        return engines;
    }

    public MySQLEngine getEngine(String name) {
        if (CommonUtils.isEmpty(name)) {
            return null;
        }
        MySQLEngine engine = DBUtils.findObject(engines, name);
        return engine == null ? new MySQLEngine(this, name) : engine;
    }

    public MySQLEngine getDefaultEngine() {
        for (MySQLEngine engine : CommonUtils.safeCollection(engines)) {
            if (engine.getSupport() == MySQLEngine.Support.DEFAULT) {
                return engine;
            }
        }
        return null;
    }

    public Collection<MySQLCharset> getCharsets() throws DBCException {
        if (charsets == null) {
            // 字符
            charsets = new ArrayList<>();
            try (JDBCSession session = DBUtils.openMetaSession(new VoidProgressMonitor(), this, "Load Charsets")) {

                try (JDBCPreparedStatement dbStat = session.prepareStatement("SHOW CHARSET")) {
                    try (JDBCResultSet dbResult = dbStat.executeQuery()) {
                        while (dbResult.next()) {
                            MySQLCharset charset = new MySQLCharset(this, dbResult);
                            charsets.add(charset);
                        }
                    }
                } catch (SQLException ex) {
                    // Engines are not supported. Shame on it. Leave this list empty
                }
                charsets.sort(DBUtils.<MySQLCharset>nameComparator());
            }
        }
        return charsets;
    }

    public MySQLCharset getCharset(String name) {
        for (MySQLCharset charset : CommonUtils.safeCollection(charsets)) {
            if (charset.getName().equals(name)) {
                return charset;
            }
        }

        return new MySQLCharset(this, name);
    }

    public MySQLCollation getCollation(String name) throws DBCException {
        if (CommonUtils.isEmpty(name)) {
            return null;
        }
        if (collations == null) {
            // 字符集
            collations = new LinkedHashMap<>();
            try (JDBCSession session = DBUtils.openMetaSession(new VoidProgressMonitor(), this, "Load Collation")) {
                try (JDBCPreparedStatement dbStat = session.prepareStatement("SHOW COLLATION")) {
                    try (JDBCResultSet dbResult = dbStat.executeQuery()) {
                        while (dbResult.next()) {
                            String charsetName = JDBCUtils.safeGetString(dbResult, MySQLConstants.COL_CHARSET);
                            MySQLCharset charset = getCharset(charsetName);
                            if (charset == null) {
                                log.warn("Charset '" + charsetName + "' not found.");
                                continue;
                            }
                            MySQLCollation collation = new MySQLCollation(charset, dbResult);
                            collations.put(collation.getName(), collation);
                            charset.addCollation(collation);
                        }
                    }
                } catch (SQLException ex) {
                    // Engines are not supported. Shame on it. Leave this list empty
                }
            }
        }
        MySQLCollation collation = collations.get(name);
        if (collation == null) {
            try (JDBCSession session = DBUtils.openMetaSession(new VoidProgressMonitor(), this, "Get Collation")) {
                try (JDBCPreparedStatement dbStat = session.prepareStatement("SHOW COLLATION LIKE '" + name + "'")) {

                    try (JDBCResultSet dbResult = dbStat.executeQuery()) {
                        if (dbResult.next()) {
                            String charsetName = JDBCUtils.safeGetString(dbResult, MySQLConstants.COL_CHARSET);
                            MySQLCharset charset = getCharset(charsetName);
                            if (charset == null) {
                                log.warn("Charset '" + charsetName + "' not found.");
                            }
                            collation = new MySQLCollation(charset, dbResult);
                        }
                    }
                } catch (SQLException ex) {
                    // Engines are not supported. Shame on it. Leave this list empty
                }
            }
        }
        return collation;
    }

    public MySQLCollation getCollation(String collationName, String charsetName) throws DBCException {

        if (CommonUtils.isEmpty(collationName)) {
            return null;
        }
        MySQLCollation collation = collations.get(collationName);

        if (collation != null && collation.getCharset().getName().equals(charsetName)) {
            return collation;
        }

        MySQLCharset charset = getCharset(charsetName);
        if (charset == null) {
            log.warn("Charset '" + charsetName + "' not found.");
        }
        collation = new MySQLCollation(charset, collationName);
        return collation;
    }

    public MySQLCharset getDefaultCharset() throws DBCException {
        if (defaultCharset == null) {
            loadDefault();
        }
        return getCharset(defaultCharset);
    }

    public MySQLCollation getDefaultCollation() throws DBCException {
        if (defaultCollation == null) {
            loadDefault();
        }
        return getCollation(defaultCollation);
    }

    private void loadDefault() throws DBCException {
        // 默认值
        try (JDBCSession session = DBUtils.openMetaSession(new VoidProgressMonitor(), this, "Load Default")) {
            try (JDBCPreparedStatement dbStat = session.prepareStatement("SELECT @@GLOBAL.character_set_server,@@GLOBAL.collation_server")) {
                try (JDBCResultSet dbResult = dbStat.executeQuery()) {
                    if (dbResult.next()) {
                        defaultCharset = JDBCUtils.safeGetString(dbResult, 1);
                        defaultCollation = JDBCUtils.safeGetString(dbResult, 2);
                    }
                }
            } catch (Throwable ex) {
                log.debug("Error reading default server charset/collation", ex);
            }
        }
    }

    @NotNull
    public Collection<MySQLPlugin> getPlugins(DBRProgressMonitor monitor) throws DBCException {
        if (plugins == null) {
            plugins = new ArrayList<>();
            try (JDBCSession session = DBUtils.openMetaSession(monitor, this, "Load plugins")) {
                try (JDBCPreparedStatement dbStat = session.prepareStatement("SHOW PLUGINS")) {
                    try (JDBCResultSet dbResult = dbStat.executeQuery()) {
                        while (dbResult.next()) {
                            plugins.add(new MySQLPlugin(this, dbResult));
                        }
                    }
                } catch (SQLException e) {
                    log.debug("Error reading plugins information", e);
                }
            }
        }
        return plugins;
    }

    @Nullable
    public MySQLPlugin getPlugin(@NotNull String name) {
        for (MySQLPlugin plugin : plugins) {
            if (plugin.getName().equals(name)) {
                return plugin;
            }
        }
        return null;
    }

    public List<MySQLPrivilege> getPrivileges(DBRProgressMonitor monitor)
            throws DBException {
        if (privileges == null) {
            privileges = loadPrivileges(monitor);
        }
        return privileges;
    }

    public List<MySQLPrivilege> getPrivilegesByKind(DBRProgressMonitor monitor, MySQLPrivilege.Kind kind)
            throws DBException {
        List<MySQLPrivilege> privs = new ArrayList<>();
        for (MySQLPrivilege priv : getPrivileges(monitor)) {
            if (priv.getKind() == kind) {
                privs.add(priv);
            }
        }
        return privs;
    }

    public MySQLPrivilege getPrivilege(DBRProgressMonitor monitor, String name)
            throws DBException {
        return DBUtils.findObject(getPrivileges(monitor), name, true);
    }

    private List<MySQLPrivilege> loadPrivileges(DBRProgressMonitor monitor)
            throws DBException {
        try (JDBCSession session = DBUtils.openMetaSession(monitor, this, "Load privileges")) {
            try (JDBCPreparedStatement dbStat = session.prepareStatement("SHOW PRIVILEGES")) {
                try (JDBCResultSet dbResult = dbStat.executeQuery()) {
                    List<MySQLPrivilege> privileges = new ArrayList<>();
                    while (dbResult.next()) {
                        MySQLPrivilege user = new MySQLPrivilege(this, dbResult);
                        privileges.add(user);
                    }
                    return privileges;
                }
            }
        } catch (SQLException ex) {
            throw new DBException(ex, this);
        }
    }

    public List<MySQLParameter> getSessionStatus(DBRProgressMonitor monitor)
            throws DBException {
        return loadParameters(monitor, true, false);
    }

    public List<MySQLParameter> getGlobalStatus(DBRProgressMonitor monitor)
            throws DBException {
        return loadParameters(monitor, true, true);
    }

    public List<MySQLParameter> getSessionVariables(DBRProgressMonitor monitor)
            throws DBException {
        return loadParameters(monitor, false, false);
    }

    public List<MySQLParameter> getGlobalVariables(DBRProgressMonitor monitor)
            throws DBException {
        return loadParameters(monitor, false, true);
    }

    public List<MySQLDataSource> getInformation() {
        return Collections.singletonList(this);
    }

    private List<MySQLParameter> loadParameters(DBRProgressMonitor monitor, boolean status, boolean global) throws DBException {
        try (JDBCSession session = DBUtils.openMetaSession(monitor, this, "Load status")) {
            try (JDBCPreparedStatement dbStat = session.prepareStatement(
                    "SHOW " +
                            (global ? "GLOBAL " : "") +
                            (status ? "STATUS" : "VARIABLES"))) {
                try (JDBCResultSet dbResult = dbStat.executeQuery()) {
                    List<MySQLParameter> parameters = new ArrayList<>();
                    while (dbResult.next()) {
                        MySQLParameter parameter = new MySQLParameter(
                                this,
                                JDBCUtils.safeGetString(dbResult, "variable_name"),
                                JDBCUtils.safeGetString(dbResult, "value"));
                        parameters.add(parameter);
                    }
                    return parameters;
                }
            }
        } catch (SQLException ex) {
            throw new DBException(ex, this);
        }
    }

    @Override
    public DBCQueryTransformer createQueryTransformer(@NotNull DBCQueryTransformType type) {
        if (type == DBCQueryTransformType.RESULT_SET_LIMIT) {
            return new QueryTransformerLimit();
        } else if (type == DBCQueryTransformType.FETCH_ALL_TABLE) {
            return new QueryTransformerFetchAll(this);
        }
        return super.createQueryTransformer(type);
    }

    @Override
    public <T> T getAdapter(Class<T> adapter) {
        if (adapter == DBSStructureAssistant.class) {
            return adapter.cast(new MySQLStructureAssistant(this));
        } else if (adapter == SQLHelpProvider.class) {
            if (helpProvider == null) {
                helpProvider = new MySQLHelpProvider(this);
            }
            return adapter.cast(helpProvider);
        } else if (adapter == DBAServerSessionManager.class) {
            return adapter.cast(new MySQLSessionManager(this));
        } else if (adapter == SpatialDataProvider.class) {
            return adapter.cast(new SpatialDataProvider() {
                @Override
                public boolean isFlipCoordinates() {
                    return false;
                }

                @Override
                public int getDefaultSRID() {
                    return GisConstants.SRID_4326;
                }
            });
        } else if (adapter == DBCQueryPlanner.class) {
            return adapter.cast(new MySQLPlanAnalyser(this));
        }
        return super.getAdapter(adapter);
    }

    @Override
    public Collection<? extends DBSDataType> getLocalDataTypes() {
        return dataTypeCache.getCachedObjects();
    }

    @Override
    public DBSDataType getLocalDataType(String typeName) {
        return dataTypeCache.getCachedObject(typeName);
    }

    @Override
    public DBSDataType getLocalDataType(int typeID) {
        return dataTypeCache.getCachedObject(typeID);
    }

    @Override
    public String getDefaultDataTypeName(DBPDataKind dataKind) {
        switch (dataKind) {
            case BOOLEAN:
                return "TINYINT(1)";
            case NUMERIC:
                return "BIGINT";
            case DATETIME:
                return "TIMESTAMP";
            case BINARY:
                return "BINARY";
            case CONTENT:
                return "LONGBLOB";
            case ROWID:
                return "BINARY";
            default:
                return "VARCHAR";
        }
    }

    @Override
    public boolean isStatisticsCollected() {
        return hasStatistics;
    }

    @Override
    public void collectObjectStatistics(DBRProgressMonitor monitor, boolean totalSizeOnly, boolean forceRefresh) throws DBException {
        if (hasStatistics && !forceRefresh) {
            return;
        }
        if (!this.isMariaDB() && !this.isServerVersionAtLeast(4, 1)) {
            // Not supported by MySQL server
            hasStatistics = true;
            return;
        }

        try (JDBCSession session = DBUtils.openMetaSession(monitor, this, "Load table status")) {
            try (JDBCPreparedStatement dbStat = session.prepareStatement(
                    "SELECT table_schema, SUM(data_length + index_length) \n" +
                            "FROM information_schema.tables \n" +
                            "GROUP BY table_schema")) {
                try (JDBCResultSet dbResult = dbStat.executeQuery()) {
                    while (dbResult.next()) {
                        String dbName = dbResult.getString(1);
                        MySQLCatalog catalog = catalogCache.getObject(monitor, this, dbName);
                        if (catalog != null) {
                            long dbSize = dbResult.getLong(2);
                            catalog.setDatabaseSize(dbSize);
                        }
                    }
                }
            } catch (SQLException e) {
                throw new DBCException(e, session.getExecutionContext());
            }
        } finally {
            hasStatistics = true;
        }
    }

    static class CatalogCache extends JDBCObjectCache<MySQLDataSource, MySQLCatalog> {
        @NotNull
        @Override
        protected JDBCStatement prepareObjectsStatement(@NotNull JDBCSession session, @NotNull MySQLDataSource owner) throws SQLException {
            StringBuilder catalogQuery = new StringBuilder("show databases");
            DBSObjectFilter catalogFilters = owner.getContainer().getObjectFilter(MySQLCatalog.class, null, false);
            if (catalogFilters != null) {
                JDBCUtils.appendFilterClause(catalogQuery, catalogFilters, MySQLConstants.COL_DATABASE_NAME, true, owner);
            }
            JDBCPreparedStatement dbStat = session.prepareStatement(catalogQuery.toString());
            if (catalogFilters != null) {
                JDBCUtils.setFilterParameters(dbStat, 1, catalogFilters);
            }
            return dbStat;
        }

        @Override
        protected MySQLCatalog fetchObject(@NotNull JDBCSession session, @NotNull MySQLDataSource owner, @NotNull JDBCResultSet resultSet) throws SQLException, DBException {
            return new MySQLCatalog(owner, resultSet, null);
        }

    }

    public boolean isMariaDB() {
        return MySQLConstants.DRIVER_CLASS_MARIA_DB.equals(
                getContainer().getDriver().getDriverClassName());
    }

    @Override
    public ErrorType discoverErrorType(@NotNull Throwable error) {
        String sqlState = SQLState.getStateFromException(error);
        if (isMariaDB()) {
            // MariaDB-specific. They have bad SQLState support
            if ("08".equals(sqlState)) {
                return ErrorType.CONNECTION_LOST;
            }
        } else {
            if (SQLState.SQL_S1009.getCode().equals(sqlState)) {
                return ErrorType.CONNECTION_LOST;
            }
        }
        Throwable cause = error.getCause();
        if (cause != null && sqlState == null) {
//            if (cause instanceof InterruptedException) {
//                return ErrorType.CONNECTION_LOST;
//            }
            if (cause instanceof JDBCSQLException) {
                return ErrorType.CONNECTION_LOST;
            }
            if (cause instanceof NullPointerException) {
                return ErrorType.CONNECTION_LOST;
            }
        }
        return super.discoverErrorType(error);
    }

    private Pattern ERROR_POSITION_PATTERN = Pattern.compile(".+\\s+line ([0-9]+),? column ([0-9]+)");

    private Pattern ERROR_POSITION_PATTERN_2 = Pattern.compile(" at line ([0-9]+)");

    @Nullable
    @Override
    public ErrorPosition[] getErrorPosition(@NotNull DBRProgressMonitor monitor, @NotNull DBCExecutionContext context, @NotNull String query, @NotNull Throwable error) {
        String message = error.getMessage();
        if (!CommonUtils.isEmpty(message)) {

            List<ErrorPosition> positions = new ArrayList<>();
            Matcher matcher = ERROR_POSITION_PATTERN.matcher(message);
            while (matcher.find()) {
                DBPErrorAssistant.ErrorPosition pos = new DBPErrorAssistant.ErrorPosition();
                pos.line = Integer.parseInt(matcher.group(1)) - 1;
                pos.position = Integer.parseInt(matcher.group(2)) - 1;
                positions.add(pos);
            }

            if (positions.isEmpty()) {
                matcher = ERROR_POSITION_PATTERN_2.matcher(message);
                if (matcher.find()) {
                    DBPErrorAssistant.ErrorPosition pos = new DBPErrorAssistant.ErrorPosition();
                    pos.line = Integer.parseInt(matcher.group(1)) - 1;
                    positions.add(pos);
                }
            }

            if (!positions.isEmpty()) {
                return positions.toArray(new ErrorPosition[0]);
            }
        }
        return null;
    }

    public boolean supportsCheckConstraints() {

        if (containsCheckConstraintTable == null) {
            try (JDBCSession session = DBUtils.openMetaSession(new VoidProgressMonitor(), this, "Load ContainsCheckConstraintTable")) {
                // 读取包含检查约束表。这里用个了工具类，算是简化查询和获取结果。
                if (supportsInformationSchema()) {
                    // Check check constraints in base
                    try {
                        String resultSet = JDBCUtils.queryString(session, "SELECT * FROM information_schema.TABLES t\n" +
                                "WHERE\n" +
                                "\tt.TABLE_SCHEMA = 'information_schema'\n" +
                                "\tAND t.TABLE_NAME = 'CHECK_CONSTRAINTS'");
                        containsCheckConstraintTable = (resultSet != null);
                    } catch (SQLException e) {
                        log.debug("Error reading information schema", e);
                    }
                }
            } catch (DBCException e) {
                log.debug("Error reading information schema", e);
            }
        }

        if (this.isMariaDB()) {
            return this.isServerVersionAtLeast(10, 2) && containsCheckConstraintTable;
        } else {
            return this.isServerVersionAtLeast(8, 0) && containsCheckConstraintTable;
        }
    }

    /**
     * Checks if information_schema table is supported.
     *
     * <p>The table was not supported up until MySQL 5.0
     *
     * @return {@code true} if information_schema is supported
     */
    public boolean supportsInformationSchema() {
        return isServerVersionAtLeast(5, 0);
    }

    public boolean supportsSequences() {
        if (this.isMariaDB()) {
            return this.isServerVersionAtLeast(10, 3);
        }
        return false;
    }

    /**
     * Checks if column statistics is supported.
     *
     * @return {@code true} if column statistics is supported
     */
    public boolean supportsColumnStatistics() {
        return !isMariaDB() && isServerVersionAtLeast(8, 0);
    }

    @Override
    public RuntimeRunnable killConnection(DBRProgressMonitor monitor, JDBCExecutionContext defaultContext, String processId) {
        String sql = String.format("KILL CONNECTION %s;", processId);
        return () -> DBExecUtils.execute(monitor, defaultContext, killConnection(), sql);
    }

}
