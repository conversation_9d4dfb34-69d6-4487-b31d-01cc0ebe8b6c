
package com.dc.summer.ext.mysql.tasks;

import com.dc.summer.ext.mysql.model.MySQLTableBase;
import com.dc.summer.model.data.json.JSONUtils;
import com.dc.summer.model.meta.Property;
import com.dc.summer.model.runtime.DBRRunnableContext;
import com.dc.summer.model.sql.task.SQLToolExecuteSettings;

import java.util.Map;

public class MySQLToolTableTruncateSettings extends SQLToolExecuteSettings<MySQLTableBase> {
    private boolean isForce;

    @Property(viewable = true, editable = true, updatable = true)
    public boolean isForce() {
        return isForce;
    }

    public void setForce(boolean force) {
        isForce = force;
    }

    @Override
    public void loadConfiguration(DBRRunnableContext runnableContext, Map<String, Object> config) {
        super.loadConfiguration(runnableContext, config);
        isForce = JSONUtils.getBoolean(config, "force");
    }

    @Override
    public void saveConfiguration(Map<String, Object> config) {
        super.saveConfiguration(config);
        config.put("force", isForce);
    }
}
