
package com.dc.summer.ext.mysql.model;

import com.dc.summer.model.data.DBDInsertReplaceMethod;
import com.dc.summer.model.runtime.DBRProgressMonitor;
import com.dc.summer.model.struct.DBSAttributeBase;
import com.dc.summer.model.struct.rdb.DBSTable;
import com.dc.code.NotNull;

public class MySQLInsertReplaceMethod implements DBDInsertReplaceMethod {

    @NotNull
    @Override
    public String getOpeningClause(DBSTable table, DBRProgressMonitor monitor) {
        return "REPLACE INTO";
    }

    @Override
    public String getTrailingClause(DBSTable table, DBRProgressMonitor monitor, DBSAttributeBase[] attributes) {
        return null;
    }
}
