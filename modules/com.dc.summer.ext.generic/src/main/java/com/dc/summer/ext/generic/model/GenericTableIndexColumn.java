
package com.dc.summer.ext.generic.model;

import com.dc.summer.model.impl.struct.AbstractTableIndexColumn;
import com.dc.summer.model.meta.Property;
import com.dc.code.NotNull;
import com.dc.code.Nullable;

/**
 * GenericTableIndexColumn
 */
public class GenericTableIndexColumn extends AbstractTableIndexColumn
{
    private GenericTableIndex index;
    private GenericTableColumn tableColumn;
    private int ordinalPosition;
    private boolean ascending;

    public GenericTableIndexColumn(GenericTableIndex index, GenericTableColumn tableColumn,
                                   int ordinalPosition,
                                   boolean ascending)
    {
        this.index = index;
        this.tableColumn = tableColumn;
        this.ordinalPosition = ordinalPosition;
        this.ascending = ascending;
    }

    GenericTableIndexColumn(GenericTableIndex toIndex, GenericTableIndexColumn source)
    {
        this.index = toIndex;
        this.tableColumn = source.tableColumn;
        this.ordinalPosition = source.ordinalPosition;
        this.ascending = source.ascending;
    }

    @NotNull
    @Override
    public GenericTableIndex getIndex()
    {
        return index;
    }

    @NotNull
    @Override
    @Property(viewable = true, order = 1)
    public String getName()
    {
        return tableColumn.getName();
    }

    @Nullable
    @Override
    @Property(viewable = true, order = 1)
    public GenericTableColumn getTableColumn()
    {
        return tableColumn;
    }

    @Override
    @Property(viewable = false, order = 2)
    public int getOrdinalPosition()
    {
        return ordinalPosition;
    }

    @Override
    @Property(viewable = true, order = 3)
    public boolean isAscending()
    {
        return ascending;
    }

    @Nullable
    @Override
    public String getDescription()
    {
        return tableColumn.getDescription();
    }

    @Override
    public GenericTableIndex getParentObject()
    {
        return index;
    }

    @NotNull
    @Override
    public GenericDataSource getDataSource()
    {
        return index.getDataSource();
    }

}
