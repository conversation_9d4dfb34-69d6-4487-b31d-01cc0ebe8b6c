
package com.dc.summer.ext.generic.edit;

import com.dc.summer.ext.generic.model.GenericTableBase;
import com.dc.summer.ext.generic.model.GenericUtils;
import com.dc.summer.model.edit.DBECommandContext;
import com.dc.summer.model.runtime.DBRProgressMonitor;
import com.dc.summer.model.struct.rdb.DBSIndexType;
import com.dc.code.Nullable;
import com.dc.summer.ext.generic.model.GenericTable;
import com.dc.summer.ext.generic.model.GenericTableIndex;
import com.dc.summer.model.impl.sql.edit.struct.SQLIndexManager;
import com.dc.summer.model.struct.DBSObject;
import com.dc.summer.model.struct.cache.DBSObjectCache;

import java.util.Map;

/**
 * Generic index manager
 */
public class GenericIndexManager extends SQLIndexManager<GenericTableIndex, GenericTableBase> {

    @Nullable
    @Override
    public DBSObjectCache<? extends DBSObject, GenericTableIndex> getObjectsCache(GenericTableIndex object)
    {
        return object.getTable().getContainer().getIndexCache();
    }

    @Override
    public boolean canCreateObject(Object container) {
        return (container instanceof GenericTable)
            && ((GenericTable) container).getDataSource().getInfo().supportsIndexes()
            && ((GenericTable) container).getDataSource().getSQLDialect().supportsIndexCreateAndDrop();
    }

    @Override
    public boolean canEditObject(GenericTableIndex object) {
        return GenericUtils.canAlterTable(object);
    }

    @Override
    public boolean canDeleteObject(GenericTableIndex object) {
        return object.getDataSource().getSQLDialect().supportsIndexCreateAndDrop();
    }

    @Override
    protected GenericTableIndex createDatabaseObject(
            DBRProgressMonitor monitor, DBECommandContext context, final Object container,
            Object from, Map<String, Object> options)
    {
        GenericTableBase tableBase = (GenericTableBase) container;
        return tableBase.getDataSource().getMetaModel().createIndexImpl(
            tableBase,
            true,
            null,
            0,
            null,
            DBSIndexType.OTHER,
            false);
    }

}
