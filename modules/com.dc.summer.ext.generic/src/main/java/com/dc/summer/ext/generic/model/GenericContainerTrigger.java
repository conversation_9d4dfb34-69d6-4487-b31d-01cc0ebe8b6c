
package com.dc.summer.ext.generic.model;

import com.dc.summer.model.struct.rdb.DBSTable;
import com.dc.code.NotNull;

public class GenericContainerTrigger extends GenericTrigger<GenericStructContainer> {

    public GenericContainerTrigger(@NotNull GenericStructContainer container, String name, String description) {
        super(container, name, description);
    }

    @Override
    public DBSTable getTable() {
        return null;
    }
}
