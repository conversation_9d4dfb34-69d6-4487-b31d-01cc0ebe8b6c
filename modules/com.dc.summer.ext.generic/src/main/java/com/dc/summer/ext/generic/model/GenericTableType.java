
package com.dc.summer.ext.generic.model;

import com.dc.summer.model.DBPDataSource;
import com.dc.code.NotNull;
import com.dc.code.Nullable;
import com.dc.summer.model.struct.DBSObject;

/**
 * Table type
 */
public class GenericTableType implements DBSObject {

    private GenericDataSource dataSource;
    private String name;

    public GenericTableType(GenericDataSource dataSource, String name)
    {
        this.dataSource = dataSource;
        this.name = name;
    }

    @Nullable
    @Override
    public String getDescription()
    {
        return null;
    }

    @Override
    public DBSObject getParentObject()
    {
        return dataSource.getContainer();
    }

    @NotNull
    @Override
    public DBPDataSource getDataSource()
    {
        return dataSource;
    }

    @NotNull
    @Override
    public String getName()
    {
        return name;
    }

    @Override
    public boolean isPersisted()
    {
        return true;
    }
}
