

package com.dc.summer.ext.generic.model;

import com.dc.summer.ext.generic.GenericConstants;
import com.dc.summer.model.connection.DBPDriver;
import com.dc.summer.model.exec.jdbc.JDBCDatabaseMetaData;
import com.dc.summer.model.impl.jdbc.JDBCDataSourceInfo;
import com.dc.utils.CommonUtils;

/**
 * Generic data source info
 */
public class GenericDataSourceInfo extends JDBCDataSourceInfo {

    private final boolean supportsLimits;
    public boolean supportsCatalogSelection;
    public boolean supportsSchemaSelection;
    private boolean supportsMultipleResults;
    private boolean supportsNullableUniqueConstraints;
    private final boolean supportsTransactionsForDDL;
    private final boolean supportsConstraints;
    private final boolean supportsTransactionIsolation;

    public GenericDataSourceInfo(DBPDriver driver, JDBCDatabaseMetaData metaData)
    {
        super(metaData);
        supportsLimits = CommonUtils.getBoolean(driver.getDriverParameter(GenericConstants.PARAM_SUPPORTS_LIMITS), true);
        setSupportsResultSetScroll(CommonUtils.getBoolean(driver.getDriverParameter(GenericConstants.PARAM_SUPPORTS_SCROLL), false));
        supportsMultipleResults = CommonUtils.getBoolean(driver.getDriverParameter(GenericConstants.PARAM_SUPPORTS_MULTIPLE_RESULTS), false);
        supportsTransactionsForDDL = CommonUtils.getBoolean(driver.getDriverParameter(GenericConstants.PARAM_SUPPORTS_TRANSACTIONS_FOR_DDL), true);
        supportsTransactionIsolation = CommonUtils.getBoolean(driver.getDriverParameter(GenericConstants.PARAM_SUPPORTS_TRANSACTION_ISOLATION), true);
        setReadOnlyData(CommonUtils.getBoolean(driver.getDriverParameter(GenericConstants.PARAM_READ_ONLY_DATA), false));
        setReadOnlyMetaData(CommonUtils.getBoolean(driver.getDriverParameter(GenericConstants.PARAM_READ_ONLY_META_DATA), false));
        supportsCatalogSelection = CommonUtils.getBoolean(driver.getDriverParameter(GenericConstants.PARAM_SUPPORTS_CATALOG_SELECTION), true);
        supportsSchemaSelection = CommonUtils.getBoolean(driver.getDriverParameter(GenericConstants.PARAM_SUPPORTS_CATALOG_SELECTION), true);
        supportsNullableUniqueConstraints = false;
        supportsConstraints = CommonUtils.getBoolean(driver.getDriverParameter(GenericConstants.PARAM_SUPPORTS_CONSTRAINTS), true);
    }

    @Override
    public boolean supportsResultSetLimit() {
        return supportsLimits;
    }

    @Override
    public boolean supportsNullableUniqueConstraints() {
        return supportsNullableUniqueConstraints;
    }

    public void setSupportsNullableUniqueConstraints(boolean supportsNullableUniqueConstraints) {
        this.supportsNullableUniqueConstraints = supportsNullableUniqueConstraints;
    }

    public boolean supportsCatalogSelection() {
        return supportsCatalogSelection;
    }

    public boolean supportsSchemaSelection() {
        return supportsSchemaSelection;
    }

    @Override
    public boolean supportsMultipleResults() {
        return supportsMultipleResults;
    }

    @Override
    public boolean supportsTransactionsForDDL() {
        return super.supportsTransactionsForDDL() && supportsTransactionsForDDL;
    }

    public boolean supportsTableConstraints() {
        return supportsConstraints;
    }

    @Override
    public boolean isSupportsTransactionIsolation() {
        return supportsTransactionIsolation;
    }
}
