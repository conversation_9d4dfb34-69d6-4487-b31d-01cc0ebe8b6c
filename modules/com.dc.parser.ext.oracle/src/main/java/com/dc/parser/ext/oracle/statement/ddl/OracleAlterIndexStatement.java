
package com.dc.parser.ext.oracle.statement.ddl;

import com.dc.parser.model.segment.ddl.index.IndexSegment;
import com.dc.parser.model.statement.ddl.AlterIndexStatement;
import com.dc.parser.ext.oracle.statement.OracleStatement;
import lombok.Getter;
import lombok.Setter;

import java.util.Optional;

/**
 * Oracle alter index statement.
 */
@Setter
@Getter
public final class OracleAlterIndexStatement extends AlterIndexStatement implements OracleStatement {

    private IndexSegment renameIndex;

    /**
     * Get rename index segment.
     *
     * @return rename index segment
     */
    public Optional<IndexSegment> getRenameIndex() {
        return Optional.ofNullable(renameIndex);
    }
}
