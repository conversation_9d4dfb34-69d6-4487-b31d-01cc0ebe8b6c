
package com.dc.parser.ext.oracle.statement.ddl;

import com.dc.parser.ext.oracle.enums.IndexSpecification;
import com.dc.parser.ext.oracle.segment.IndexPropertiesSegment;
import com.dc.parser.model.statement.ddl.CreateIndexStatement;
import com.dc.parser.ext.oracle.statement.OracleStatement;
import lombok.Getter;
import lombok.Setter;

/**
 * Oracle create index statement.
 */
@Getter
@Setter
public final class OracleCreateIndexStatement extends CreateIndexStatement implements OracleStatement {
    private IndexPropertiesSegment indexProperties;
    private IndexSpecification specification;
}
