package com.dc.parser.ext.oracle.segment.interval;

import com.dc.parser.model.segment.dml.expr.ExpressionSegment;
import com.dc.parser.model.segment.dml.item.IntervalExpressionProjection;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Setter;

/**
 * Between expression.
 */
@RequiredArgsConstructor
@Getter
public final class IntervalExpressionProjectionSegment implements IntervalExpressionProjection {
    
    private final int startIndex;
    
    private final int stopIndex;
    
    private final ExpressionSegment left;
    
    private final ExpressionSegment minus;
    
    private final ExpressionSegment right;
    
    private final String expression;
    
    @Setter
    private IntervalDayToSecondExpression dayToSecondExpression;
    
    @Setter
    private IntervalYearToMonthExpression yearToMonthExpression;
    
    @Override
    public String getText() {
        return expression;
    }
    
    @Override
    public String getColumnLabel() {
        return getText();
    }
}
