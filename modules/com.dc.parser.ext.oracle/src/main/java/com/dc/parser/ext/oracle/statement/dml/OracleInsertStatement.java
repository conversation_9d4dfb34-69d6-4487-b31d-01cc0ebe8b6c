package com.dc.parser.ext.oracle.statement.dml;

import com.dc.parser.ext.oracle.segment.HintSegment;
import com.dc.parser.ext.oracle.statement.OracleStatement;
import com.dc.parser.model.segment.dml.predicate.WhereSegment;
import com.dc.parser.model.segment.dml.table.MultiTableConditionalIntoSegment;
import com.dc.parser.model.segment.dml.table.MultiTableInsertIntoSegment;
import com.dc.parser.model.segment.dml.table.MultiTableInsertType;
import com.dc.parser.model.statement.dml.InsertStatement;
import lombok.Getter;
import lombok.Setter;

import java.util.Optional;

/**
 * Oracle insert statement.
 */
@Getter
@Setter
public final class OracleInsertStatement extends InsertStatement implements OracleStatement {
    
    private MultiTableInsertType multiTableInsertType;
    
    private MultiTableInsertIntoSegment multiTableInsertIntoSegment;
    
    private MultiTableConditionalIntoSegment multiTableConditionalIntoSegment;
    
    private WhereSegment where;

    private HintSegment hint;
    /**
     * Get multi table insert type.
     *
     * @return multi table insert type
     */
    public Optional<MultiTableInsertType> getMultiTableInsertType() {
        return Optional.ofNullable(multiTableInsertType);
    }
    
    /**
     * Get multi table insert into segment.
     *
     * @return multi table insert into segment
     */
    public Optional<MultiTableInsertIntoSegment> getMultiTableInsertIntoSegment() {
        return Optional.ofNullable(multiTableInsertIntoSegment);
    }
    
    /**
     * Get multi table conditional into segment.
     *
     * @return multi table conditional into segment
     */
    public Optional<MultiTableConditionalIntoSegment> getMultiTableConditionalIntoSegment() {
        return Optional.ofNullable(multiTableConditionalIntoSegment);
    }
    
    /**
     * Get where segment.
     *
     * @return where segment
     */
    public Optional<WhereSegment> getWhere() {
        return Optional.ofNullable(where);
    }
}
