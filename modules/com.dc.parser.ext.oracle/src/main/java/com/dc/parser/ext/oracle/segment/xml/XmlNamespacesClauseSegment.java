
package com.dc.parser.ext.oracle.segment.xml;

import lombok.Getter;
import lombok.RequiredArgsConstructor;
import com.dc.parser.model.segment.dml.expr.complex.ComplexExpressionSegment;

import java.util.Collection;
import java.util.LinkedList;

/**
 * Xml namespaces segment.
 */
@RequiredArgsConstructor
@Getter
public final class XmlNamespacesClauseSegment implements ComplexExpressionSegment {
    
    private final int startIndex;
    
    private final int stopIndex;
    
    private final String defaultString;
    
    private final Collection<XmlNamespaceStringAsIdentifierSegment> stringAsIdentifier = new LinkedList<>();
    
    private final String text;
}
