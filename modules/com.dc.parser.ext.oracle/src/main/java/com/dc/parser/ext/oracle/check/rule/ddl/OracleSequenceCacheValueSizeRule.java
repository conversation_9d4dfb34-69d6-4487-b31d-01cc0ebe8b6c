package com.dc.parser.ext.oracle.check.rule.ddl;

import com.dc.parser.ext.oracle.statement.ddl.OracleCreateSequenceStatement;
import com.dc.parser.model.enums.CheckRuleDatabasePrefix;
import com.dc.parser.model.check.rule.CheckResult;
import com.dc.parser.model.check.rule.CheckRuleParameter;
import com.dc.parser.model.check.rule.ddl.SequenceCacheValueSizeRule;
import com.dc.parser.model.statement.SQLStatement;

public class OracleSequenceCacheValueSizeRule extends SequenceCacheValueSizeRule {

    @Override
    public CheckResult check(SQLStatement sqlStatement, CheckRuleParameter parameter) {

        if (!(sqlStatement instanceof OracleCreateSequenceStatement)) {
            return CheckResult.DEFAULT_SUCCESS_RESULT;
        }

        OracleCreateSequenceStatement checkStatement = (OracleCreateSequenceStatement) sqlStatement;
        int cache = checkStatement.getCache();

        int max = Integer.parseInt(parameter.getCheckRuleContent().getFirstValue());
        int min = Integer.parseInt(parameter.getCheckRuleContent().getSecondValue());

        return cache < min || cache > max ? CheckResult.buildFailResult(parameter.getCheckRuleContent()) : CheckResult.DEFAULT_SUCCESS_RESULT;
    }

    @Override
    public String getType() {
        return CheckRuleDatabasePrefix.ORACLE_.getValue() + super.getType();
    }

}
