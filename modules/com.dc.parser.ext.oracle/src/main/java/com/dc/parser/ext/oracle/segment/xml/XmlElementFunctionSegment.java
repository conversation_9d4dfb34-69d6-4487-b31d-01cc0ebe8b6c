
package com.dc.parser.ext.oracle.segment.xml;

import lombok.Getter;
import lombok.RequiredArgsConstructor;
import com.dc.parser.model.segment.dml.expr.ExpressionSegment;
import com.dc.parser.model.segment.dml.expr.complex.ComplexExpressionSegment;
import com.dc.parser.model.segment.dml.item.ProjectionSegment;
import com.dc.parser.model.value.identifier.IdentifierValue;

import java.util.Collection;
import java.util.LinkedList;

/**
 * Xml element function segment.
 */
@RequiredArgsConstructor
@Getter
public final class XmlElementFunctionSegment implements ComplexExpressionSegment, ProjectionSegment {
    
    private final int startIndex;
    
    private final int stopIndex;
    
    private final String functionName;
    
    private final IdentifierValue identifier;
    
    private final Collection<ExpressionSegment> xmlAttributes = new LinkedList<>();
    
    private final Collection<ExpressionSegment> parameters = new LinkedList<>();
    
    private final String text;
    
    @Override
    public String getColumnLabel() {
        return text;
    }
}
