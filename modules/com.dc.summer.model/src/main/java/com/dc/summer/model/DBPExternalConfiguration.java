
package com.dc.summer.model;

import java.util.Map;

/**
 * External configuration
 */
public class DBPExternalConfiguration {

    private final String id;
    private final Map<String, Object> properties;

    public DBPExternalConfiguration(String id, Map<String, Object> properties) {
        this.id = id;
        this.properties = properties;
    }

    public String getId() {
        return id;
    }

    public Map<String, Object> getProperties() {
        return properties;
    }
}
