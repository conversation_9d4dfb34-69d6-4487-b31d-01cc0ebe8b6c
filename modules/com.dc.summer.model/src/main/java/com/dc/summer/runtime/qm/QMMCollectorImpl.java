
package com.dc.summer.runtime.qm;

import com.dc.summer.Log;
import com.dc.summer.model.DBUtils;
import com.dc.summer.model.app.DBPWorkspace;
import com.dc.summer.model.auth.SMSession;
import com.dc.summer.model.auth.SMSessionPersistent;
import com.dc.summer.model.exec.DBCExecutionContext;
import com.dc.summer.model.exec.DBCStatement;
import com.dc.summer.model.qm.*;
import com.dc.summer.model.qm.meta.*;
import com.dc.summer.model.runtime.DBRProgressMonitor;
import com.dc.summer.model.runtime.LoggingProgressMonitor;
import org.eclipse.core.runtime.IStatus;
import org.eclipse.core.runtime.Status;
import com.dc.code.NotNull;
import com.dc.summer.DBException;
import com.dc.summer.model.app.DBPProject;
import com.dc.summer.model.exec.DBCResultSet;
import com.dc.summer.model.exec.DBCSavepoint;
import com.dc.summer.model.qm.*;
import com.dc.summer.model.qm.meta.*;
import com.dc.summer.model.runtime.AbstractJob;
import com.dc.utils.LongKeyMap;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * Query manager execution handler implementation
 */
public class QMMCollectorImpl extends DefaultExecutionHandler implements QMMCollector {

    private static final Log log = Log.getLog(QMMCollectorImpl.class);

    private static final long EVENT_DISPATCH_PERIOD = 250;
    private static final int MAX_HISTORY_EVENTS = 10000;

    // Session map
    private LongKeyMap<QMMConnectionInfo> connectionMap = new LongKeyMap<>();
    private List<Long> closedConnections = new ArrayList<>();

    // External listeners
    private final List<QMMetaListener> listeners = new ArrayList<>();

    // Temporary event pool
    private List<QMMetaEvent> eventPool = new ArrayList<>();
    // Sync object
    private final Object historySync = new Object();
    // History (may be purged when limit reached)
    private List<QMMetaEvent> pastEvents = new ArrayList<>();
    private boolean running = true;

    public QMMCollectorImpl()
    {
        // TODO 1
//        new EventDispatcher().schedule(EVENT_DISPATCH_PERIOD);
    }

    public synchronized void dispose()
    {
        if (!connectionMap.isEmpty()) {
            List<QMMConnectionInfo> openSessions = new ArrayList<>();
            for (QMMConnectionInfo connection : connectionMap.values()) {
                if (!connection.isClosed()) {
                    openSessions.add(connection);
                }
            }
            if (!openSessions.isEmpty()) {
                log.warn("Some sessions are still open: " + openSessions);
            }
        }
        synchronized (listeners) {
            if (!listeners.isEmpty()) {
                log.warn("Some QM meta collector listeners are still open: " + listeners);
                listeners.clear();
            }
        }
        running = false;
    }

    boolean isRunning()
    {
        return running;
    }

    @NotNull
    @Override
    public String getHandlerName()
    {
        return "Meta info collector";
    }

    public void addListener(QMMetaListener listener)
    {
        synchronized (listeners) {
            listeners.add(listener);
        }
    }

    public void removeListener(QMMetaListener listener)
    {
        synchronized (listeners) {
            if (!listeners.remove(listener)) {
                log.warn("Listener '" + listener + "' is not registered in QM meta collector");
            }
        }
    }

    private List<QMMetaListener> getListeners()
    {
        synchronized (listeners) {
            if (listeners.isEmpty()) {
                return Collections.emptyList();
            }
            if (listeners.size() == 1) {
                return Collections.singletonList(listeners.get(0));
            }
            return new ArrayList<>(listeners);
        }
    }

    private synchronized void tryFireMetaEvent(final QMMObject object, final QMEventAction action, DBCExecutionContext context) {
        try {
            DBRProgressMonitor monitor = new LoggingProgressMonitor();
            DBPProject project = context.getDataSource().getContainer().getProject();
            SMSession session = project.getSessionContext().getSpaceSession(monitor, project, false);
            if (session == null) {
                DBPWorkspace workspace = project.getWorkspace();
                session = workspace.getAuthContext().getSpaceSession(monitor, workspace, false);
            }
            SMSessionPersistent sessionPersistent = DBUtils.getAdapter(SMSessionPersistent.class, session);
            if (sessionPersistent == null) {
                log.warn("Session persistent not found");
                return;
            }

            eventPool.add(new QMMetaEvent(object, action, sessionPersistent.getAttribute(QMConstants.QM_SESSION_ID_ATTR)));
        } catch (DBException e) {
            log.error("Failed to fire qm meta event", e);
        }
    }

    private synchronized List<QMMetaEvent> obtainEvents() {
        if (eventPool.isEmpty()) {
            return Collections.emptyList();
        }
        List<QMMetaEvent> events = eventPool;
        eventPool = new ArrayList<>();
        return events;
    }

    public QMMConnectionInfo getConnectionInfo(DBCExecutionContext context) {
        QMMConnectionInfo connectionInfo = connectionMap.get(context.getContextId());
        if (connectionInfo == null) {
//            log.debug("Can't find connectionInfo meta information: " + context.getContextId() + " (" + context.getContextName() + ")");
        }
        return connectionInfo;
    }

    public List<QMMetaEvent> getPastEvents() {
        synchronized (historySync) {
            return new ArrayList<>(pastEvents);
        }
    }

    @Override
    public synchronized void handleContextOpen(@NotNull DBCExecutionContext context, boolean transactional) {
        final long contextId = context.getContextId();
        QMMConnectionInfo connection = connectionMap.get(contextId);
        if (connection == null) {
            connection = new QMMConnectionInfo(
                context,
                transactional);
            connectionMap.put(contextId, connection);
        } else {
            // This session may already be in cache in case of reconnect/invalidate
            // (when context closed and reopened without new context object creation)
            connection.reopen(context);
        }

        // Remove from closed sessions (in case of re-opened connection)
        closedConnections.remove(contextId);
        tryFireMetaEvent(connection, QMEventAction.BEGIN, context);
        // Notify
    }

    @Override
    public synchronized void handleContextClose(@NotNull DBCExecutionContext context)
    {
        QMMConnectionInfo session = getConnectionInfo(context);
        if (session != null) {
            session.close();
            tryFireMetaEvent(session, QMEventAction.END, context);
        }
        closedConnections.add(context.getContextId());
    }

    @Override
    public synchronized void handleTransactionAutocommit(@NotNull DBCExecutionContext context, boolean autoCommit)
    {
        QMMConnectionInfo sessionInfo = getConnectionInfo(context);
        if (sessionInfo != null) {
            QMMTransactionInfo oldTxn = sessionInfo.changeTransactional(!autoCommit);
            if (oldTxn != null) {
                tryFireMetaEvent(oldTxn, QMEventAction.END, context);
            }
            tryFireMetaEvent(sessionInfo, QMEventAction.UPDATE, context);
        }
    }

    @Override
    public synchronized void handleTransactionCommit(@NotNull DBCExecutionContext context)
    {
        QMMConnectionInfo sessionInfo = getConnectionInfo(context);
        if (sessionInfo != null) {
            QMMTransactionInfo oldTxn = sessionInfo.commit();
            if (oldTxn != null) {
                tryFireMetaEvent(oldTxn, QMEventAction.END, context);
            }
        }
    }

    @Override
    public synchronized void handleTransactionRollback(@NotNull DBCExecutionContext context, DBCSavepoint savepoint)
    {
        QMMConnectionInfo sessionInfo = getConnectionInfo(context);
        if (sessionInfo != null) {
            QMMObject oldTxn = sessionInfo.rollback(savepoint);
            if (oldTxn != null) {
                tryFireMetaEvent(oldTxn, QMEventAction.END, context);
            }
        }
    }

    @Override
    public synchronized void handleStatementOpen(@NotNull DBCStatement statement)
    {
        // TODO 1
        /*QMMConnectionInfo session = getConnectionInfo(statement.getSession().getExecutionContext());
        if (session != null) {
            QMMStatementInfo stat = session.openStatement(statement);
            tryFireMetaEvent(stat, QMEventAction.BEGIN, statement.getSession().getExecutionContext());
        }*/
    }

    @Override
    public synchronized void handleStatementClose(@NotNull DBCStatement statement, long rows)
    {
        QMMConnectionInfo session = getConnectionInfo(statement.getSession().getExecutionContext());
        if (session != null) {
            QMMStatementInfo stat = session.closeStatement(statement, rows);
            if (stat == null) {
                log.warn("Can't properly handle statement close");
            } else {
                tryFireMetaEvent(stat, QMEventAction.END, statement.getSession().getExecutionContext());
            }
        }
    }

    @Override
    public synchronized void handleStatementExecuteBegin(@NotNull DBCStatement statement)
    {
        QMMConnectionInfo session = getConnectionInfo(statement.getSession().getExecutionContext());
        if (session != null) {
            QMMStatementExecuteInfo exec = session.beginExecution(statement);
            if (exec != null) {
                tryFireMetaEvent(exec, QMEventAction.BEGIN, statement.getSession().getExecutionContext());
            }
        }
    }

    @Override
    public synchronized void handleStatementExecuteEnd(@NotNull DBCStatement statement, long rows, Throwable error)
    {
        QMMConnectionInfo session = getConnectionInfo(statement.getSession().getExecutionContext());
        if (session != null) {
            QMMStatementExecuteInfo exec = session.endExecution(statement, rows, error);
            if (exec != null) {
                tryFireMetaEvent(exec, QMEventAction.END, statement.getSession().getExecutionContext());
            }
        }
    }

    @Override
    public synchronized void handleResultSetOpen(@NotNull DBCResultSet resultSet)
    {
        QMMConnectionInfo session = getConnectionInfo(resultSet.getSession().getExecutionContext());
        if (session != null) {
            QMMStatementExecuteInfo exec = session.beginFetch(resultSet);
            if (exec != null) {
                tryFireMetaEvent(exec, QMEventAction.UPDATE, resultSet.getSession().getExecutionContext());
            }
        }
    }

    @Override
    public synchronized void handleResultSetClose(@NotNull DBCResultSet resultSet, long rowCount)
    {
        QMMConnectionInfo session = getConnectionInfo(resultSet.getSession().getExecutionContext());
        if (session != null) {
            QMMStatementExecuteInfo exec = session.endFetch(resultSet, rowCount);
            if (exec != null) {
                tryFireMetaEvent(exec, QMEventAction.UPDATE, resultSet.getSession().getExecutionContext());
            }
        }
    }

    // 虽然在 QMMCollectorImpl 中，但是却继承 AbstractJob 类，用来做事件的任务调度
    private class EventDispatcher extends AbstractJob {

        protected EventDispatcher()
        {
            super("QM meta events dispatcher");
            setUser(false);
            setSystem(true);
        }

        /*
        简单的说，就是一个执行事件的方法，使得作业可以进入 Worker 的 WorkerPool 中，等待被调用。
         */
        @Override
        protected IStatus run(DBRProgressMonitor monitor)
        {
            final List<QMMetaEvent> events;
            List<Long> sessionsToClose;
            // 加上外部类的对象锁，去事件池中获取事件
            synchronized (QMMCollectorImpl.this) {
                events = obtainEvents();
                sessionsToClose = closedConnections;
                closedConnections.clear();
            }
            // 处理事件
            if (!events.isEmpty()) {
                final List<QMMetaListener> listeners = getListeners();
                if (!listeners.isEmpty() && !events.isEmpty()) {
                    // 反向收集。新的事件必须放在首位。
                    // Reverse collection. Fresh events must come first.
                    Collections.reverse(events);
                    // 调度所有事件
                    // Dispatch all events
                    for (QMMetaListener listener : listeners) {
                        try {
                            listener.metaInfoChanged(monitor, events);
                        } catch (Throwable e) {
                            log.error("Error notifying event listener", e);
                        }
                    }
                }
                // 锁住 historySync 对象，记录历史事件
                synchronized (historySync) {
                    pastEvents.addAll(events);
                    int size = pastEvents.size();
                    if (size > MAX_HISTORY_EVENTS) {
                        pastEvents = new ArrayList<>(pastEvents.subList(
                            size - MAX_HISTORY_EVENTS,
                            size));
                    }
                }
            }
            // 锁住外部类的对象，清理关闭的会话
            // Cleanup closed sessions
            synchronized (QMMCollectorImpl.this) {
                for (Long sessionId : sessionsToClose) {
                    final QMMConnectionInfo session = connectionMap.get(sessionId);
                    if (session != null && !session.isClosed()) {
                        // It is possible (rarely) that session was reopened before event dispatcher run
                        // In that case just ignore it
                        connectionMap.remove(sessionId);
                    }
                }
            }
            // 加入作业队列
            if (isRunning()) {
                this.schedule(EVENT_DISPATCH_PERIOD);
            }
            return Status.OK_STATUS;
        }
    }

}
