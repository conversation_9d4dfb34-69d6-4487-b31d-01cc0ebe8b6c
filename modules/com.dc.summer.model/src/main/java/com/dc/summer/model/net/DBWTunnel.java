
package com.dc.summer.model.net;

import com.dc.summer.model.runtime.DBRProgressMonitor;
import com.dc.summer.DBException;

import java.io.IOException;

/**
 * Abstract tunnel
 */
public interface DBWTunnel extends DBW<PERSON>work<PERSON><PERSON><PERSON>, DBWForwarder {

    enum AuthCredentials {
        NONE,
        CREDENTIALS,
        PASSWORD
    }

    AuthCredentials getRequiredCredentials(DBWHandlerConfiguration configuration);

    void closeTunnel(DBRProgressMonitor monitor)
        throws DBException, IOException;

    Object getImplementation();

}
