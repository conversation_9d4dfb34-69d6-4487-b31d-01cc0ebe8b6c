
package com.dc.summer.model.impl.jdbc.data.handlers;

import com.dc.summer.Log;
import com.dc.summer.model.data.DBDContent;
import com.dc.summer.model.data.DBDValueDefaultGenerator;
import com.dc.summer.model.exec.DBCException;
import com.dc.summer.model.exec.jdbc.JDBCResultSet;
import com.dc.summer.model.struct.DBSTypedObject;
import com.dc.summer.utils.ContentUtils;
import com.dc.summer.utils.GeneralUtils;
import com.dc.code.NotNull;
import com.dc.summer.model.exec.DBCSession;
import com.dc.summer.model.exec.jdbc.JDBCPreparedStatement;
import com.dc.summer.model.exec.jdbc.JDBCSession;

import java.sql.SQLException;

/**
 * JDBC string value handler
 */
public class JDBCStringValueHandler extends JDBCAbstractValueHandler implements DBDValueDefaultGenerator {

    public static final JDBCStringValueHandler INSTANCE = new JDBCStringValueHandler();

    private static final Log log = Log.getLog(JDBCStringValueHandler.class);

    @Override
    protected Object fetchColumnValue(
        DBCSession session,
        JDBCResultSet resultSet,
        DBSTypedObject type,
        int index)
        throws SQLException
    {
        // Use getObject instead of getString because sometimes CHAR/VARCHAR holds something specific. E.g. FOR BIT DATA
        return resultSet.getObject(index);
    }

    @Override
    public void bindParameter(JDBCSession session, JDBCPreparedStatement statement, DBSTypedObject paramType,
                              int paramIndex, Object value)
        throws SQLException
    {
        if (value == null) {
            statement.setNull(paramIndex, paramType.getTypeID());
        } else {
            statement.setString(paramIndex, value.toString());
        }
    }

    @NotNull
    @Override
    public Class<String> getValueObjectType(@NotNull DBSTypedObject attribute)
    {
        return String.class;
    }

    @Override
    public Object getValueFromObject(@NotNull DBCSession session, @NotNull DBSTypedObject type, Object object, boolean copy, boolean validateValue) throws DBCException
    {
        if (object == null || object instanceof String) {
            return object;
        } else if (object instanceof char[]) {
            return new String((char[])object);
        } else if (object instanceof byte[]) {
            return new String((byte[])object);
        } else if (object instanceof DBDContent) {
            return ContentUtils.getContentStringValue(session.getProgressMonitor(), (DBDContent) object);
        } else if (object.getClass().isArray()) {
            // Special workaround for #798 - convert array to string (weird stuff)
            return GeneralUtils.makeDisplayString(object);
        } else {
            return object.toString();
        }
    }

    @Override
    public String getDefaultValueLabel() {
        return "Empty string";
    }

    @Override
    public Object generateDefaultValue(DBCSession session, DBSTypedObject type) {
        return "";
    }
}