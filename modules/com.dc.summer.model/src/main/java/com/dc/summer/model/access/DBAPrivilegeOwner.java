

package com.dc.summer.model.access;

import com.dc.summer.model.runtime.DBRProgressMonitor;
import com.dc.summer.DBException;
import com.dc.summer.model.struct.DBSObject;

import java.util.Collection;

/**
 * Permission owner
 */
public interface DBAPrivilegeOwner extends DBSObject {

    Collection<? extends DBAPrivilege> getPrivileges(DBRProgressMonitor monitor, boolean includeNestedObjects) throws DBException;

}