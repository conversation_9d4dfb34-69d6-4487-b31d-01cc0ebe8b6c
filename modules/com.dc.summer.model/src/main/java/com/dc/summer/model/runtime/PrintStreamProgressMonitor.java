
package com.dc.summer.model.runtime;

import java.io.PrintStream;

/**
 * Progress monitor with extra logging
 */
public class PrintStreamProgressMonitor extends ProxyProgressMonitor {

    private final PrintStream out;

    public PrintStreamProgressMonitor(DBRProgressMonitor monitor, PrintStream out) {
        super(monitor);
        this.out = out;
    }

    @Override
    public void beginTask(String name, int totalWork) {
        super.beginTask(name, totalWork);
        out.println(name);
    }

    @Override
    public void subTask(String name) {
        super.subTask(name);
        out.println("\t" + name);
    }
}