
package com.dc.summer.model.auth;

import com.dc.code.Nullable;

/**
 * Security manager credentials provider.
 */
public interface SMCredentialsProvider {
    /**
     * @return null if the user is not authorized
     */
    @Nullable
    SMCredentials getActiveUserCredentials();

    default boolean hasPermission(String permission) {
        var activeUserCredentials = getActiveUserCredentials();
        return activeUserCredentials != null && activeUserCredentials.hasPermission(permission);
    }
}
