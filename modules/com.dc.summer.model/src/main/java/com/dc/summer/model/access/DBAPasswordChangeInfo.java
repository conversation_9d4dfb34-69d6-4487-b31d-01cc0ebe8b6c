
package com.dc.summer.model.access;

/**
 * Password change info
 */
public class DBAPasswordChangeInfo {
    private String userName;
    private String oldPassword;
    private String newPassword;

    public DBAPasswordChangeInfo() {
    }

    public DBAPasswordChangeInfo(String userName, String oldPassword) {
        this.userName = userName;
        this.oldPassword = oldPassword;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getOldPassword() {
        return oldPassword;
    }

    public void setOldPassword(String oldPassword) {
        this.oldPassword = oldPassword;
    }

    public String getNewPassword() {
        return newPassword;
    }

    public void setNewPassword(String newPassword) {
        this.newPassword = newPassword;
    }
}
