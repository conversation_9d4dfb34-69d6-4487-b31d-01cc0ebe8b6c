
package com.dc.summer.model;

import com.dc.summer.DBException;
import com.dc.summer.model.auth.SMSessionContext;
import com.dc.summer.model.runtime.DBRProgressMonitor;
import com.dc.code.NotNull;
import com.dc.code.Nullable;

/**
 * Object with detailed info.
 */
public interface DBPObjectWithDetails<OBJECT> extends DBPObject {

    /**
     * Get additional object information.
     * Although this function required progress monitor it mustn't establish any remote database connections or require addional authentication.
     */
    @Nullable
    DBPObject getObjectDetails(@NotNull DBRProgressMonitor monitor, @NotNull SMSessionContext sessionContext, @NotNull OBJECT dataSource) throws DBException;

}
