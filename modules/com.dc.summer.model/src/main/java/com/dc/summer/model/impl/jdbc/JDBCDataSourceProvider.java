
package com.dc.summer.model.impl.jdbc;

import com.dc.summer.DBException;
import com.dc.summer.Log;
import com.dc.summer.model.DBConstants;
import com.dc.summer.model.connection.DBPDriver;
import com.dc.summer.model.impl.PropertyDescriptor;
import com.dc.summer.model.messages.ModelMessages;
import com.dc.summer.model.preferences.DBPPropertyDescriptor;
import com.dc.summer.model.runtime.DBRProgressMonitor;
import com.dc.code.NotNull;
import com.dc.summer.model.DBPDataSourceProvider;
import com.dc.summer.model.app.DBPPlatform;
import com.dc.summer.model.connection.DBPConnectionConfiguration;

import java.sql.Driver;
import java.sql.DriverPropertyInfo;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Properties;

/**
 * JDBCDataSourceProvider
 */
public abstract class JDBCDataSourceProvider implements DBPDataSourceProvider {
    static final protected Log log = Log.getLog(JDBCDataSourceProvider.class);

    @Override
    public void init(@NotNull DBPPlatform platform) {

    }

    @Override
    public DBPPropertyDescriptor[] getConnectionProperties(
        DBRProgressMonitor monitor,
        DBPDriver driver,
        DBPConnectionConfiguration connectionInfo)
        throws DBException {
        Collection<DBPPropertyDescriptor> props = null;
        if (driver.isInternalDriver()) {
            // Do not load properties from internal (ODBC) driver.
            // There is a bug in sun's JdbcOdbc bridge driver (#830): if connection fails during props reading
            // then all subsequent calls to openConnection will fail until another props reading will succeed.
            props = null;
        } else {
            Object driverInstance = driver.getDriverInstance(monitor);
            if (driverInstance instanceof Driver) {
                props = readDriverProperties(connectionInfo, (Driver) driverInstance);
            }
        }
        if (props == null) {
            return null;
        }
        return props.toArray(new DBPPropertyDescriptor[0]);
    }

    private Collection<DBPPropertyDescriptor> readDriverProperties(
        DBPConnectionConfiguration connectionInfo,
        Driver driver)
        throws DBException {
        Properties driverProps = new Properties();
        //driverProps.putAll(connectionInfo.getProperties());
        DriverPropertyInfo[] propDescs;
        try {
            propDescs = driver.getPropertyInfo(connectionInfo.getUrl(), driverProps);
        } catch (Throwable e) {
            log.debug("Cannot obtain driver's properties", e); //$NON-NLS-1$
            return null;
        }
        if (propDescs == null) {
            return null;
        }

        List<DBPPropertyDescriptor> properties = new ArrayList<>();
        for (DriverPropertyInfo desc : propDescs) {
            if (desc == null || DBConstants.DATA_SOURCE_PROPERTY_USER.equals(desc.name) || DBConstants.DATA_SOURCE_PROPERTY_PASSWORD.equals(desc.name)) {
                // Skip user/password properties
                continue;
            }
            desc.value = getConnectionPropertyDefaultValue(desc.name, desc.value);
            properties.add(new PropertyDescriptor(
                ModelMessages.model_jdbc_driver_properties,
                desc.name,
                desc.name,
                desc.description,
                String.class,
                desc.required,
                desc.value,
                desc.choices,
                true));
        }
        return properties;
    }

    protected String getConnectionPropertyDefaultValue(String name, String value) {
        return value;
    }
}
