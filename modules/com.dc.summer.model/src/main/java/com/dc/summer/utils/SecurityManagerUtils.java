
package com.dc.summer.utils;

import com.dc.summer.model.DBPDataSourceContainer;
import com.dc.summer.model.access.DBAAuthModel;
import com.dc.summer.model.connection.DBPDriver;
import com.dc.summer.model.connection.DBPDriverLibrary;
import com.dc.summer.runtime.DBWorkbench;
import org.osgi.framework.AdminPermission;

import java.io.File;
import java.io.FilePermission;
import java.lang.reflect.ReflectPermission;
import java.net.NetPermission;
import java.net.SocketPermission;
import java.security.*;
import java.util.ArrayList;
import java.util.List;
import java.util.PropertyPermission;
import java.util.concurrent.Callable;

public class SecurityManagerUtils {
    private static final List<Permission> DEFAULT_PERMISSIONS = List.of(new SocketPermission("*", "connect"),
        new NetPermission("*"),
        new ReflectPermission("*"),
        new AdminPermission(),
        new RuntimePermission("accessDeclaredMembers"),
        new PropertyPermission("*", "read"),
        new RuntimePermission("getClassLoader"),
        new RuntimePermission("createClassLoader"),
        new RuntimePermission("getenv.*")
    );

    public static List<Permission> getDefaultPermissions() {
        return new ArrayList<>(DEFAULT_PERMISSIONS);
    }

    public static <T> T executeWithAccessControlContext(AccessControlContext controlContext, Callable<T> callable) throws Throwable {
        try {
            return AccessController.doPrivileged((PrivilegedAction<T>) () -> {
                try {
                    return callable.call();
                } catch (Exception e) {
                    throw new RuntimeException(e);
                }
            }, controlContext);
        } catch (Throwable e) {
            Throwable throwable = e;
            if (throwable instanceof RuntimeException && throwable.getCause() != null) {
                throwable = throwable.getCause();
            }
            throw throwable;
        }
    }

    public static AccessControlContext controlContextOf(List<Permission> permissions) {

        Permissions noPermissions = new Permissions();
        for (Permission permission : permissions) {
            noPermissions.add(permission);
        }
        noPermissions.setReadOnly();

        return new AccessControlContext(
            new ProtectionDomain[]{new ProtectionDomain(null, noPermissions)}
        );
    }

    public static <T> T wrapDriverActions(DBPDataSourceContainer container, DBAAuthModel<?> authModel, Callable<T> callable) throws Throwable {
        var driver = container.getDriver();
        if (System.getSecurityManager() != null
            && DBWorkbench.getPlatform().getApplication().isMultiuser()
            && container.isAccessCheckRequired()
        ) {
            //unsecured connection created by user
            var permissions = SecurityManagerUtils.getDefaultPermissions();
            permissions.addAll(getDriverFilesPermissions(driver));
            return SecurityManagerUtils.executeWithAccessControlContext(SecurityManagerUtils.controlContextOf(permissions), callable);
        } else if (authModel != null) {
            return authModel.wrapDriverActions(container, callable);
        } else {
            return callable.call();
        }
    }

    private static List<Permission> getDriverFilesPermissions(DBPDriver driver) {
        var driverFilesPermissions = new ArrayList<Permission>();
        var driverLibraries = driver.getDriverLibraries();
        for (DBPDriverLibrary driverLibrary : driverLibraries) {
            File libraryFile = driverLibrary.getLocalFile();
            if (libraryFile == null) {
                continue;
            }
            //We need different permissions to work with a file by relative path and by absolute
            String relativeFilePath = libraryFile.getPath();
            String absoluteFilePath = libraryFile.getAbsolutePath();
            if (libraryFile.isDirectory()) {
                driverFilesPermissions.add(new FilePermission(relativeFilePath, "read")); // access to directory
                driverFilesPermissions.add(new FilePermission(absoluteFilePath, "read"));
                absoluteFilePath += (File.separator + "*"); //access to all files in directory
                relativeFilePath += (File.separator + "*");
            }
            driverFilesPermissions.add(new FilePermission(relativeFilePath, "read"));
            driverFilesPermissions.add(new FilePermission(absoluteFilePath, "read"));
        }
        return driverFilesPermissions;
    }

}
