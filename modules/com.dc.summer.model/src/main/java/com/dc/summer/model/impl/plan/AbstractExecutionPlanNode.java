

package com.dc.summer.model.impl.plan;

import com.dc.summer.model.exec.plan.DBCPlanNodeKind;
import com.dc.summer.model.exec.plan.DBCPlanNode;

/**
 * Abstract execution plan
 */
public abstract class AbstractExecutionPlanNode implements DBCPlanNode {

    @Override
    public DBCPlanNodeKind getNodeKind() {
        return DBCPlanNodeKind.DEFAULT;
    }

    @Override
    public String getNodeCondition() {
        return null;
    }

    @Override
    public String getNodeDescription() {
        return null;
    }
}
