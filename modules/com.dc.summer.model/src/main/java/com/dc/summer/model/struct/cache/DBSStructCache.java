

package com.dc.summer.model.struct.cache;

import com.dc.code.Nullable;
import com.dc.summer.DBException;
import com.dc.summer.model.runtime.DBRProgressMonitor;
import com.dc.summer.model.struct.DBSObject;

import java.util.Collection;

/**
 * Structure objects cache
 */
public interface DBSStructCache<OWNER extends DBSObject, OBJECT extends DBSObject, <PERSON><PERSON><PERSON> extends DBSObject>
    extends DBSObjectCache<OWNER, OBJECT>
{

    DBSObjectCache<OBJECT, CHILD> getChildrenCache(final OBJECT forObject);

    @Nullable
    Collection<CHILD> getChildren(DBRProgressMonitor monitor, OWNER owner, final OBJECT forObject)
        throws DBException;

    @Nullable
    CHILD getChild(DBRProgressMonitor monitor, OWNER owner, final OBJECT forObject, String objectName)
        throws DBException;

    void clearChildrenCache(OBJECT forParent);
}
