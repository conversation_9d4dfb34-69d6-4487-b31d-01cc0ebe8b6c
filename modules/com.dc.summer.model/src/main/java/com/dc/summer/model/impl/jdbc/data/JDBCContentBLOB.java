
package com.dc.summer.model.impl.jdbc.data;

import com.dc.code.NotNull;
import com.dc.summer.registry.center.Global;
import com.dc.summer.Log;
import com.dc.summer.ModelPreferences;
import com.dc.summer.model.DBPDataKind;
import com.dc.summer.model.DBValueFormatting;
import com.dc.summer.model.data.DBDContentCached;
import com.dc.summer.model.data.DBDContentStorage;
import com.dc.summer.model.data.DBDDisplayFormat;
import com.dc.summer.model.data.storage.BytesContentStorage;
import com.dc.summer.model.data.storage.TemporaryContentStorage;
import com.dc.summer.model.exec.DBCException;
import com.dc.summer.model.exec.DBCExecutionContext;
import com.dc.summer.model.exec.jdbc.JDBCPreparedStatement;
import com.dc.summer.model.exec.jdbc.JDBCSession;
import com.dc.summer.model.runtime.DBRProgressMonitor;
import com.dc.summer.model.struct.DBSTypedObject;
import com.dc.summer.utils.ContentUtils;
import com.dc.summer.utils.MimeTypes;

import java.io.*;
import java.sql.Blob;
import java.sql.SQLException;
import java.sql.SQLFeatureNotSupportedException;

/**
 * JDBCContentBLOB
 *
 * <AUTHOR> Rider
 */
public class JDBCContentBLOB extends JDBCContentLOB {

    private static final Log log = Log.getLog(JDBCContentBLOB.class);

    private Blob blob;
    private InputStream tmpStream;

    public JDBCContentBLOB(DBCExecutionContext dataSource, Blob blob) {
        super(dataSource);
        this.blob = blob;
    }

    @Override
    public long getLOBLength() throws DBCException {
        if (blob != null) {
            try {
                return blob.length();
            } catch (Throwable e) {
                throw new DBCException(e, executionContext);
            }
        }
        return 0;
    }

    @NotNull
    @Override
    public String getContentType()
    {
        return MimeTypes.OCTET_STREAM;
    }

    @Override
    public DBDContentStorage getContents(DBRProgressMonitor monitor)
        throws DBCException
    {
        if (storage == null && blob != null) {
            long contentLength = getContentLength();
            if (contentLength < ModelPreferences.getPreferences().getInt(ModelPreferences.MEMORY_CONTENT_MAX_SIZE)) {
                try {
                    try (InputStream bs = blob.getBinaryStream()) {
                        storage = BytesContentStorage.createFromStream(
                            bs,
                            contentLength,
                            getDefaultEncoding());
                    }
                } catch (IOException e) {
                    throw new DBCException("IO error while reading content", e);
                } catch (Throwable e) {
                    throw new DBCException(e, executionContext);
                }
            } else {
                // Create new local storage
                File tempFile;
                //                    tempFile = ContentUtils.createTempContentFile(monitor, platform, "blob" + blob.hashCode());
                tempFile = new File(Global.getEXPORT() + "blob-" + System.currentTimeMillis() + ".data");
                try (OutputStream os = new FileOutputStream(tempFile)) {
                    try (InputStream bs = blob.getBinaryStream()) {
                        ContentUtils.copyStreams(bs, contentLength, os, monitor);
                    }
                } catch (IOException e) {
                    ContentUtils.deleteTempFile(tempFile);
                    throw new DBCException("IO error while copying stream", e);
                } catch (Throwable e) {
                    ContentUtils.deleteTempFile(tempFile);
                    throw new DBCException(e, executionContext);
                }
                this.storage = new TemporaryContentStorage(tempFile, getDefaultEncoding(), true);
            }
            // Free blob - we don't need it anymore
            releaseBlob();
        }
        return storage;
    }

    @Override
    public void release()
    {
        releaseTempStream();
        releaseBlob();
        super.release();
    }

    public void releaseBlob() {
        if (blob != null) {
            try {
                blob.free();
            } catch (Throwable e) {
                // Log as warning only if it is an exception.
                // Errors just spam log
                log.debug("Error freeing BLOB: " + e.getClass().getName() + ": " + e.getMessage());
            }
            blob = null;
        }
    }

    private void releaseTempStream() {
        if (tmpStream != null) {
            ContentUtils.close(tmpStream);
            tmpStream = null;
        }
    }

    @Override
    public void bindParameter(JDBCSession session, JDBCPreparedStatement preparedStatement, DBSTypedObject columnType, int paramIndex)
        throws DBCException
    {
        try {
            if (storage != null) {
                // Write new blob value
                releaseTempStream();
                tmpStream = storage.getContentStream();
                try {
                    preparedStatement.setBinaryStream(paramIndex, tmpStream);
                }
                catch (Throwable e) {
                    try {
                        if (e instanceof SQLException) {
                            throw (SQLException)e;
                        } else {
                            try {
                                preparedStatement.setBinaryStream(paramIndex, tmpStream, storage.getContentLength());
                            }
                            catch (Throwable e1) {
                                if (e1 instanceof SQLException) {
                                    throw (SQLException)e1;
                                } else {
                                    preparedStatement.setBinaryStream(paramIndex, tmpStream, (int)storage.getContentLength());
                                }
                            }
                        }
                    } catch (SQLFeatureNotSupportedException e1) {
                        // Stream values seems to be unsupported
                        // Let's try bytes
                        int contentLength = (int) storage.getContentLength();
                        ByteArrayOutputStream buffer = new ByteArrayOutputStream(contentLength);
                        ContentUtils.copyStreams(tmpStream, contentLength, buffer, session.getProgressMonitor());
                        preparedStatement.setBytes(paramIndex, buffer.toByteArray());
                    }
                }
            } else if (blob != null) {
                try {
                    if (columnType.getDataKind() == DBPDataKind.BINARY) {
                        preparedStatement.setBinaryStream(paramIndex, blob.getBinaryStream());
                    } else {
                        preparedStatement.setBlob(paramIndex, blob);
                    }
                }
                catch (Throwable e0) {
                    // Write new blob value
                    releaseTempStream();
                    tmpStream = blob.getBinaryStream();
                    try {
                        preparedStatement.setBinaryStream(paramIndex, tmpStream);
                    }
                    catch (Throwable e) {
                        if (e instanceof SQLException) {
                            throw (SQLException)e;
                        } else {
                            try {
                                preparedStatement.setBinaryStream(paramIndex, tmpStream, blob.length());
                            }
                            catch (Throwable e1) {
                                if (e1 instanceof SQLException) {
                                    throw (SQLException)e1;
                                } else {
                                    preparedStatement.setBinaryStream(paramIndex, tmpStream, (int)blob.length());
                                }
                            }
                        }
                    }
                }
            } else {
                preparedStatement.setNull(paramIndex, java.sql.Types.BLOB);
            }
        }
        catch (SQLException e) {
            throw new DBCException(e, session.getExecutionContext());
        }
        catch (Throwable e) {
            throw new DBCException("Error while reading content", e);
        }
    }

    @Override
    public Object getRawValue() {
        return blob;
    }

    @Override
    public boolean isNull()
    {
        return blob == null && storage == null;
    }

    @Override
    protected JDBCContentLOB createNewContent()
    {
        return new JDBCContentBLOB(executionContext, null);
    }

    @Override
    public String getDisplayString(DBDDisplayFormat format)
    {
        if (blob == null && storage == null) {
            return null;
        }
        if (storage != null && storage instanceof DBDContentCached) {
            final Object cachedValue = ((DBDContentCached) storage).getCachedValue();
            if (cachedValue instanceof byte[]) {
                return DBValueFormatting.formatBinaryString(executionContext.getDataSource(), (byte[]) cachedValue, format);
            }
        }
        return "[BLOB]";
    }

}
