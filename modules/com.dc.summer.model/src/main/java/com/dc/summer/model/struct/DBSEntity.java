
package com.dc.summer.model.struct;

import com.dc.summer.DBException;
import com.dc.summer.model.runtime.DBRProgressMonitor;
import com.dc.code.NotNull;
import com.dc.code.Nullable;

import java.util.Collection;
import java.util.List;

/**
 * DBSEntity
 */
public interface DBSEntity extends DBSObject
{
    /**
     * Entity type
     * @return entity type
     */
    @NotNull
    DBSEntityType getEntityType();

    /**
     * Gets this entity attributes
     * @return attribute list
     * @throws DBException on any DB error
     * @param monitor progress monitor
     */
    @Nullable
    List<? extends DBSEntityAttribute> getAttributes(@NotNull DBRProgressMonitor monitor) throws DBException;

    /**
     * Retrieve attribute by it's name (case insensitive)
     * @param monitor progress monitor
     * @param attributeName column name  @return column or null
     * @throws DBException on any DB error
     */
    @Nullable
    DBSEntityAttribute getAttribute(@NotNull DBRProgressMonitor monitor, @NotNull String attributeName) throws DBException;

    /**
     * Gets this entity constraints
     * @return association list
     * @throws DBException on any DB error
     * @param monitor progress monitor
     */
    @Nullable
    Collection<? extends DBSEntityConstraint> getConstraints(@NotNull DBRProgressMonitor monitor) throws DBException;

    /**
     * Gets this entity associations
     * @return association list
     * @throws DBException on any DB error
     * @param monitor progress monitor
     */
    @Nullable
    Collection<? extends DBSEntityAssociation> getAssociations(@NotNull DBRProgressMonitor monitor) throws DBException;

    /**
     * Gets associations which refers this entity
     * @return reference association list
     * @throws DBException on any DB error
     * @param monitor progress monitor
     */
    @Nullable
    Collection<? extends DBSEntityAssociation> getReferences(@NotNull DBRProgressMonitor monitor) throws DBException;

}
