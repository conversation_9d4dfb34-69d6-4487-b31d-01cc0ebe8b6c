
package com.dc.summer.model.data.storage;

import com.dc.summer.registry.center.Global;
import com.dc.summer.model.runtime.DBRProgressMonitor;
import com.dc.summer.utils.ContentUtils;
import com.dc.code.NotNull;
import com.dc.summer.model.app.DBPPlatform;
import com.dc.summer.model.data.DBDContentStorage;
import com.dc.summer.utils.GeneralUtils;

import java.io.*;

/**
 * File content storage
 */
public class ExternalContentStorage implements DBDContentStorage {

    @NotNull
    private final DBPPlatform platform;
    @NotNull
    private File file;
    private String charset;

    public ExternalContentStorage(@NotNull DBPPlatform platform, @NotNull File file)
    {
        this(platform, file, null);
    }

    public ExternalContentStorage(@NotNull DBPPlatform platform, @NotNull File file, String charset)
    {
        this.platform = platform;
        this.file = file;
        this.charset = charset;
    }

    @NotNull
    public File getFile() {
        return file;
    }

    @NotNull
    @Override
    public InputStream getContentStream()
        throws IOException
    {
        return new FileInputStream(file);
    }

    @NotNull
    @Override
    public Reader getContentReader()
        throws IOException
    {
        if (charset == null) {
            return new InputStreamReader(new FileInputStream(file), GeneralUtils.DEFAULT_ENCODING);
        } else {
            return new InputStreamReader(new FileInputStream(file), charset);
        }
    }

    @Override
    public long getContentLength()
    {
        return file.length();
    }

    @Override
    public String getCharset()
    {
        return charset;
    }

    @Override
    public DBDContentStorage cloneStorage(DBRProgressMonitor monitor)
        throws IOException
    {
        // Create new local storage
//        File tempFile = ContentUtils.createTempContentFile(monitor, platform, "copy" + this.hashCode());
        File tempFile = new File(Global.getEXPORT() + "copy-" + System.currentTimeMillis() + ".data");
        try {
            try (InputStream is = new FileInputStream(file)) {
                try (OutputStream os = new FileOutputStream(tempFile)) {
                    ContentUtils.copyStreams(is, file.length(), os, monitor);
                }
            }
        } catch (IOException e) {
            ContentUtils.deleteTempFile(tempFile);
            throw new IOException(e);
        }
        return new TemporaryContentStorage(tempFile, charset, true);
    }

    @Override
    public void release()
    {
        // Do nothing
/*
        if (!file.delete()) {
            log.warn("Could not delete temporary file");
        }
*/
    }
}