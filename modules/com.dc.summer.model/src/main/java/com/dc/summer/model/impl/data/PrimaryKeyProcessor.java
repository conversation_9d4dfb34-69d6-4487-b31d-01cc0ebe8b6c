package com.dc.summer.model.impl.data;

import com.dc.summer.ModelPreferences;
import com.dc.summer.model.DBPDataKind;
import com.dc.summer.model.data.DBDAttributeBinding;
import com.dc.summer.model.data.DBDAttributeBindingProcessor;
import lombok.Data;
import lombok.Getter;

import java.util.List;

@Data
public class PrimaryKeyProcessor implements DBDAttributeBindingProcessor {

    private boolean hasPrimaryKeys;

    @Getter
    private List<String> primaryKeyColumns;

    private String tableName;

    public PrimaryKeyProcessor(List<String> primaryKeyColumns, String tableName) {
        this.hasPrimaryKeys = primaryKeyColumns != null && !primaryKeyColumns.isEmpty();
        this.primaryKeyColumns = primaryKeyColumns == null ? List.of() : primaryKeyColumns;
        this.tableName = tableName;
    }

    public PrimaryKeyProcessor() {
        this(null, null);
    }

    @SuppressWarnings("OptionalGetWithoutIsPresent")
    @Override
    public void processAndBind(DBDAttributeBinding[] bindings) {
        //默认当前查询无主键
        hasPrimaryKeys = false;
        String rowKey = null;
        for (DBDAttributeBinding binding : bindings) {
            //判断查出的列中是否有主键列
            if (binding.getName() == null) {
                continue; // union all , 会出现空值的情况
            }
            if (primaryKeyColumns.stream().anyMatch(binding.getName()::equals)) {
                if (binding.isDesensitized()) {
                    String pk = primaryKeyColumns.stream().filter(binding.getName()::equals).findFirst().get();
                    primaryKeyColumns.remove(pk);
                } else {
                    hasPrimaryKeys = true;
                }
            }
            if (tableName != null && !tableName.isBlank()) {
                String label = ModelPreferences.getPreferences().getString(ModelPreferences.VIRTUAL_ROW_ID_LABEL);
                if (label.equals(binding.getLabel()) || ("ROWID".equalsIgnoreCase(binding.getLabel()) && "ROWID".equalsIgnoreCase(binding.getTypeName()))) {
                    rowKey = binding.getName();
                }
                if (bindings.length == 1 && binding.getDataKind() == DBPDataKind.DOCUMENT) {
                    rowKey = "_id";
                }
            }
        }
        String rowId = rowKey;
        if (!hasPrimaryKeys && rowKey != null && !rowKey.isBlank()) {
            hasPrimaryKeys = true;
            primaryKeyColumns = List.of(rowId);
        }

    }

}
