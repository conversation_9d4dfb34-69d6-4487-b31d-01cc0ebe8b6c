

package com.dc.summer.model.exec;

import com.dc.summer.model.DBPDataSource;

/**
 * Connect exception
 */
public class DBCConnectException extends DBCException
{
    private static final long serialVersionUID = 1L;

    public DBCConnectException(String message, Throwable cause, DBPDataSource dataSource) {
        super(message, cause, dataSource);
    }

    public DBCConnectException(String message, Throwable cause) {
        super(message, cause);
    }

}
