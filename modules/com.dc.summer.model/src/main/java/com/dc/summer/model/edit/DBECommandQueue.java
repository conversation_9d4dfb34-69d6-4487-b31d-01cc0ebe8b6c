

package com.dc.summer.model.edit;

import com.dc.summer.model.DBPObject;

import java.util.Collection;

/**
 * Object commander.
 * Provides facilities for object edit commands, undo/redo, save/revert
 */
public interface DBECommandQueue<OBJECT_TYPE extends DBPObject> extends Collection<DBECommand<OBJECT_TYPE>> {

    OBJECT_TYPE getObject();

    DBECommandQueue getParentQueue();

    Collection<DBECommandQueue> getSubQueues();

}