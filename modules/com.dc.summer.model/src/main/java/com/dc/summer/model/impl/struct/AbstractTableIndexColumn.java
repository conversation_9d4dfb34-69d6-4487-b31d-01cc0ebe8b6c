
package com.dc.summer.model.impl.struct;

import com.dc.summer.model.DBValueFormatting;
import com.dc.summer.model.struct.DBSEntityAttribute;
import com.dc.code.Nullable;
import com.dc.summer.model.struct.rdb.DBSTableIndexColumn;

/**
 * AbstractTableIndexColumn
 */
public abstract class AbstractTableIndexColumn implements DBSTableIndexColumn
{

    @Override
    public boolean isPersisted()
    {
        return true;
    }

    @Nullable
    @Override
    public DBSEntityAttribute getAttribute()
    {
        return getTableColumn();
    }
}
