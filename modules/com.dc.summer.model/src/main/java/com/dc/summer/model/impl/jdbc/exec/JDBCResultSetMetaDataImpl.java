
package com.dc.summer.model.impl.jdbc.exec;

import com.dc.summer.model.exec.DBCAttributeMetaData;
import com.dc.summer.model.exec.jdbc.JDBCResultSetMetaData;
import com.dc.summer.model.impl.jdbc.JDBCUtils;
import com.dc.code.Nullable;
import com.dc.summer.model.DBUtils;
import com.dc.summer.model.exec.jdbc.JDBCResultSet;
import com.dc.utils.CommonUtils;

import java.sql.ResultSetMetaData;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * JDBCResultSetMetaDataImpl
 */
public class JDBCResultSetMetaDataImpl implements JDBCResultSetMetaData
{
    protected JDBCResultSet resultSet;
    protected ResultSetMetaData original;
    protected List<DBCAttributeMetaData> columns = new ArrayList<>();
    protected Map<String, JDBCTableMetaData> tables = new HashMap<>();

    public JDBCResultSetMetaDataImpl(JDBCResultSet resultSet)
        throws SQLException
    {
        this.resultSet = resultSet;
        this.original = resultSet.getOriginal().getMetaData();
        int count = original.getColumnCount();
        for (int i = 0; i < count; i++) {
            columns.add(createColumnMetaDataImpl(i));
        }
    }

    protected JDBCColumnMetaData createColumnMetaDataImpl(int index) throws SQLException
    {
        return new JDBCColumnMetaData(this, index);
    }

    public JDBCResultSet getResultSet()
    {
        return resultSet;
    }

    public ResultSetMetaData getOriginal()
    {
        return original;
    }

    @Override
    public List<DBCAttributeMetaData> getAttributes()
    {
        return columns;
    }

    @Nullable
    public JDBCTableMetaData getTableMetaData(String catalogName, String schemaName, String tableName)
    {
        if (CommonUtils.isEmpty(tableName)) {
            // some constant instead of table name
            return null;
        }
        String fullQualifiedName = DBUtils.getSimpleQualifiedName(catalogName, schemaName, tableName);

        JDBCTableMetaData tableMetaData = tables.get(fullQualifiedName);
        if (tableMetaData == null) {
            tableMetaData = new JDBCTableMetaData(this, catalogName, schemaName, tableName);
            tables.put(fullQualifiedName, tableMetaData);
        }
        return tableMetaData;
    }

    @Override
    public int getColumnCount()
        throws SQLException
    {
        return original.getColumnCount();
    }

    @Override
    public boolean isAutoIncrement(int column)
        throws SQLException
    {
        return original.isAutoIncrement(column);
    }

    @Override
    public boolean isCaseSensitive(int column)
        throws SQLException
    {
        return original.isCaseSensitive(column);
    }

    @Override
    public boolean isSearchable(int column)
        throws SQLException
    {
        return original.isSearchable(column);
    }

    @Override
    public boolean isCurrency(int column)
        throws SQLException
    {
        return original.isCurrency(column);
    }

    @Override
    public int isNullable(int column)
        throws SQLException
    {
        return original.isNullable(column);
    }

    @Override
    public boolean isSigned(int column)
        throws SQLException
    {
        return original.isSigned(column);
    }

    @Override
    public int getColumnDisplaySize(int column)
        throws SQLException
    {
        return original.getColumnDisplaySize(column);
    }

    @Override
    public String getColumnLabel(int column)
        throws SQLException
    {
        return JDBCUtils.normalizeIdentifier(original.getColumnLabel(column));
    }

    @Override
    public String getColumnName(int column)
        throws SQLException
    {
        return JDBCUtils.normalizeIdentifier(original.getColumnName(column));
    }

    @Override
    public String getSchemaName(int column)
        throws SQLException
    {
        return JDBCUtils.normalizeIdentifier(original.getSchemaName(column));
    }

    @Override
    public int getPrecision(int column)
        throws SQLException
    {
        return original.getPrecision(column);
    }

    @Override
    public int getScale(int column)
        throws SQLException
    {
        return original.getScale(column);
    }

    @Override
    public String getTableName(int column)
        throws SQLException
    {
        return JDBCUtils.normalizeIdentifier(original.getTableName(column));
    }

    @Override
    public String getCatalogName(int column)
        throws SQLException
    {
        return JDBCUtils.normalizeIdentifier(original.getCatalogName(column));
    }

    @Override
    public int getColumnType(int column)
        throws SQLException
    {
        return original.getColumnType(column);
    }

    @Override
    public String getColumnTypeName(int column)
        throws SQLException
    {
        return JDBCUtils.normalizeIdentifier(original.getColumnTypeName(column));
    }

    @Override
    public boolean isReadOnly(int column)
        throws SQLException
    {
        return original.isReadOnly(column);
    }

    @Override
    public boolean isWritable(int column)
        throws SQLException
    {
        return original.isWritable(column);
    }

    @Override
    public boolean isDefinitelyWritable(int column)
        throws SQLException
    {
        return original.isDefinitelyWritable(column);
    }

    @Override
    public String getColumnClassName(int column)
        throws SQLException
    {
        return JDBCUtils.normalizeIdentifier(original.getColumnClassName(column));
    }

    @Override
    public <T> T unwrap(Class<T> iface)
        throws SQLException
    {
        return original.unwrap(iface);
    }

    @Override
    public boolean isWrapperFor(Class<?> iface)
        throws SQLException
    {
        return original.isWrapperFor(iface);
    }
}
