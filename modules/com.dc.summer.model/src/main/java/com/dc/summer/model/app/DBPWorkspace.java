

package com.dc.summer.model.app;

import com.dc.code.NotNull;
import com.dc.code.Nullable;
import com.dc.summer.model.auth.SMAuthSpace;
import com.dc.summer.model.auth.SMSessionContext;

import java.nio.file.Path;
import java.util.List;

/**
 * DBPWorkspace
 */
public interface DBPWorkspace extends SMAuthSpace
{
    String METADATA_FOLDER = ".metadata";

    @NotNull
    DBPPlatform getPlatform();

    @NotNull
    String getWorkspaceId();

    boolean isActive();

    @NotNull
    Path getAbsolutePath();

    @NotNull
    Path getMetadataFolder();

    @NotNull
    List<DBPProject> getProjects();
    DBPProject getActiveProject();
    DBPProject getProject(@NotNull String projectName);

    /**
     * Workspace auth context
     */
    @NotNull
    SMSessionContext getAuthContext();

    @Nullable
    DBPDataSourceRegistry getDefaultDataSourceRegistry();

    void dispose();

}
