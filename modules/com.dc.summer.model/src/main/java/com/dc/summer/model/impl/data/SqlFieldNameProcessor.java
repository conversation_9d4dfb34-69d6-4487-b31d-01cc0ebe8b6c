package com.dc.summer.model.impl.data;

import com.dc.summer.model.data.DBDAttributeBinding;
import com.dc.summer.model.data.DBDAttributeBindingProcessor;
import com.dc.summer.model.proxy.impl.DBDAttributeBindingMetaProxy;
import com.dc.summer.model.sql.SqlFieldData;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

public class SqlFieldNameProcessor implements DBDAttributeBindingProcessor {

    private final List<SqlFieldData> sqlFieldDataList;

    public SqlFieldNameProcessor(List<SqlFieldData> sqlFieldDataList) {
        this.sqlFieldDataList = sqlFieldDataList;
    }

    @Override
    public void processAndBind(DBDAttributeBinding[] bindings) {

        if (CollectionUtils.isEmpty(sqlFieldDataList)) {
            return;
        }

        int length = bindings.length;

        // 未处理的绑定下标
        Set<Integer> indexes = new HashSet<>();

        // 全尺寸匹配，根据别名绑定真实名
        if (length == sqlFieldDataList.size()) {

            for (int i = 0; i < bindings.length; i++) {
                DBDAttributeBinding binding = bindings[i];
                SqlFieldData sqlFieldData = sqlFieldDataList.get(i);
                if (!sqlFieldData.isWithinFunc() && sqlFieldData.getFieldName() != null && !binding.getName().equalsIgnoreCase(sqlFieldData.getFieldName())) {

                    if (binding.getLabel().equalsIgnoreCase(sqlFieldData.getFieldAlias())) {
                        bindings[i] = new DBDAttributeBindingMetaProxy(binding, sqlFieldData.getFieldName()).getInstance();
                    } else {
                        indexes.add(i);
                    }
                }
            }

        }
        // 尺寸不匹配
        else {
            IntStream.range(0, length).forEach(indexes::add);
        }

        if (CollectionUtils.isNotEmpty(indexes)) {

            Map<String, List<SqlFieldData>> groupAlias = sqlFieldDataList
                    .stream()
                    .filter(sqlFieldData -> StringUtils.isNotBlank(sqlFieldData.getFieldAlias()))
                    .collect(Collectors.groupingBy(sqlFieldData -> sqlFieldData.getFieldAlias().toUpperCase(Locale.ROOT)));

            for (Integer index : indexes) {

                DBDAttributeBinding binding = bindings[index];

                List<SqlFieldData> labelData = groupAlias.get(binding.getMetaAttribute().getLabel().toUpperCase(Locale.ROOT));

                // 1. 匹配唯一别名
                if (CollectionUtils.isNotEmpty(labelData) && labelData.size() == 1 && labelData.get(0).getFieldName() != null) {
                    bindings[index] = new DBDAttributeBindingMetaProxy(binding, labelData.get(0).getFieldName()).getInstance();
                }

            }

        }

    }

}
