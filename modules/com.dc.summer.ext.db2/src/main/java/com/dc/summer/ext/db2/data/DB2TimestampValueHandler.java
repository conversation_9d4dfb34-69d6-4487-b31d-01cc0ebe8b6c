
package com.dc.summer.ext.db2.data;

import com.dc.summer.model.data.DBDFormatSettings;
import com.dc.summer.model.struct.DBSTypedObject;
import com.dc.code.Nullable;
import com.dc.summer.model.impl.jdbc.data.handlers.JDBCDateTimeValueHandler;
import com.dc.utils.time.ExtendedDateFormat;

import java.sql.Types;
import java.text.Format;
import java.text.SimpleDateFormat;

/**
 * Object type support
 */
public class DB2TimestampValueHandler extends JDBCDateTimeValueHandler {

    private final SimpleDateFormat DEFAULT_DATETIME_FORMAT = new ExtendedDateFormat("''yyyy-MM-dd HH:mm:ss.ffffff''");

    public DB2TimestampValueHandler(DBDFormatSettings formatSettings)
    {
        super(formatSettings);
    }

    @Nullable
    @Override
    public Format getNativeValueFormat(DBSTypedObject type) {
        switch (type.getTypeID()) {
            case Types.TIMESTAMP:
                return DEFAULT_DATETIME_FORMAT;
        }
        return super.getNativeValueFormat(type);
    }

}
