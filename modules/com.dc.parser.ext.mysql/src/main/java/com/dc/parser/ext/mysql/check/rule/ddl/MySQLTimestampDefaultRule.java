package com.dc.parser.ext.mysql.check.rule.ddl;

import com.dc.parser.ext.mysql.segment.MySQLColumnDefinitionSegment;
import com.dc.parser.ext.mysql.statement.ddl.MySQLAlterTableStatement;
import com.dc.parser.ext.mysql.utils.MySQLAlterTableHelper;
import com.dc.parser.model.enums.CheckRuleDatabasePrefix;
import com.dc.parser.model.enums.CheckRuleUniqueKey;
import com.dc.parser.model.check.rule.CheckResult;
import com.dc.parser.model.check.rule.CheckRuleParameter;
import com.dc.parser.model.check.rule.SQLRule;
import com.dc.parser.model.segment.ddl.column.ColumnDefinitionSegment;
import com.dc.parser.model.statement.SQLStatement;
import com.dc.parser.model.statement.ddl.CreateTableStatement;

import java.util.Optional;
import java.util.stream.Stream;

import static com.dc.parser.ext.mysql.utils.MySQLCreateTableHelper.getColumnDefinitionsFromAlterTableStatement;

public class MySQLTimestampDefaultRule implements SQLRule {

    private final String TIMESTAMP_DATATYPE = "TIMESTAMP";

    @Override
    public String getType() {
        return CheckRuleDatabasePrefix.MYSQL_.getValue() + CheckRuleUniqueKey.DDL_CHECK_TIMESTAMP_DEFAULT_VALUE.getValue();
    }

    @Override
    public CheckResult check(SQLStatement sqlStatement, CheckRuleParameter parameter) {

        boolean hasDefaultValue = true;

        if (sqlStatement instanceof CreateTableStatement) {
            CreateTableStatement createTableStatement = (CreateTableStatement) sqlStatement;

            Stream<MySQLColumnDefinitionSegment> columnDefinitionsFromAlterTableStatement = getColumnDefinitionsFromAlterTableStatement(createTableStatement);

            hasDefaultValue = checkColumnDefaultValue(columnDefinitionsFromAlterTableStatement);

        } else if (sqlStatement instanceof MySQLAlterTableStatement) {
            MySQLAlterTableStatement alterTableStatement = (MySQLAlterTableStatement) sqlStatement;

            Stream<MySQLColumnDefinitionSegment> columnDefinitionSegmentStream = MySQLAlterTableHelper.getColumnDefinitionsFromAlterTableStatement(alterTableStatement);

            hasDefaultValue = checkColumnDefaultValue(columnDefinitionSegmentStream);
        }

        if (!hasDefaultValue) {
            return CheckResult.buildFailResult(parameter.getCheckRuleContent());
        }

        return CheckResult.DEFAULT_SUCCESS_RESULT;
    }

    public boolean checkColumnDefaultValue(Stream<? extends ColumnDefinitionSegment> columnDefinitionSegmentStream) {
        return columnDefinitionSegmentStream.filter(columnDefinitionSegment -> TIMESTAMP_DATATYPE.equalsIgnoreCase(columnDefinitionSegment.getDataType().getDataTypeName()))
                .map(ColumnDefinitionSegment::getDefaultSegment)
                .allMatch(Optional::isPresent);
    }
}
