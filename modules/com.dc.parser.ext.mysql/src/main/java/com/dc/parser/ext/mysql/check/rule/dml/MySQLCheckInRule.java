package com.dc.parser.ext.mysql.check.rule.dml;

import com.dc.parser.ext.mysql.check.rule.listener.MySQLPredicateInListener;
import com.dc.parser.model.enums.CheckRuleDatabasePrefix;
import com.dc.parser.model.enums.CheckRuleUniqueKey;
import com.dc.parser.model.check.rule.CheckResult;
import com.dc.parser.model.check.rule.CheckRuleParameter;
import com.dc.parser.model.check.rule.SQLRule;
import com.dc.parser.model.statement.SQLStatement;
import org.antlr.v4.runtime.tree.ParseTreeWalker;

public class MySQLCheckInRule implements SQLRule {

    private final ParseTreeWalker parseTreeWalker = ParseTreeWalker.DEFAULT;

    @Override
    public String getType() {
        return CheckRuleDatabasePrefix.MYSQL_.getValue() + CheckRuleUniqueKey.DML_CHECK_IN.getValue();
    }

    @Override
    public CheckResult check(SQLStatement sqlStatement, CheckRuleParameter parameter) {

        if (sqlStatement.getParseTree() != null) {
            MySQLPredicateInListener listener = new MySQLPredicateInListener();
            parseTreeWalker.walk(listener, sqlStatement.getParseTree());
            if (listener.isHasInPredicate()) {
                return CheckResult.buildFailResult(parameter.getCheckRuleContent());
            }
        }
        return CheckResult.DEFAULT_SUCCESS_RESULT;
    }
}
