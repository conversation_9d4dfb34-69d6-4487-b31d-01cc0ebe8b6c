package com.dc.parser.ext.mysql.check.rule.listener;

import com.dc.parser.ext.mysql.parser.autogen.MySQLStatementBaseListener;
import com.dc.parser.ext.mysql.parser.autogen.MySQLStatementParser;
import lombok.Getter;

@Getter
public class MySQLCountColListener extends MySQLStatementBaseListener {

    private boolean existsCountCol = false;

    @Override
    public void enterAggregationFunction(MySQLStatementParser.AggregationFunctionContext ctx) {
        if (existsCountCol) {
            return;
        }
        if (ctx.aggregationFunctionName().COUNT() != null) {
            this.existsCountCol = ctx.ASTERISK_() == null;
        }
    }
}
