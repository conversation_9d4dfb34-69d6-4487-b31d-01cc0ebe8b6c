package com.dc.parser.ext.mysql.check.rule.listener;

import com.dc.parser.ext.mysql.parser.autogen.MySQLStatementBaseListener;
import com.dc.parser.ext.mysql.parser.autogen.MySQLStatementParser;
import lombok.Getter;

import java.util.concurrent.atomic.AtomicBoolean;

@Getter
public class MySQLOrderDirectionListener extends MySQLStatementBaseListener {

    private final AtomicBoolean hasdiffDirection = new AtomicBoolean(false);

    @Override
    public void enterOrderByClause(MySQLStatementParser.OrderByClauseContext ctx) {
        boolean hasAsc = false;
        boolean hasDesc = false;

        for (MySQLStatementParser.OrderByItemContext orderByItemContext : ctx.orderByItem()) {
            if (orderByItemContext.direction() == null) {
                hasAsc = true;
                continue;
            }
            if (orderByItemContext.direction().ASC() != null) {
                hasAsc = true;
            } else if (orderByItemContext.direction().DESC() != null) {
                hasDesc = true;
            }
        }
        hasdiffDirection.compareAndSet(false, hasDesc && hasAsc);
    }
}
