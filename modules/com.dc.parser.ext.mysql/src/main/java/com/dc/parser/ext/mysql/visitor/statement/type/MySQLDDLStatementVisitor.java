package com.dc.parser.ext.mysql.visitor.statement.type;

import com.dc.parser.ext.mysql.parser.autogen.MySQLStatementParser.*;
import com.dc.parser.ext.mysql.segment.*;
import com.dc.parser.ext.mysql.statement.ddl.*;
import com.dc.parser.ext.mysql.statement.dml.MySQLDeleteStatement;
import com.dc.parser.ext.mysql.statement.dml.MySQLInsertStatement;
import com.dc.parser.ext.mysql.statement.dml.MySQLSelectStatement;
import com.dc.parser.ext.mysql.statement.dml.MySQLUpdateStatement;
import com.dc.parser.ext.mysql.visitor.statement.MySQLStatementVisitor;
import com.dc.parser.model.api.ASTNode;
import com.dc.parser.model.api.visitor.statement.type.DDLStatementVisitor;
import com.dc.parser.model.enums.AlgorithmOption;
import com.dc.parser.model.enums.LockTableOption;
import com.dc.parser.model.segment.ddl.AlterDefinitionSegment;
import com.dc.parser.model.segment.ddl.charset.CharsetNameSegment;
import com.dc.parser.model.segment.ddl.collation.CollationNameSegment;
import com.dc.parser.model.segment.ddl.column.ColumnDefinitionSegment;
import com.dc.parser.model.segment.ddl.column.alter.AddColumnDefinitionSegment;
import com.dc.parser.model.segment.ddl.column.alter.DropColumnDefinitionSegment;
import com.dc.parser.model.segment.ddl.column.alter.RenameColumnSegment;
import com.dc.parser.model.segment.ddl.column.position.ColumnAfterPositionSegment;
import com.dc.parser.model.segment.ddl.column.position.ColumnFirstPositionSegment;
import com.dc.parser.model.segment.ddl.column.position.ColumnPositionSegment;
import com.dc.parser.model.segment.ddl.constraint.ConstraintSegment;
import com.dc.parser.model.segment.ddl.constraint.DropForeignKeySegment;
import com.dc.parser.model.segment.ddl.constraint.DropPrimaryKeySegment;
import com.dc.parser.model.segment.ddl.constraint.alter.AddConstraintDefinitionSegment;
import com.dc.parser.model.segment.ddl.constraint.alter.DropConstraintDefinitionSegment;
import com.dc.parser.model.segment.ddl.constraint.alter.ModifyConstraintDefinitionSegment;
import com.dc.parser.model.segment.ddl.engine.EngineSegment;
import com.dc.parser.model.segment.ddl.index.DropIndexDefinitionSegment;
import com.dc.parser.model.segment.ddl.index.IndexNameSegment;
import com.dc.parser.model.segment.ddl.index.IndexSegment;
import com.dc.parser.model.segment.ddl.index.RenameIndexDefinitionSegment;
import com.dc.parser.model.segment.ddl.routine.*;
import com.dc.parser.model.segment.ddl.table.*;
import com.dc.parser.model.segment.ddl.tablespace.TablespaceSegment;
import com.dc.parser.model.segment.ddl.trigger.TriggerNameSegment;
import com.dc.parser.model.segment.dml.column.ColumnSegment;
import com.dc.parser.model.segment.dml.expr.simple.SimpleExpressionSegment;
import com.dc.parser.model.segment.generic.*;
import com.dc.parser.model.segment.generic.table.SimpleTableSegment;
import com.dc.parser.model.statement.SQLStatement;
import com.dc.parser.model.value.collection.CollectionValue;
import com.dc.parser.model.value.identifier.IdentifierValue;
import com.dc.parser.model.value.literal.impl.NumberLiteralValue;
import com.google.common.base.Preconditions;
import org.antlr.v4.runtime.ParserRuleContext;
import org.antlr.v4.runtime.misc.Interval;

import java.util.Collection;
import java.util.Collections;
import java.util.LinkedList;
import java.util.List;

/**
 * DDL statement visitor for MySQL.
 */
public final class MySQLDDLStatementVisitor extends MySQLStatementVisitor implements DDLStatementVisitor {
    
    @Override
    public ASTNode visitCreateView(final CreateViewContext ctx) {
        MySQLCreateViewStatement result = new MySQLCreateViewStatement();
        result.setReplaceView(null != ctx.REPLACE());
        result.setView((SimpleTableSegment) visit(ctx.viewName()));
        result.setViewDefinition(getOriginalText(ctx.select()));
        result.setSelect((MySQLSelectStatement) visit(ctx.select()));
        return result;
    }
    
    @Override
    public ASTNode visitAlterView(final AlterViewContext ctx) {
        MySQLAlterViewStatement result = new MySQLAlterViewStatement();
        result.setView((SimpleTableSegment) visit(ctx.viewName()));
        result.setViewDefinition(getOriginalText(ctx.select()));
        result.setSelect((MySQLSelectStatement) visit(ctx.select()));
        return result;
    }
    
    @SuppressWarnings("unchecked")
    @Override
    public ASTNode visitDropView(final DropViewContext ctx) {
        MySQLDropViewStatement result = new MySQLDropViewStatement();
        result.setIfExists(null != ctx.ifExists());
        result.getViews().addAll(((CollectionValue<SimpleTableSegment>) visit(ctx.viewNames())).getValue());
        return result;
    }
    
    @Override
    public ASTNode visitCreateDatabase(final CreateDatabaseContext ctx) {
        MySQLCreateDatabaseStatement result = new MySQLCreateDatabaseStatement();
        result.setDatabaseName((DatabaseSegment) visit(ctx.databaseName()));
        result.setIfNotExists(null != ctx.ifNotExists());
        for (CreateDatabaseSpecification_Context each : ctx.createDatabaseSpecification_()) {
            if (null != each.defaultCharset()) {
                result.setCharsetName((CharsetNameSegment) visit(each.defaultCharset()));
            } else if (null != each.defaultCollation()) {
                result.setCollationName((CollationNameSegment) visit(each.defaultCollation()));
            }
        }
        return result;
    }
    
    @Override
    public ASTNode visitAlterDatabase(final AlterDatabaseContext ctx) {
        MySQLAlterDatabaseStatement result = new MySQLAlterDatabaseStatement();
        result.setDatabaseName((DatabaseSegment) visit(ctx.databaseName()));
        for (AlterDatabaseSpecification_Context each : ctx.alterDatabaseSpecification_()) {
            CreateDatabaseSpecification_Context specification = each.createDatabaseSpecification_();
            if (null != specification.defaultCharset()) {
                result.setCharsetName((CharsetNameSegment) visit(specification.defaultCharset()));
            }
            if (null != specification.defaultCollation()) {
                result.setCollationName((CollationNameSegment) visit(specification.defaultCollation()));
            }
        }
        return result;
    }
    
    @Override
    public ASTNode visitDropDatabase(final DropDatabaseContext ctx) {
        MySQLDropDatabaseStatement result = new MySQLDropDatabaseStatement();
        result.setDatabaseName((DatabaseSegment) visit(ctx.databaseName()));
        result.setIfExists(null != ctx.ifExists());
        return result;
    }
    
    @Override
    public ASTNode visitCreateTable(final CreateTableContext ctx) {
        MySQLCreateTableStatement result = new MySQLCreateTableStatement();
        result.setTable((SimpleTableSegment) visit(ctx.tableName()));
        result.setIfNotExists(null != ctx.ifNotExists());
        if (null != ctx.createDefinitionClause()) {
            result.setRelationalTable(visitCreateDefinitionClause(ctx.createDefinitionClause()));
        }
        if (null != ctx.createLikeClause()) {
            result.setLikeTable((SimpleTableSegment) visit(ctx.createLikeClause()));
        }
        if (null != ctx.createTableOptions()) {
            result.setCreateTableOption(visitCreateTableOptions(ctx.createTableOptions()));
        }
        if (null != ctx.createPartitionOptions()) {
            result.setPartitionProperties(visitCreatePartitionOptions(ctx.createPartitionOptions()));
        }
        if (null != ctx.duplicateAsQueryExpression()) {
            result.setSelectStatement(visitDuplicateAsQueryExpression(ctx.duplicateAsQueryExpression()));
        }
        return result;
    }



    @Override
    public PartitionPropertiesSegment visitCreatePartitionOptions(CreatePartitionOptionsContext ctx) {
        return new PartitionPropertiesSegment(ctx.start.getStartIndex(), ctx.stop.getStopIndex());
    }

    @Override
    public MySQLSelectStatement visitDuplicateAsQueryExpression(DuplicateAsQueryExpressionContext ctx) {
        return (MySQLSelectStatement) visitSelect(ctx.select());
    }

    @Override
    public MySQLCreateTableOptionSegment visitCreateTableOptions(final CreateTableOptionsContext ctx) {
        MySQLCreateTableOptionSegment result = new MySQLCreateTableOptionSegment(ctx.getStart().getStartIndex(), ctx.getStop().getStopIndex());
        for (CreateTableOptionContext each : ctx.createTableOption()) {
            if (null != each.engineRef()) {
                result.setEngine((EngineSegment) visit(each.engineRef()));
            } else if (null != each.COMMENT()) {
                result.setCommentSegment(new CommentSegment(each.string_().getText(), each.string_().getStart().getStartIndex(), each.string_().getStop().getStopIndex()));
            } else if (null != each.defaultCharset()) {
                result.setCharsetName(visitDefaultCharset(each.defaultCharset()));
            } else if (null != each.AUTO_INCREMENT()) {
                result.setAutoIncrement(new NumberLiteralValue(each.NUMBER_().getText()));
            } else if (null != each.defaultCollation()) {
                result.setCollationName((CollationNameSegment) visit(each.defaultCollation()));
            }
        }
        return result;
    }

    @Override
    public ASTNode visitDefaultCollation(DefaultCollationContext ctx) {
        return new CollationNameSegment(ctx.start.getStartIndex(), ctx.stop.getStopIndex(), new IdentifierValue(ctx.collationName().textOrIdentifier().getText()));
    }

    @Override
    public CharsetNameSegment visitDefaultCharset(DefaultCharsetContext ctx) {
        return new CharsetNameSegment(ctx.getStart().getStartIndex(), ctx.getStop().getStopIndex(), new IdentifierValue(ctx.charsetName().getText()));
    }

    @Override
    public RelationalTableSegment visitCreateDefinitionClause(final CreateDefinitionClauseContext ctx) {
        RelationalTableSegment result = new RelationalTableSegment(ctx.start.getStartIndex(), ctx.stop.getStopIndex());
        for (CreateDefinitionItemContext each : ctx.createDefinitionItem()) {
            if (null != each.columnDefinitionClause()) {
                result.getColumnDefinitions().add(visitColumnDefinitionClause(each.columnDefinitionClause()));
            }
            if (null != each.tableConstraintDef()) {
                result.getConstraintDefinitions().add(visitTableConstraintDef(each.tableConstraintDef()));
            }
        }
        return result;
    }
    
    @Override
    public ASTNode visitCreateLikeClause(final CreateLikeClauseContext ctx) {
        return visit(ctx.tableName());
    }
    
    @Override
    public ASTNode visitAlterTable(final AlterTableContext ctx) {
        MySQLAlterTableStatement result = new MySQLAlterTableStatement();
        result.setTable((SimpleTableSegment) visit(ctx.tableName()));
        if (null != ctx.alterTableOptions()) {
            ctx.alterTableOptions().alterTableOption().forEach(option ->
                    setAlterDefinition(result, (AlterDefinitionSegment) visit(option)));

            for (CreateTableOptionContext each : ctx.alterTableOptions().createTableOption()) {
                if (null != each.defaultCollation()) {
                    result.setCollationName((CollationNameSegment) visit(each.defaultCollation()));
                }
                if (null != each.engineRef()) {
                    result.setEngine((EngineSegment) visit(each.engineRef()));
                }
            }
        }
        if (null != ctx.alterPartitionOptions()) {
            result.setPartitionSegment(visitAlterPartitionOptions(ctx.alterPartitionOptions()));
        }
        return result;
    }

    @Override
    public AlterPartitionSegment visitAlterPartitionOptions(AlterPartitionOptionsContext ctx) {
        return new AlterPartitionSegment(ctx.start.getStartIndex(), ctx.stop.getStopIndex());
    }

    private void setAlterDefinition(final MySQLAlterTableStatement alterTableStatement, final AlterDefinitionSegment alterDefinitionSegment) {
        if (alterDefinitionSegment instanceof AddColumnDefinitionSegment) {
            alterTableStatement.getAddColumnDefinitions().add((AddColumnDefinitionSegment) alterDefinitionSegment);
        } else if (alterDefinitionSegment instanceof MySQLModifyColumnDefinitionSegment) {
            alterTableStatement.getMysqlModifyColumnDefinitions().add((MySQLModifyColumnDefinitionSegment) alterDefinitionSegment);
        } else if (alterDefinitionSegment instanceof ChangeColumnDefinitionSegment) {
            alterTableStatement.getChangeColumnDefinitions().add((ChangeColumnDefinitionSegment) alterDefinitionSegment);
        } else if (alterDefinitionSegment instanceof DropColumnDefinitionSegment) {
            alterTableStatement.getDropColumnDefinitions().add((DropColumnDefinitionSegment) alterDefinitionSegment);
        } else if (alterDefinitionSegment instanceof AddConstraintDefinitionSegment) {
            alterTableStatement.getAddConstraintDefinitions().add((AddConstraintDefinitionSegment) alterDefinitionSegment);
        } else if (alterDefinitionSegment instanceof DropConstraintDefinitionSegment) {
            alterTableStatement.getDropConstraintDefinitions().add((DropConstraintDefinitionSegment) alterDefinitionSegment);
        } else if (alterDefinitionSegment instanceof RenameTableDefinitionSegment) {
            alterTableStatement.setRenameTable(((RenameTableDefinitionSegment) alterDefinitionSegment).getRenameTable());
        } else if (alterDefinitionSegment instanceof ConvertTableDefinitionSegment) {
            alterTableStatement.setConvertTableDefinition((ConvertTableDefinitionSegment) alterDefinitionSegment);
        } else if (alterDefinitionSegment instanceof DropIndexDefinitionSegment) {
            alterTableStatement.getDropIndexDefinitions().add((DropIndexDefinitionSegment) alterDefinitionSegment);
        } else if (alterDefinitionSegment instanceof DropForeignKeySegment) {
            alterTableStatement.getDropForeignKeyDefinitions().add((DropForeignKeySegment) alterDefinitionSegment);
        } else if (alterDefinitionSegment instanceof DropPrimaryKeySegment) {
            alterTableStatement.getDropPrimaryKeyDefinitions().add((DropPrimaryKeySegment) alterDefinitionSegment);
        } else if (alterDefinitionSegment instanceof RenameIndexDefinitionSegment) {
            alterTableStatement.getRenameIndexDefinitions().add((RenameIndexDefinitionSegment) alterDefinitionSegment);
        } else if (alterDefinitionSegment instanceof RenameColumnSegment) {
            alterTableStatement.getRenameColumnDefinitions().add((RenameColumnSegment) alterDefinitionSegment);
        } else if (alterDefinitionSegment instanceof AlgorithmTypeSegment) {
            alterTableStatement.setAlgorithmSegment((AlgorithmTypeSegment) alterDefinitionSegment);
        } else if (alterDefinitionSegment instanceof LockTableSegment) {
            alterTableStatement.setLockTableSegment((LockTableSegment) alterDefinitionSegment);
        } else if (alterDefinitionSegment instanceof AlterTableCharsetSegment) {
            alterTableStatement.setCharsetSegment((AlterTableCharsetSegment) alterDefinitionSegment);
        }
    }

    @Override
    public ASTNode visitAlterTableDefaultCharset(AlterTableDefaultCharsetContext ctx) {
        AlterTableCharsetSegment result = new AlterTableCharsetSegment(ctx.getStart().getStartIndex(), ctx.getStop().getStopIndex());
        result.setCharsetName(visitDefaultCharset(ctx.defaultCharset()));
        return result;
    }

    @Override
    public ASTNode visitAlterConstraint(final AlterConstraintContext ctx) {
        return new ModifyConstraintDefinitionSegment(ctx.getStart().getStartIndex(), ctx.getStop().getStopIndex(), (ConstraintSegment) visit(ctx.constraintName()));
    }

    @Override
    public ASTNode visitAlterTableDrop(AlterTableDropContext ctx) {
        if (null != ctx.CHECK() || null != ctx.CONSTRAINT()) {
            ConstraintSegment constraint = new ConstraintSegment(ctx.identifier().getStart().getStartIndex(), ctx.identifier().getStop().getStopIndex(),
                    (IdentifierValue) visit(ctx.identifier()));
            return new DropConstraintDefinitionSegment(ctx.getStart().getStartIndex(), ctx.getStop().getStopIndex(), constraint);
        }
        if (null == ctx.KEY() && null == ctx.keyOrIndex()) {

            ColumnSegment column = visitColumnName(ctx.columnName());
            return new DropColumnDefinitionSegment(ctx.getStart().getStartIndex(), ctx.getStop().getStopIndex(), Collections.singleton(column));
        }
        if (null != ctx.keyOrIndex()) {
            return new DropIndexDefinitionSegment(ctx.getStart().getStartIndex(), ctx.getStop().getStopIndex(), (IndexSegment) visit(ctx.indexName()));
        }
        if (null != ctx.FOREIGN()) {
            return new DropForeignKeySegment(ctx.getStart().getStartIndex(), ctx.getStop().getStopIndex(), visitIdentifier(ctx.columnName().name().identifier()));
        }
        if (null != ctx.PRIMARY()) {
            return new DropPrimaryKeySegment(ctx.getStart().getStartIndex(), ctx.getStop().getStopIndex());
        }
        return null;
    }

    @Override
    public ASTNode visitAlgorithmOption(final AlgorithmOptionContext ctx) {
        AlgorithmOption algorithmOption = null;
        if (null != ctx.INSTANT()) {
            algorithmOption = AlgorithmOption.INSTANT;
        } else if (null != ctx.DEFAULT()) {
            algorithmOption = AlgorithmOption.DEFAULT;
        } else if (null != ctx.INPLACE()) {
            algorithmOption = AlgorithmOption.INPLACE;
        } else if (null != ctx.COPY()) {
            algorithmOption = AlgorithmOption.COPY;
        }
        return new AlgorithmTypeSegment(ctx.getStart().getStartIndex(), ctx.getStop().getStopIndex(), algorithmOption);
    }
    
    @Override
    public ASTNode visitLockOption(final LockOptionContext ctx) {
        LockTableOption lockOption = null;
        if (null != ctx.DEFAULT()) {
            lockOption = LockTableOption.DEFAULT;
        } else if (null != ctx.NONE()) {
            lockOption = LockTableOption.NONE;
        } else if (null != ctx.SHARED()) {
            lockOption = LockTableOption.SHARED;
        } else if (null != ctx.EXCLUSIVE()) {
            lockOption = LockTableOption.EXCLUSIVE;
        }
        return new LockTableSegment(ctx.getStart().getStartIndex(), ctx.getStop().getStopIndex(), lockOption);
    }
    
    @Override
    public ASTNode visitAlterConvert(final AlterConvertContext ctx) {
        ConvertTableDefinitionSegment result = new ConvertTableDefinitionSegment(ctx.getStart().getStartIndex(), ctx.getStop().getStopIndex(), (CharsetNameSegment) visit(ctx.charsetName()));
        if (null != ctx.collateClause()) {
            result.setCollateValue((SimpleExpressionSegment) visit(ctx.collateClause()));
        }
        return result;
    }

    
    @Override
    public ASTNode visitAddTableConstraint(final AddTableConstraintContext ctx) {
        return new AddConstraintDefinitionSegment(ctx.getStart().getStartIndex(), ctx.getStop().getStopIndex(), (MySQLConstraintDefinitionSegment) visit(ctx.tableConstraintDef()));
    }
    
    @Override
    public ASTNode visitAlterCheck(final AlterCheckContext ctx) {
        return new ModifyConstraintDefinitionSegment(ctx.getStart().getStartIndex(), ctx.getStop().getStopIndex(), (ConstraintSegment) visit(ctx.constraintName()));
    }
    
    @Override
    public ASTNode visitAlterRenameTable(final AlterRenameTableContext ctx) {
        RenameTableDefinitionSegment result = new RenameTableDefinitionSegment(ctx.start.getStartIndex(), ctx.stop.getStopIndex());
        result.setRenameTable((SimpleTableSegment) visit(ctx.tableName()));
        return result;
    }
    
    @Override
    public ASTNode visitRenameTable(final RenameTableContext ctx) {
        MySQLRenameTableStatement result = new MySQLRenameTableStatement();
        for (RenameTableClauseContext context : ctx.renameTableClause()) {
            result.getRenameTables().add(createRenameTableDefinitionSegment(context.oldName, context.newName));
        }
        return result;
    }
    
    private RenameTableDefinitionSegment createRenameTableDefinitionSegment(final TableNameContext tableName, final TableNameContext renameTableName) {
        RenameTableDefinitionSegment result = new RenameTableDefinitionSegment(tableName.start.getStartIndex(), renameTableName.stop.getStopIndex());
        result.setTable((SimpleTableSegment) visit(tableName));
        result.setRenameTable((SimpleTableSegment) visit(renameTableName));
        return result;
    }

    @Override
    public MySQLModifyColumnDefinitionSegment visitModifyColumn(ModifyColumnContext ctx) {
        MySQLColumnDefinitionSegment columnDefinitionSegment = new MySQLColumnDefinitionSegment(ctx.getStart().getStartIndex(), ctx.getStop().getStopIndex(), getText(ctx));
        columnDefinitionSegment.setColumnName(visitColumnName(ctx.columnName()));
        setColumnTypeAttribute(columnDefinitionSegment, ctx.columnTypeAttribute());
        MySQLModifyColumnDefinitionSegment result = new MySQLModifyColumnDefinitionSegment(
                ctx.getStart().getStartIndex(), ctx.getStop().getStopIndex(), columnDefinitionSegment);
        if (null != ctx.place()) {
            result.setColumnPosition((ColumnPositionSegment) visit(ctx.place()));
        }
        return result;
    }

    private String getText(final ParserRuleContext ctx) {
        return ctx.start.getInputStream().getText(new Interval(ctx.start.getStartIndex(), ctx.stop.getStopIndex()));
    }

    @Override
    public ChangeColumnDefinitionSegment visitChangeColumn(ChangeColumnContext ctx) {
        ChangeColumnDefinitionSegment result = new ChangeColumnDefinitionSegment(ctx.getStart().getStartIndex(), ctx.getStop().getStopIndex(), visitColumnDefinitionClause(ctx.columnDefinitionClause()));
        result.setPreviousColumn(visitColumnName(ctx.columnName()));
        if (null != ctx.place()) {
            result.setColumnPosition((ColumnPositionSegment) visit(ctx.place()));
        }
        return result;
    }

    @Override
    public ASTNode visitAddColumn(final AddColumnContext ctx) {
        Collection<ColumnDefinitionSegment> columnDefinitions = new LinkedList<>();
        if (null != ctx.columnDefinitionClause()) {
            for (ColumnDefinitionClauseContext each : ctx.columnDefinitionClause()) {
                columnDefinitions.add((ColumnDefinitionSegment) visit(each));
            }
        }
        AddColumnDefinitionSegment result = new AddColumnDefinitionSegment(ctx.getStart().getStartIndex(), ctx.getStop().getStopIndex(), columnDefinitions);
        if (null != ctx.place()) {
            Preconditions.checkState(1 == columnDefinitions.size());
            result.setColumnPosition((ColumnPositionSegment) visit(ctx.place()));
        }
        return result;
    }
    
    @Override
    public ASTNode visitRenameColumn(final RenameColumnContext ctx) {
        ColumnSegment oldColumnSegment = (ColumnSegment) visit(ctx.oldColumn());
        ColumnSegment newColumnSegment = (ColumnSegment) visit(ctx.newColumn());
        return new RenameColumnSegment(ctx.getStart().getStartIndex(), ctx.getStop().getStopIndex(), oldColumnSegment, newColumnSegment);
    }
    
    @Override
    public MySQLColumnDefinitionSegment visitColumnDefinitionClause(final ColumnDefinitionClauseContext ctx) {
        MySQLColumnDefinitionSegment result = new MySQLColumnDefinitionSegment(ctx.getStart().getStartIndex(), ctx.getStop().getStopIndex(), getText(ctx));
        ColumnSegment column = new ColumnSegment(ctx.name().start.getStartIndex(), ctx.name().stop.getStopIndex(), (IdentifierValue) visit(ctx.name().identifier()));
        result.setColumnName(column);
        setColumnTypeAttribute(result, ctx.columnTypeAttribute());
        return result;
    }

    public void setColumnTypeAttribute(MySQLColumnDefinitionSegment result, ColumnTypeAttributeContext ctx) {
        result.setDataType((DataTypeSegment) visit(ctx.dataType()));
        for (ColumnAttributeContext attribute : ctx.columnAttribute()) {
            if (null == attribute) {
                continue;
            }
            if (null != attribute.NOT() && null != attribute.NULL()) {
                result.setNotNull(true);
            }
            if (null != attribute.KEY() && null == attribute.UNIQUE()) {
                result.setPrimaryKey(true);
            }
            if (null != attribute.AUTO_INCREMENT()) {
                result.setAutoIncrement(true);
            }
            if (null != attribute.referenceDefinition()) {
                result.getReferencedTables().add((SimpleTableSegment) visit(attribute.referenceDefinition()));
            }
            if (null != attribute.comment) {
                result.setComment(new CommentSegment(attribute.comment.getText(), attribute.comment.getStart().getStartIndex(), attribute.comment.getStop().getStopIndex()));
            }
            if (null != attribute.collateClause()) {
                result.setCollation((CollationNameSegment) visit(attribute.collateClause()));
            }

            if (null != attribute.DEFAULT()) {
                if (null != attribute.expr()) {
                    result.setDefaultSegment(new DefaultSegment(attribute.expr().getText()));
                } else if (null != attribute.now()) {
                    result.setDefaultSegment(new DefaultSegment(attribute.now().getText()));
                } else if (null != attribute.literals()) {
                    result.setDefaultSegment(new DefaultSegment(attribute.literals().getText()));
                }
            }
        }
    }

    @SuppressWarnings({"unchecked", "rawtypes"})
    @Override
    public MySQLConstraintDefinitionSegment visitTableConstraintDef(final TableConstraintDefContext ctx) {
        MySQLConstraintDefinitionSegment result = new MySQLConstraintDefinitionSegment(ctx.getStart().getStartIndex(), ctx.getStop().getStopIndex());
        if (null != ctx.constraintClause() && null != ctx.constraintClause().constraintName()) {
            result.setConstraintName((ConstraintSegment) visit(ctx.constraintClause().constraintName()));
        }
        if (null != ctx.KEY() && null != ctx.PRIMARY()) {
            result.setPrimaryKey(true);
            result.getPrimaryKeyColumns().addAll(((CollectionValue) visit(ctx.keyListWithExpression())).getValue());
            return result;
        }
        if (null != ctx.FOREIGN()) {
            result.setForeignKey(true);
            result.setReferencedTable((SimpleTableSegment) visit(ctx.referenceDefinition()));
            return result;
        }

        if (null != ctx.UNIQUE()) {
            result.setIndexSpecification(IndexSpecificationEnum.UNIQUE);
        } else if (null != ctx.FULLTEXT()) {
            result.setIndexSpecification(IndexSpecificationEnum.FULLTEXT);
        } else if (null != ctx.SPATIAL()){
            result.setIndexSpecification(IndexSpecificationEnum.SPATIAL);
        }

        if (null != ctx.UNIQUE()) {
            result.setUniqueKey(true);
            result.getIndexColumns().addAll(((CollectionValue) visit(ctx.keyListWithExpression())).getValue());
            if (null != ctx.indexName()) {
                result.setIndexName((IndexSegment) visit(ctx.indexName()));
            }
            return result;
        }
        if (null != ctx.checkConstraintDefinition()) {
            return result;
        }

        result.getIndexColumns().addAll(((CollectionValue) visit(ctx.keyListWithExpression())).getValue());
        if (null != ctx.indexName()) {
            result.setIndexName((IndexSegment) visit(ctx.indexName()));
        }
        return result;
    }
    
    @Override
    public ASTNode visitKeyListWithExpression(final KeyListWithExpressionContext ctx) {
        CollectionValue<ColumnSegment> result = new CollectionValue<>();
        for (KeyPartWithExpressionContext each : ctx.keyPartWithExpression()) {
            if (null != each.keyPart()) {
                result.getValue().add((ColumnSegment) visit(each.keyPart().columnName()));
            }
        }
        return result;
    }
    
    @Override
    public ASTNode visitReferenceDefinition(final ReferenceDefinitionContext ctx) {
        return visit(ctx.tableName());
    }
    
    @Override
    public ASTNode visitPlace(final PlaceContext ctx) {
        ColumnSegment columnName = null;
        if (null != ctx.columnName()) {
            columnName = (ColumnSegment) visit(ctx.columnName());
        }
        return null == ctx.columnName()
                ? new ColumnFirstPositionSegment(ctx.getStart().getStartIndex(), ctx.getStop().getStopIndex(), columnName)
                : new ColumnAfterPositionSegment(ctx.getStart().getStartIndex(), ctx.getStop().getStopIndex(), columnName);
    }
    
    @SuppressWarnings("unchecked")
    @Override
    public ASTNode visitDropTable(final DropTableContext ctx) {
        MySQLDropTableStatement result = new MySQLDropTableStatement();
        result.setIfExists(null != ctx.ifExists());
        result.getTables().addAll(((CollectionValue<SimpleTableSegment>) visit(ctx.tableList())).getValue());
        return result;
    }
    
    @Override
    public ASTNode visitTruncateTable(final TruncateTableContext ctx) {
        MySQLTruncateStatement result = new MySQLTruncateStatement();
        result.getTables().add((SimpleTableSegment) visit(ctx.tableName()));
        return result;
    }
    
    @SuppressWarnings({"unchecked", "rawtypes"})
    @Override
    public ASTNode visitCreateIndex(final CreateIndexContext ctx) {
        MySQLCreateIndexStatement result = new MySQLCreateIndexStatement();
        result.setTable((SimpleTableSegment) visit(ctx.tableName()));
        IndexNameSegment indexName = new IndexNameSegment(ctx.indexName().start.getStartIndex(), ctx.indexName().stop.getStopIndex(), new IdentifierValue(ctx.indexName().getText()));
        result.setIndex(new IndexSegment(ctx.indexName().start.getStartIndex(), ctx.indexName().stop.getStopIndex(), indexName));
        result.getColumns().addAll(((CollectionValue) visit(ctx.keyListWithExpression())).getValue());
        if (null != ctx.createIndexSpecification()) {
            fillCreateIndex(result, ctx.createIndexSpecification());

        }
        if (null != ctx.algorithmOptionAndLockOption()) {
            if (null != ctx.algorithmOptionAndLockOption().algorithmOption()) {
                result.setAlgorithmType((AlgorithmTypeSegment) visit(ctx.algorithmOptionAndLockOption().algorithmOption()));
            }
            if (null != ctx.algorithmOptionAndLockOption().lockOption()) {
                result.setLockTable((LockTableSegment) visit(ctx.algorithmOptionAndLockOption().lockOption()));
            }
        }
        return result;
    }

    public void fillCreateIndex(final MySQLCreateIndexStatement createIndexStatement, CreateIndexSpecificationContext createIndexSpecificationContext) {
        if (null != createIndexSpecificationContext.UNIQUE()) {
            createIndexStatement.setIndexSpecification(IndexSpecificationEnum.UNIQUE);
        } else if (null != createIndexSpecificationContext.FULLTEXT()) {
            createIndexStatement.setIndexSpecification(IndexSpecificationEnum.FULLTEXT);
        } else if (null != createIndexSpecificationContext.SPATIAL()){
            createIndexStatement.setIndexSpecification(IndexSpecificationEnum.SPATIAL);
        }
    }

    @Override
    public ASTNode visitDropIndex(final DropIndexContext ctx) {
        MySQLDropIndexStatement result = new MySQLDropIndexStatement();
        result.setSimpleTable((SimpleTableSegment) visit(ctx.tableName()));
        IndexNameSegment indexName = new IndexNameSegment(ctx.indexName().start.getStartIndex(), ctx.indexName().stop.getStopIndex(), new IdentifierValue(ctx.indexName().getText()));
        result.getIndexes().add(new IndexSegment(ctx.indexName().start.getStartIndex(), ctx.indexName().stop.getStopIndex(), indexName));
        if (null != ctx.algorithmOptionAndLockOption()) {
            if (null != ctx.algorithmOptionAndLockOption().algorithmOption()) {
                result.setAlgorithmType((AlgorithmTypeSegment) visit(ctx.algorithmOptionAndLockOption().algorithmOption()));
            }
            if (null != ctx.algorithmOptionAndLockOption().lockOption()) {
                result.setLockTable((LockTableSegment) visit(ctx.algorithmOptionAndLockOption().lockOption()));
            }
        }
        return result;
    }
    
    @Override
    public ASTNode visitRenameIndex(final RenameIndexContext ctx) {
        IndexSegment indexNameSegment = (IndexSegment) visitIndexName(ctx.indexName(0));
        IndexSegment renameIndexName = (IndexSegment) visitIndexName(ctx.indexName(1));
        return new RenameIndexDefinitionSegment(ctx.start.getStartIndex(), ctx.stop.getStopIndex(), indexNameSegment, renameIndexName);
    }
    
    @Override
    public ASTNode visitKeyParts(final KeyPartsContext ctx) {
        CollectionValue<ColumnSegment> result = new CollectionValue<>();
        List<KeyPartContext> keyParts = ctx.keyPart();
        for (KeyPartContext each : keyParts) {
            if (null != each.columnName()) {
                result.getValue().add((ColumnSegment) visit(each.columnName()));
            }
        }
        return result;
    }
    
    @Override
    public ASTNode visitCreateProcedure(final CreateProcedureContext ctx) {
        MySQLCreateProcedureStatement result = new MySQLCreateProcedureStatement();
        result.setProcedureName((ProcedureNameSegment) visit(ctx.procedureName()));
        result.setRoutineBody((RoutineBodySegment) visit(ctx.routineBody()));
        return result;
    }

    @Override
    public ASTNode visitProcedureName(ProcedureNameContext ctx) {
        ProcedureNameSegment result = new ProcedureNameSegment(ctx.getStart().getStartIndex(), ctx.getStop().getStopIndex(), (IdentifierValue) visit(ctx.name().identifier()));
        if (null != ctx.owner()) {
            result.setOwner((OwnerSegment) visit(ctx.owner()));
        }
        return result;
    }

    @Override
    public ASTNode visitFunctionName(final FunctionNameContext ctx) {
        FunctionNameSegment result = new FunctionNameSegment(ctx.start.getStartIndex(), ctx.stop.getStopIndex(), (IdentifierValue) visit(ctx.name().identifier()));
        if (null != ctx.owner()) {
            result.setOwner((OwnerSegment) visit(ctx.owner()));
        }
        return result;
    }
    
    @Override
    public ASTNode visitAlterProcedure(final AlterProcedureContext ctx) {
        MySQLAlterProcedureStatement result = new MySQLAlterProcedureStatement();
        result.setProcedureNameSegment((ProcedureNameSegment) visit(ctx.procedureName()));
        return result;
    }
    
    @Override
    public ASTNode visitDropProcedure(final DropProcedureContext ctx) {
        MySQLDropProcedureStatement result = new MySQLDropProcedureStatement();
        result.setProcedureNameSegment((ProcedureNameSegment) visit(ctx.procedureName()));
        return result;
    }
    
    @Override
    public ASTNode visitCreateFunction(final CreateFunctionContext ctx) {
        MySQLCreateFunctionStatement result = new MySQLCreateFunctionStatement();
        result.setFunctionName((FunctionNameSegment) visit(ctx.functionName()));
        result.setRoutineBody((RoutineBodySegment) visit(ctx.routineBody()));
        return result;
    }
    
    @SuppressWarnings("unchecked")
    @Override
    public ASTNode visitRoutineBody(final RoutineBodyContext ctx) {
        RoutineBodySegment result = new RoutineBodySegment(ctx.start.getStartIndex(), ctx.stop.getStopIndex());
        CollectionValue<ValidStatementSegment> validStatements = null == ctx.simpleStatement()
                ? (CollectionValue<ValidStatementSegment>) visit(ctx.compoundStatement())
                : (CollectionValue<ValidStatementSegment>) visit(ctx.simpleStatement());
        result.getValidStatements().addAll(validStatements.getValue());
        return result;
    }
    
    @Override
    public ASTNode visitSimpleStatement(final SimpleStatementContext ctx) {
        return visit(ctx.validStatement());
    }
    
    @Override
    public ASTNode visitCompoundStatement(final CompoundStatementContext ctx) {
        return visit(ctx.beginStatement());
    }
    
    @SuppressWarnings("unchecked")
    @Override
    public ASTNode visitBeginStatement(final BeginStatementContext ctx) {
        CollectionValue<ValidStatementSegment> result = new CollectionValue<>();
        for (ValidStatementContext each : ctx.validStatement()) {
            result.combine((CollectionValue<ValidStatementSegment>) visit(each));
        }
        return result;
    }
    
    @SuppressWarnings("unchecked")
    @Override
    public ASTNode visitValidStatement(final ValidStatementContext ctx) {
        CollectionValue<ValidStatementSegment> result = new CollectionValue<>();
        ValidStatementSegment validStatement = createValidStatementSegment(ctx);
        if (null != validStatement.getSqlStatement()) {
            result.getValue().add(validStatement);
        }
        if (null != ctx.beginStatement()) {
            result.combine((CollectionValue<ValidStatementSegment>) visit(ctx.beginStatement()));
        }
        if (null != ctx.flowControlStatement()) {
            result.combine((CollectionValue<ValidStatementSegment>) visit(ctx.flowControlStatement()));
        }
        return result;
    }
    
    private ValidStatementSegment createValidStatementSegment(final ValidStatementContext ctx) {
        ValidStatementSegment result = new ValidStatementSegment(ctx.start.getStartIndex(), ctx.stop.getStopIndex());
        SQLStatement sqlStatement = null;
        if (null != ctx.createTable()) {
            sqlStatement = (MySQLCreateTableStatement) visit(ctx.createTable());
        } else if (null != ctx.alterTable()) {
            sqlStatement = (MySQLAlterTableStatement) visit(ctx.alterTable());
        } else if (null != ctx.dropTable()) {
            sqlStatement = (MySQLDropTableStatement) visit(ctx.dropTable());
        } else if (null != ctx.truncateTable()) {
            sqlStatement = (MySQLTruncateStatement) visit(ctx.truncateTable());
        } else if (null != ctx.insert()) {
            sqlStatement = (MySQLInsertStatement) visit(ctx.insert());
        } else if (null != ctx.replace()) {
            sqlStatement = (MySQLInsertStatement) visit(ctx.replace());
        } else if (null != ctx.update()) {
            sqlStatement = (MySQLUpdateStatement) visit(ctx.update());
        } else if (null != ctx.delete()) {
            sqlStatement = (MySQLDeleteStatement) visit(ctx.delete());
        } else if (null != ctx.select()) {
            sqlStatement = (MySQLSelectStatement) visit(ctx.select());
        }
        result.setSqlStatement(sqlStatement);
        return result;
    }
    
    @SuppressWarnings("unchecked")
    @Override
    public ASTNode visitFlowControlStatement(final FlowControlStatementContext ctx) {
        CollectionValue<ValidStatementSegment> result = new CollectionValue<>();
        if (null != ctx.caseStatement()) {
            result.combine((CollectionValue<ValidStatementSegment>) visit(ctx.caseStatement()));
        }
        if (null != ctx.ifStatement()) {
            result.combine((CollectionValue<ValidStatementSegment>) visit(ctx.ifStatement()));
        }
        if (null != ctx.loopStatement()) {
            result.combine((CollectionValue<ValidStatementSegment>) visit(ctx.loopStatement()));
        }
        if (null != ctx.repeatStatement()) {
            result.combine((CollectionValue<ValidStatementSegment>) visit(ctx.repeatStatement()));
        }
        if (null != ctx.whileStatement()) {
            result.combine((CollectionValue<ValidStatementSegment>) visit(ctx.whileStatement()));
        }
        return result;
    }
    
    @SuppressWarnings("unchecked")
    @Override
    public ASTNode visitCaseStatement(final CaseStatementContext ctx) {
        CollectionValue<ValidStatementSegment> result = new CollectionValue<>();
        for (ValidStatementContext each : ctx.validStatement()) {
            result.combine((CollectionValue<ValidStatementSegment>) visit(each));
        }
        return result;
    }
    
    @SuppressWarnings("unchecked")
    @Override
    public ASTNode visitIfStatement(final IfStatementContext ctx) {
        CollectionValue<ValidStatementSegment> result = new CollectionValue<>();
        for (ValidStatementContext each : ctx.validStatement()) {
            result.combine((CollectionValue<ValidStatementSegment>) visit(each));
        }
        return result;
    }
    
    @SuppressWarnings("unchecked")
    @Override
    public ASTNode visitLoopStatement(final LoopStatementContext ctx) {
        CollectionValue<ValidStatementSegment> result = new CollectionValue<>();
        for (ValidStatementContext each : ctx.validStatement()) {
            result.combine((CollectionValue<ValidStatementSegment>) visit(each));
        }
        return result;
    }
    
    @SuppressWarnings("unchecked")
    @Override
    public ASTNode visitRepeatStatement(final RepeatStatementContext ctx) {
        CollectionValue<ValidStatementSegment> result = new CollectionValue<>();
        for (ValidStatementContext each : ctx.validStatement()) {
            result.combine((CollectionValue<ValidStatementSegment>) visit(each));
        }
        return result;
    }
    
    @SuppressWarnings("unchecked")
    @Override
    public ASTNode visitWhileStatement(final WhileStatementContext ctx) {
        CollectionValue<ValidStatementSegment> result = new CollectionValue<>();
        for (ValidStatementContext each : ctx.validStatement()) {
            result.combine((CollectionValue<ValidStatementSegment>) visit(each));
        }
        return result;
    }
    
    @Override
    public ASTNode visitAlterFunction(final AlterFunctionContext ctx) {
        MySQLAlterFunctionStatement result = new MySQLAlterFunctionStatement();
        result.setFunctionName((FunctionNameSegment) visit(ctx.functionName()));
        return result;
    }
    
    @Override
    public ASTNode visitDropFunction(final DropFunctionContext ctx) {
        MySQLDropFunctionStatement result = new MySQLDropFunctionStatement();
        result.setFunctionName((FunctionNameSegment) visit(ctx.functionName()));
        return result;
    }


    @Override
    public ASTNode visitCreateEvent(final CreateEventContext ctx) {
        MySQLCreateEventStatement result = new MySQLCreateEventStatement();
        result.setEventNameSegment((EventNameSegment) visit(ctx.eventName()));
        return result;
    }
    
    @Override
    public ASTNode visitAlterEvent(final AlterEventContext ctx) {
        MySQLAlterEventStatement result = new MySQLAlterEventStatement();
        result.setEventNameSegment((EventNameSegment) visit(ctx.eventName()));
        return result;
    }

    @Override
    public ASTNode visitEventName(EventNameContext ctx) {
        EventNameSegment result = new EventNameSegment(ctx.getStart().getStartIndex(), ctx.getStop().getStopIndex(), (IdentifierValue) visit(ctx.identifier()));
        if (null != ctx.owner()) {
            result.setOwner((OwnerSegment) visit(ctx.owner()));
        }
        return result;
    }

    @Override
    public ASTNode visitDropEvent(final DropEventContext ctx) {
        MySQLDropEventStatement result = new MySQLDropEventStatement();
        result.setEventNameSegment((EventNameSegment) visit(ctx.eventName()));
        return result;
    }
    
    @Override
    public ASTNode visitAlterInstance(final AlterInstanceContext ctx) {
        return new MySQLAlterInstanceStatement();
    }
    
    @Override
    public ASTNode visitCreateLogfileGroup(final CreateLogfileGroupContext ctx) {
        return new MySQLCreateLogfileGroupStatement();
    }
    
    @Override
    public ASTNode visitAlterLogfileGroup(final AlterLogfileGroupContext ctx) {
        return new MySQLAlterLogfileGroupStatement();
    }
    
    @Override
    public ASTNode visitDropLogfileGroup(final DropLogfileGroupContext ctx) {
        return new MySQLDropLogfileGroupStatement();
    }
    
    @Override
    public ASTNode visitCreateServer(final CreateServerContext ctx) {
        return new MySQLCreateServerStatement();
    }
    
    @Override
    public ASTNode visitAlterServer(final AlterServerContext ctx) {
        return new MySQLAlterServerStatement();
    }
    
    @Override
    public ASTNode visitDropServer(final DropServerContext ctx) {
        return new MySQLDropServerStatement();
    }
    
    @Override
    public ASTNode visitCreateTrigger(final CreateTriggerContext ctx) {
        MySQLCreateTriggerStatement result = new MySQLCreateTriggerStatement();
        result.setTriggerNameSegment((TriggerNameSegment) visit(ctx.triggerName()));
        return result;
    }
    
    @Override
    public ASTNode visitDropTrigger(final DropTriggerContext ctx) {
        MySQLDropTriggerStatement result = new MySQLDropTriggerStatement();
        result.setTriggerNameSegment((TriggerNameSegment) visit(ctx.triggerName()));
        return result;
    }

    @Override
    public ASTNode visitTriggerName(TriggerNameContext ctx) {
        TriggerNameSegment result = new TriggerNameSegment(ctx.getStart().getStartIndex(), ctx.getStop().getStopIndex(), (IdentifierValue) visit(ctx.name().identifier()));
        if (null != ctx.owner()) {
            result.setOwner((OwnerSegment) visit(ctx.owner()));
        }
        return result;
    }

    @Override
    public ASTNode visitCreateTablespace(final CreateTablespaceContext ctx) {
        MySQLCreateTablespaceStatement result = new MySQLCreateTablespaceStatement();
        result.setTablespaceSegment(createTablespaceSegment(ctx.identifier()));
        return result;
    }
    
    @Override
    public ASTNode visitAlterTablespace(final AlterTablespaceContext ctx) {
        if (null != ctx.alterTablespaceInnodb()) {
            return visit(ctx.alterTablespaceInnodb());
        } else {
            return visit(ctx.alterTablespaceNdb());
        }
    }
    
    @Override
    public ASTNode visitAlterTablespaceInnodb(final AlterTablespaceInnodbContext ctx) {
        MySQLAlterTablespaceStatement result = new MySQLAlterTablespaceStatement();
        if (null != ctx.tablespace) {
            result.setTablespaceSegment(createTablespaceSegment(ctx.tablespace));
        }
        if (null != ctx.renameTablespace) {
            result.setRenameTablespaceSegment(createTablespaceSegment(ctx.renameTablespace));
        }
        return result;
    }
    
    @Override
    public ASTNode visitAlterTablespaceNdb(final AlterTablespaceNdbContext ctx) {
        MySQLAlterTablespaceStatement result = new MySQLAlterTablespaceStatement();
        if (null != ctx.tablespace) {
            result.setTablespaceSegment(createTablespaceSegment(ctx.tablespace));
        }
        if (null != ctx.renameTableSpace) {
            result.setRenameTablespaceSegment(createTablespaceSegment(ctx.renameTableSpace));
        }
        return result;
    }
    
    private TablespaceSegment createTablespaceSegment(final IdentifierContext ctx) {
        return new TablespaceSegment(ctx.getStart().getStartIndex(), ctx.getStop().getStopIndex(), (IdentifierValue) visit(ctx));
    }
    
    @Override
    public ASTNode visitDropTablespace(final DropTablespaceContext ctx) {
        return new MySQLDropTablespaceStatement();
    }
    
    @Override
    public ASTNode visitPrepare(final PrepareContext ctx) {
        return new MySQLPrepareStatement();
    }
    
    @Override
    public ASTNode visitExecuteStmt(final ExecuteStmtContext ctx) {
        return new MySQLExecuteStatement();
    }
    
    @Override
    public ASTNode visitDeallocate(final DeallocateContext ctx) {
        return new MySQLDeallocateStatement();
    }
}
