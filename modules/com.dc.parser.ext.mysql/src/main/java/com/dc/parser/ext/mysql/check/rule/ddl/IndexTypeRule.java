package com.dc.parser.ext.mysql.check.rule.ddl;

import com.dc.parser.ext.mysql.statement.ddl.MySQLAlterTableStatement;
import com.dc.parser.model.check.rule.CheckRuleParameter;
import com.dc.parser.model.check.rule.SQLRule;
import com.dc.parser.model.segment.ddl.constraint.ConstraintDefinitionSegment;
import com.dc.parser.model.segment.ddl.constraint.alter.AddConstraintDefinitionSegment;
import com.dc.parser.model.segment.generic.OwnerSegment;
import com.dc.parser.model.statement.SQLStatement;
import com.dc.parser.model.statement.ddl.CreateIndexStatement;
import com.dc.parser.model.value.identifier.IdentifierValue;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

public abstract class IndexTypeRule implements SQLRule {

    @Override
    public void prepare(SQLStatement sqlStatement, CheckRuleParameter parameter, Function<String, List<Map<String, Object>>> function) {
        if (sqlStatement instanceof CreateIndexStatement) {
            CreateIndexStatement createIndexStatement = (CreateIndexStatement) sqlStatement;

            String schema = createIndexStatement.getTable()
                    .getOwner()
                    .map(OwnerSegment::getIdentifier)
                    .map(IdentifierValue::getValue)
                    .orElse(parameter.getDefaultSchemaName());

            String tableName = createIndexStatement.getTable()
                    .getTableName()
                    .getIdentifier()
                    .getValue();

            List<String> fields = createIndexStatement.getColumns()
                    .stream()
                    .map(columnSegment -> columnSegment.getIdentifier().getValue())
                    .collect(Collectors.toList());

            String showColumnSql = buildSqlQuery(tableName, schema, fields);

            List<Map<String, Object>> resultSet = function.apply(showColumnSql);

            processResultSet(parameter, fields, resultSet);
        } else if (sqlStatement instanceof MySQLAlterTableStatement) {
            MySQLAlterTableStatement alterTableStatement = (MySQLAlterTableStatement) sqlStatement;

            List<String> fields = alterTableStatement.getAddConstraintDefinitions()
                    .stream()
                    .map(AddConstraintDefinitionSegment::getConstraintDefinition)
                    .map(ConstraintDefinitionSegment::getIndexColumns)
                    .flatMap(Collection::stream)
                    .map(columnSegment -> columnSegment.getIdentifier().getValue())
                    .collect(Collectors.toList());

            if (!fields.isEmpty()) {
                String schema = alterTableStatement.getTable()
                        .getOwner()
                        .map(OwnerSegment::getIdentifier)
                        .map(IdentifierValue::getValue)
                        .orElse(parameter.getDefaultSchemaName());

                String tableName = alterTableStatement.getTable()
                        .getTableName()
                        .getIdentifier()
                        .getValue();

                String showColumnSql = buildSqlQuery(tableName, schema, fields);

                List<Map<String, Object>> resultSet = function.apply(showColumnSql);

                processResultSet(parameter, fields, resultSet);
            }
        }
    }

    protected abstract String buildSqlQuery(String tableName, String schema, List<String> fields);

    protected abstract void processResultSet(CheckRuleParameter parameter, List<String> fields, List<Map<String, Object>> resultSet);
}


