package com.dc.summer.ext.hbase.model.auth;

import com.dc.summer.DBException;
import com.dc.summer.model.DBPDataSource;
import com.dc.summer.model.DBPDataSourceContainer;
import com.dc.summer.model.connection.DBPConnectionConfiguration;
import com.dc.summer.model.impl.auth.AuthModelKerberos;
import com.dc.summer.model.impl.auth.AuthModelKerberosCredentials;
import com.dc.summer.model.runtime.DBRProgressMonitor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;

import java.util.List;
import java.util.Properties;
import java.util.concurrent.Callable;

@Slf4j
public class HBaseAuthModelKerberos extends AuthModelKerberos {

    public static final String ID = "hbase_kerberos";

    @Override
    public boolean isUserNameApplicable() {
        return false;
    }

    @Override
    public boolean isUserPasswordApplicable() {
        return false;
    }

    @Override
    public Object initAuthentication(DBRProgressMonitor monitor, DBPDataSource dataSource, AuthModelKerberosCredentials credentials, DBPConnectionConfiguration configuration, Properties connectProps) throws DBException {
        super.initAuthentication(monitor, dataSource, credentials, configuration, connectProps);
        connectProps.put("hbase.security.authentication", "kerberos");
        connectProps.put("@hbase-jdbc-auth-mech@", "1");
        connectProps.put("@hbase-jdbc-principal@", credentials.getPrincipal());
        connectProps.put("@hbase-jdbc-keytab@", credentials.getKeytab());
        List<String> resources = configuration.getResources();
        if (CollectionUtils.isNotEmpty(resources)) {
            connectProps.put("@hbase-jdbc-resources@", String.join(",", resources));
        }
        return credentials;
    }


    @Override
    public <T> T wrapDriverActions(DBPDataSourceContainer container, Callable<T> callable) throws Exception {
        return callable.call();
    }

}
