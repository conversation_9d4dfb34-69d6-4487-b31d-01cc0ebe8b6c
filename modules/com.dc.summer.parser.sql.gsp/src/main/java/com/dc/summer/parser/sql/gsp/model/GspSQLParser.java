package com.dc.summer.parser.sql.gsp.model;

import com.dc.summer.parser.sql.model.SQLParserResult;
import com.dc.summer.parser.sql.model.SQLParserStructure;
import com.dc.summer.parser.sql.model.SqlActionModel;
import com.dc.sqlparser.DCustomSqlStatement;

import java.util.List;
import java.util.Map;

public class GspSQLParser extends SQLParserStructure<DCustomSqlStatement> {

    public GspSQLParser(List<SQLParserResult<DCustomSqlStatement>> sqlParserResults) {
        super(sqlParserResults);
    }

    @Override
    public SqlActionModel parserCRUD() {
        return null;
    }

    @Override
    public Map<String, Object> parserDDL() {
        return null;
    }
}
