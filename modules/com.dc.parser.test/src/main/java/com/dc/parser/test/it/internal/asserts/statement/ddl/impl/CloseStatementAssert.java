package com.dc.parser.test.it.internal.asserts.statement.ddl.impl;

import com.dc.parser.model.statement.ddl.CloseStatement;
import com.dc.parser.test.it.internal.asserts.SQLCaseAssertContext;
import com.dc.parser.test.it.internal.asserts.segment.SQLSegmentAssert;
import com.dc.parser.test.it.internal.asserts.segment.identifier.IdentifierValueAssert;
import com.dc.parser.test.it.internal.cases.parser.jaxb.statement.ddl.CloseStatementTestCase;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

import static org.hamcrest.CoreMatchers.is;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;

/**
 * Close statement assert.
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class CloseStatementAssert {

    /**
     * Assert close statement is correct with expected parser result.
     *
     * @param assertContext assert context
     * @param actual        actual close statement
     * @param expected      expected close statement test case
     */
    public static void assertIs(final SQLCaseAssertContext assertContext, final CloseStatement actual, final CloseStatementTestCase expected) {
        assertCursorName(assertContext, actual, expected);
        assertCloseAll(assertContext, actual, expected);
    }

    private static void assertCursorName(final SQLCaseAssertContext assertContext, final CloseStatement actual, final CloseStatementTestCase expected) {
        if (null == expected.getCursorName()) {
            assertFalse(actual.getCursorName().isPresent(), assertContext.getText("Actual cursor name should not exist."));
        } else {
            assertTrue(actual.getCursorName().isPresent(), assertContext.getText("Actual cursor name should exist."));
            IdentifierValueAssert.assertIs(assertContext, actual.getCursorName().get().getIdentifier(), expected.getCursorName(), "Close");
            SQLSegmentAssert.assertIs(assertContext, actual.getCursorName().get(), expected.getCursorName());
        }
    }

    private static void assertCloseAll(final SQLCaseAssertContext assertContext, final CloseStatement actual, final CloseStatementTestCase expected) {
        assertThat(assertContext.getText("Cursor's close all assertion error: "), actual.isCloseAll(), is(expected.isCloseAll()));
    }
}
