package com.dc.parser.test.it.internal.cases.parser.jaxb.segment.impl.index;

import com.dc.parser.test.it.internal.cases.parser.jaxb.segment.AbstractExpectedIdentifierSQLSegment;
import com.dc.parser.test.it.internal.cases.parser.jaxb.segment.impl.table.ExpectedOwner;
import lombok.Getter;
import lombok.Setter;

import javax.xml.bind.annotation.XmlElement;

/**
 * Expected index.
 */
@Getter
@Setter
public final class ExpectedIndex extends AbstractExpectedIdentifierSQLSegment {

    @XmlElement
    private ExpectedOwner owner;
}
