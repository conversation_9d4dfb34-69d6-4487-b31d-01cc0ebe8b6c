package com.dc.parser.test.it.internal.asserts.statement.dal.impl;

import com.dc.parser.model.segment.dal.CacheTableIndexSegment;
import com.dc.parser.model.segment.dal.PartitionDefinitionSegment;
import com.dc.parser.model.segment.dal.PartitionSegment;
import com.dc.parser.model.segment.ddl.index.IndexSegment;
import com.dc.parser.model.statement.dal.CacheIndexStatement;
import com.dc.parser.test.it.internal.asserts.SQLCaseAssertContext;
import com.dc.parser.test.it.internal.asserts.segment.SQLSegmentAssert;
import com.dc.parser.test.it.internal.asserts.segment.identifier.IdentifierValueAssert;
import com.dc.parser.test.it.internal.asserts.segment.index.IndexAssert;
import com.dc.parser.test.it.internal.asserts.segment.table.TableAssert;
import com.dc.parser.test.it.internal.cases.parser.jaxb.segment.impl.index.ExpectedCacheTableIndex;
import com.dc.parser.test.it.internal.cases.parser.jaxb.segment.impl.index.ExpectedPartitionDefinition;
import com.dc.parser.test.it.internal.cases.parser.jaxb.statement.dal.CacheIndexStatementTestCase;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

import static org.hamcrest.CoreMatchers.is;
import static org.hamcrest.MatcherAssert.assertThat;

/**
 * Cache index statement assert.
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class CacheIndexStatementAssert {

    /**
     * Assert cache index statement is correct with expected parser result.
     *
     * @param assertContext assert context
     * @param actual        actual cache index statement
     * @param expected      expected cache index statement test case
     */
    public static void assertIs(final SQLCaseAssertContext assertContext, final CacheIndexStatement actual, final CacheIndexStatementTestCase expected) {
        if (!expected.getTableIndexes().isEmpty()) {
            int count = 0;
            for (CacheTableIndexSegment each : actual.getTableIndexes()) {
                TableAssert.assertIs(assertContext, each.getTable(), expected.getTableIndexes().get(count).getTable());
                assertIndexes(assertContext, each, expected.getTableIndexes().get(count));
                SQLSegmentAssert.assertIs(assertContext, each, expected.getTableIndexes().get(count));
                count++;
            }
        }
        if (null != expected.getPartitionDefinition()) {
            assertPartitions(assertContext, actual.getPartitionDefinition(), expected.getPartitionDefinition());
            SQLSegmentAssert.assertIs(assertContext, actual.getPartitionDefinition(), expected.getPartitionDefinition());
        }
        if (null != expected.getName()) {
            assertThat(assertContext.getText("Cache index statement name assert error: "), actual.getName().getValue(), is(expected.getName()));
        }
    }

    private static void assertIndexes(final SQLCaseAssertContext assertContext, final CacheTableIndexSegment actual, final ExpectedCacheTableIndex expected) {
        int count = 0;
        for (IndexSegment index : actual.getIndexes()) {
            IndexAssert.assertIs(assertContext, index, expected.getIndexNames().get(count));
            count++;
        }
    }

    private static void assertPartitions(final SQLCaseAssertContext assertContext, final PartitionDefinitionSegment actual, final ExpectedPartitionDefinition expected) {
        TableAssert.assertIs(assertContext, actual.getTable(), expected.getTable());
        int count = 0;
        for (PartitionSegment each : actual.getPartitions()) {
            IdentifierValueAssert.assertIs(assertContext, each.getName(), expected.getPartitions().get(count), "Partition");
            SQLSegmentAssert.assertIs(assertContext, each, expected.getPartitions().get(count));
            count++;
        }
    }
}
