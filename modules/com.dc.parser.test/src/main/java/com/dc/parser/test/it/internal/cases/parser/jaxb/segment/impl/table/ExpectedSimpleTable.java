package com.dc.parser.test.it.internal.cases.parser.jaxb.segment.impl.table;

import lombok.Getter;
import lombok.Setter;
import com.dc.parser.test.it.internal.cases.parser.jaxb.segment.AbstractExpectedIdentifierSQLSegment;
import com.dc.parser.test.it.internal.cases.parser.jaxb.segment.impl.bound.ExpectedTableBoundInfo;

import javax.xml.bind.annotation.XmlAttribute;
import javax.xml.bind.annotation.XmlElement;
import java.util.Collection;
import java.util.LinkedList;

/**
 * Expected simple table.
 */
@Getter
@Setter
public final class ExpectedSimpleTable extends AbstractExpectedIdentifierSQLSegment {

    @XmlAttribute
    private String alias;

    @XmlElement
    private ExpectedOwner owner;

    @XmlElement(name = "table-bound")
    private ExpectedTableBoundInfo tableBound;

    @XmlElement(name = "index-hint")
    private final Collection<ExpectedIndexHint> indexHints = new LinkedList<>();
}
