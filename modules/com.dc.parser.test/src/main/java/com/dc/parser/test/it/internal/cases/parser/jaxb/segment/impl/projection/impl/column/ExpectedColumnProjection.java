package com.dc.parser.test.it.internal.cases.parser.jaxb.segment.impl.projection.impl.column;

import lombok.Getter;
import lombok.Setter;
import com.dc.parser.test.it.internal.cases.parser.jaxb.segment.AbstractExpectedIdentifierSQLSegment;
import com.dc.parser.test.it.internal.cases.parser.jaxb.segment.impl.bound.ExpectedColumnBoundInfo;
import com.dc.parser.test.it.internal.cases.parser.jaxb.segment.impl.generic.ExpectedParentheses;
import com.dc.parser.test.it.internal.cases.parser.jaxb.segment.impl.projection.ExpectedProjection;
import com.dc.parser.test.it.internal.cases.parser.jaxb.segment.impl.table.ExpectedOwner;

import javax.xml.bind.annotation.XmlAttribute;
import javax.xml.bind.annotation.XmlElement;

/**
 * Expected column projection.
 */
@Getter
@Setter
public final class ExpectedColumnProjection extends AbstractExpectedIdentifierSQLSegment implements ExpectedProjection {

    @XmlAttribute
    private String alias;

    @XmlElement
    private ExpectedOwner owner;

    @XmlElement(name = "left-parentheses")
    private ExpectedParentheses leftParentheses;

    @XmlElement(name = "right-parentheses")
    private ExpectedParentheses rightParentheses;

    @XmlElement(name = "column-bound")
    private ExpectedColumnBoundInfo columnBound;
}
