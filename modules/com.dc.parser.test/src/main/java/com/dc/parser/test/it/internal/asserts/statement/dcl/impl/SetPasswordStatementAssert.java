package com.dc.parser.test.it.internal.asserts.statement.dcl.impl;

import com.dc.parser.model.statement.dcl.SetPasswordStatement;
import com.dc.parser.test.it.internal.asserts.SQLCaseAssertContext;
import com.dc.parser.test.it.internal.cases.parser.jaxb.statement.dcl.SetPasswordStatementTestCase;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

/**
 * Set password statement assert.
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class SetPasswordStatementAssert {

    /**
     * Assert set password statement is correct with expected parser result.
     *
     * @param assertContext assert context
     * @param actual        actual set password statement
     * @param expected      expected set password statement test case
     */
    public static void assertIs(final SQLCaseAssertContext assertContext, final SetPasswordStatement actual, final SetPasswordStatementTestCase expected) {
    }
}
