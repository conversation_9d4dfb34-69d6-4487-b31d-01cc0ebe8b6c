package com.dc.parser.test.it.internal.asserts.statement.dal.impl;

import com.dc.parser.model.statement.dal.ShowRelayLogEventsStatement;
import com.dc.parser.test.it.internal.asserts.SQLCaseAssertContext;
import com.dc.parser.test.it.internal.cases.parser.jaxb.statement.dal.ShowRelayLogEventsStatementTestCase;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

import static org.hamcrest.CoreMatchers.is;
import static org.hamcrest.MatcherAssert.assertThat;

/**
 * Show relay log events statement assert.
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class ShowRelayLogEventsStatementAssert {

    /**
     * Assert show relay log events statement is correct with expected show relay log events statement test case.
     *
     * @param assertContext assert context
     * @param actual        actual show relay log events statement
     * @param expected      expected show relay log events statement test case
     */
    public static void assertIs(final SQLCaseAssertContext assertContext, final ShowRelayLogEventsStatement actual, final ShowRelayLogEventsStatementTestCase expected) {
        assertThat(assertContext.getText("Actual show relay log channel name assertion error: "), actual.getChannel(), is(expected.getChannel()));
        if (null != expected.getLogName()) {
            assertThat(assertContext.getText("Actual show relay log name assertion error: "), actual.getLogName(), is(expected.getLogName()));
        }
    }
}
