package com.dc.parser.test.it.internal.asserts.segment.definition;

import com.dc.parser.model.segment.ddl.table.CreateTableOptionSegment;
import com.dc.parser.test.it.internal.asserts.SQLCaseAssertContext;
import com.dc.parser.test.it.internal.cases.parser.jaxb.segment.impl.definition.ExpectedCreateTableOptionDefinition;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

import static org.hamcrest.CoreMatchers.is;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;

/**
 * Create table option assert.
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class CreateTableOptionDefinitionAssert {

    /**
     * Assert actual create table option segment is correct with expected create table option.
     *
     * @param assertContext assert context
     * @param actual        actual create table option segment
     * @param expected      expected create table option
     */
    public static void assertIs(final SQLCaseAssertContext assertContext, final CreateTableOptionSegment actual, final ExpectedCreateTableOptionDefinition expected) {
        if (null == expected.getEngine()) {
            assertFalse(actual.getEngine().isPresent(), assertContext.getText("Actual engine should not exist."));
        } else {
            assertTrue(actual.getEngine().isPresent(), assertContext.getText("Actual engine should exist."));
            assertThat(assertContext.getText(String.format("`%s`'s engine assertion error: ", actual.getClass().getSimpleName())), actual.getEngine().get().getEngine(),
                    is(expected.getEngine().getName()));
        }
        if (null == expected.getComment()) {
            assertFalse(actual.getComment().isPresent(), assertContext.getText("Actual comment should not exist."));
        } else {
            assertTrue(actual.getComment().isPresent(), assertContext.getText("Actual comment should exist."));
            assertThat(assertContext.getText(String.format("`%s`'s comment assertion error: ", actual.getClass().getSimpleName())), actual.getComment().get().getText(),
                    is(expected.getComment().getText()));
        }
    }
}
