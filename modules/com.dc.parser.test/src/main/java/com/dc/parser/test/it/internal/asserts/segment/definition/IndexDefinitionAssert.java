package com.dc.parser.test.it.internal.asserts.segment.definition;

import com.dc.parser.model.segment.ddl.index.IndexSegment;
import com.dc.parser.test.it.internal.asserts.SQLCaseAssertContext;
import com.dc.parser.test.it.internal.cases.parser.jaxb.segment.impl.definition.ExpectedIndexDefinition;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

import static org.hamcrest.CoreMatchers.is;
import static org.hamcrest.MatcherAssert.assertThat;

/**
 * Index definition assert.
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class IndexDefinitionAssert {

    /**
     * Assert actual index definition segment is correct with expected index definition.
     *
     * @param assertContext assert context
     * @param actual        actual index definition segment
     * @param expected      expected index definition
     */
    public static void assertIs(final SQLCaseAssertContext assertContext, final IndexSegment actual, final ExpectedIndexDefinition expected) {
        assertThat(assertContext.getText("Index definition name assertion error: "), actual.getIndexName().getIdentifier().getValue(), is(expected.getIndex().getName()));
        assertThat(assertContext.getText("Index definition start index assertion error: "), actual.getStartIndex(), is(expected.getStartIndex()));
        assertThat(assertContext.getText("Index definition stop index assertion error: "), actual.getStopIndex(), is(expected.getStopIndex()));
    }
}
