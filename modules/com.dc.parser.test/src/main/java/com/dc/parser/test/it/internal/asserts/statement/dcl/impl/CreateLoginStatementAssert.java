package com.dc.parser.test.it.internal.asserts.statement.dcl.impl;

import com.dc.parser.model.statement.dcl.CreateLoginStatement;
import com.dc.parser.test.it.internal.asserts.SQLCaseAssertContext;
import com.dc.parser.test.it.internal.asserts.segment.SQLSegmentAssert;
import com.dc.parser.test.it.internal.cases.parser.jaxb.statement.dcl.CreateLoginStatementTestCase;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

import static org.hamcrest.CoreMatchers.is;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;

/**
 * Create login statement assert.
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class CreateLoginStatementAssert {

    /**
     * Assert create login statement is correct with expected parser result.
     *
     * @param assertContext assert context
     * @param actual        actual create login statement
     * @param expected      expected create login statement test case
     */
    public static void assertIs(final SQLCaseAssertContext assertContext, final CreateLoginStatement actual, final CreateLoginStatementTestCase expected) {
        if (null == expected.getLogin()) {
            assertNull(actual.getLoginSegment(), assertContext.getText("Actual login should not exist."));
        } else {
            assertNotNull(actual.getLoginSegment(), assertContext.getText("Actual login should exist."));
            assertThat(assertContext.getText("Login name assertion error: "), actual.getLoginSegment().getLoginName().getValueWithQuoteCharacters(), is(expected.getLogin().getName()));
            SQLSegmentAssert.assertIs(assertContext, actual.getLoginSegment(), expected.getLogin());
        }
    }
}
