package com.dc.parser.test.it.internal.asserts.segment.generic;

import com.dc.parser.model.segment.generic.DataTypeLengthSegment;
import com.dc.parser.model.segment.generic.DataTypeSegment;
import com.dc.parser.test.it.internal.asserts.SQLCaseAssertContext;
import com.dc.parser.test.it.internal.cases.parser.jaxb.segment.impl.generic.ExpectedDataType;
import com.dc.parser.test.it.internal.cases.parser.jaxb.segment.impl.generic.ExpectedDataTypeLength;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

import static org.hamcrest.CoreMatchers.is;
import static org.hamcrest.MatcherAssert.assertThat;

/**
 * Data type assert.
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class DataTypeAssert {

    /**
     * Assert actual data type segment is correct with expected date type.
     *
     * @param assertContext assert context
     * @param actual        actual data type statement
     * @param expected      expected data type
     */
    public static void assertIs(final SQLCaseAssertContext assertContext, final DataTypeSegment actual, final ExpectedDataType expected) {
        assertThat(assertContext.getText(String.format("%s name assertion error: ", "dataType")),
                actual.getDataTypeName(), is(expected.getValue()));
        assertThat(assertContext.getText(String.format("%s start index assertion error: ", "dataType")),
                actual.getStartIndex(), is(expected.getStartIndex()));
        assertThat(assertContext.getText(String.format("%s end index assertion error: ", "dataType")),
                actual.getStopIndex(), is(expected.getStopIndex()));
        if (null != expected.getDataLength()) {
            DataTypeLengthSegment actualDataLength = actual.getDataLength();
            ExpectedDataTypeLength expectedDataLength = expected.getDataLength();
            if (null != expectedDataLength.getType()) {
                assertThat(assertContext.getText(String.format("%s name assertion error: ", "dataTypeLength")),
                        actualDataLength.getType().get(), is(expectedDataLength.getType()));
            }
            if (null != expectedDataLength.getPrecision()) {
                assertThat(assertContext.getText(String.format("%s name assertion error: ", "dataTypeLength")),
                        actualDataLength.getPrecision(), is(expectedDataLength.getPrecision()));
            }
        }
    }
}
