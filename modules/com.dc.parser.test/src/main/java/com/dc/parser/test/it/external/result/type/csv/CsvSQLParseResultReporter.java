package com.dc.parser.test.it.external.result.type.csv;

import com.dc.parser.test.it.external.result.SQLParseResultReporter;
import lombok.SneakyThrows;
import org.apache.commons.csv.CSVFormat;
import org.apache.commons.csv.CSVPrinter;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.nio.file.StandardOpenOption;

/**
 * SQL parse result reporter for CSV.
 */
public final class CsvSQLParseResultReporter implements SQLParseResultReporter {

    private final CSVPrinter printer;

    @SneakyThrows(IOException.class)
    public CsvSQLParseResultReporter(final String databaseType, final String resultPath) {
        File csvFile = new File(resultPath + databaseType + "-result.csv");
        printHeader(csvFile);
        printer = new CSVPrinter(Files.newBufferedWriter(Paths.get(csvFile.toURI()), StandardOpenOption.APPEND), CSVFormat.DEFAULT.builder().setSkipHeaderRecord(true).build());
    }

    @SneakyThrows(IOException.class)
    private void printHeader(final File csvFile) {
        if (csvFile.exists()) {
            return;
        }
        try (
                CSVPrinter csvHeaderPrinter = new CSVPrinter(Files.newBufferedWriter(Paths.get(csvFile.toURI())), CSVFormat.DEFAULT.builder().setSkipHeaderRecord(false).build())) {
            csvHeaderPrinter.printRecord("SQLCaseId", "DatabaseType", "Result", "SQL");
            csvHeaderPrinter.flush();
        }
    }

    /**
     * Print result.
     *
     * @param sqlCaseId    SQL case ID
     * @param databaseType database type
     * @param isSuccess    whether success
     * @param sql          SQL
     */
    @SneakyThrows(IOException.class)
    @Override
    public void printResult(final String sqlCaseId, final String databaseType, final boolean isSuccess, final String sql) {
        printer.printRecord(sqlCaseId, databaseType, isSuccess ? "success" : "failed", sql);
        printer.flush();
    }

    @Override
    public void close() throws IOException {
        printer.close();
    }
}
