package com.dc.parser.test.it.internal.cases.parser.jaxb.segment.impl.statistics;

import lombok.Getter;
import lombok.Setter;
import com.dc.parser.test.it.internal.cases.parser.jaxb.segment.AbstractExpectedSQLSegment;

import javax.xml.bind.annotation.XmlAttribute;
import java.util.Collection;

@Getter
@Setter
public final class ExpectedSampleOption extends AbstractExpectedSQLSegment {

    @XmlAttribute
    private String strategy;

    @XmlAttribute
    private Collection<String> partitions;

    @XmlAttribute(name = "sample-number")
    private String sampleNumber;

    @XmlAttribute(name = "persist-sample-percent")
    private boolean persistSamplePercent;

    @XmlAttribute(name = "scan-unit")
    private String scanUnit;

}
