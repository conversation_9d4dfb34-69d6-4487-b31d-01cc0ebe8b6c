package com.dc.parser.test.it.internal.asserts.statement.tcl.impl;

import com.dc.parser.model.statement.tcl.SavepointStatement;
import com.dc.parser.test.it.internal.asserts.SQLCaseAssertContext;
import com.dc.parser.test.it.internal.cases.parser.jaxb.statement.tcl.SavepointStatementTestCase;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

import static org.hamcrest.CoreMatchers.is;
import static org.hamcrest.MatcherAssert.assertThat;

/**
 * Savepoint statement assert.
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class SavepointStatementAssert {

    /**
     * Assert savepoint statement is correct with expected parser result.
     *
     * @param assertContext assert context
     * @param actual        actual savepoint statement
     * @param expected      expected savepoint statement test case
     */
    public static void assertIs(final SQLCaseAssertContext assertContext, final SavepointStatement actual, final SavepointStatementTestCase expected) {
        assertThat(assertContext.getText("Savepoint name assertion error."), actual.getSavepointName(), is(expected.getSavepointName()));
    }
}
