package com.dc.parser.test.it.internal.asserts.statement.dcl.impl;

import com.dc.parser.model.statement.dcl.AlterRoleStatement;
import com.dc.parser.test.it.internal.asserts.SQLCaseAssertContext;
import com.dc.parser.test.it.internal.cases.parser.jaxb.statement.dcl.AlterRoleStatementTestCase;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

/**
 * Alter role statement assert.
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class AlterRoleStatementAssert {

    /**
     * Assert alter role statement is correct with expected parser result.
     *
     * @param assertContext assert context
     * @param actual        actual alter role statement
     * @param expected      expected alter role statement test case
     */
    public static void assertIs(final SQLCaseAssertContext assertContext, final AlterRoleStatement actual, final AlterRoleStatementTestCase expected) {
    }
}
