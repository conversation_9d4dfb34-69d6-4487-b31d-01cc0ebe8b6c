package com.dc.parser.test.it.internal.cases.parser.jaxb.segment.impl.expr.complex;


import com.dc.parser.test.it.internal.cases.parser.jaxb.segment.impl.expr.ExpectedExpressionSegment;

/**
 * Expected complex expression segment.
 */
public interface ExpectedComplexExpressionSegment extends ExpectedExpressionSegment {

    /**
     * Get text.
     *
     * @return text
     */
    String getText();
}
