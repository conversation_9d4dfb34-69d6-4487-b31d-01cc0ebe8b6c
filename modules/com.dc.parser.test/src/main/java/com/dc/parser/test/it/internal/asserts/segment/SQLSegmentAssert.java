package com.dc.parser.test.it.internal.asserts.segment;

import com.dc.parser.model.segment.SQLSegment;
import com.dc.parser.test.it.internal.asserts.SQLCaseAssertContext;
import com.dc.parser.test.it.internal.cases.parser.jaxb.segment.ExpectedSQLSegment;
import com.dc.parser.test.it.internal.cases.sql.type.SQLCaseType;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

import static org.hamcrest.CoreMatchers.is;
import static org.hamcrest.MatcherAssert.assertThat;

/**
 * SQL segment assert.
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class SQLSegmentAssert {

    /**
     * Assert generic attributes of actual SQL segment are same with expected SQL segment.
     *
     * @param assertContext assert context
     * @param actual        actual SQL segment
     * @param expected      expected SQL segment
     */
    public static void assertIs(final SQLCaseAssertContext assertContext, final SQLSegment actual, final ExpectedSQLSegment expected) {
        assertStartIndex(assertContext, actual, expected);
        assertStopIndex(assertContext, actual, expected);
    }

    private static void assertStartIndex(final SQLCaseAssertContext assertContext, final SQLSegment actual, final ExpectedSQLSegment expected) {
        int expectedStartIndex = SQLCaseType.LITERAL == assertContext.getCaseType() && null != expected.getLiteralStartIndex() ? expected.getLiteralStartIndex() : expected.getStartIndex();
        assertThat(assertContext.getText(String.format("`%s`'s start index assertion error: ", actual.getClass().getSimpleName())), actual.getStartIndex(), is(expectedStartIndex));
    }

    private static void assertStopIndex(final SQLCaseAssertContext assertContext, final SQLSegment actual, final ExpectedSQLSegment expected) {
        int expectedStopIndex = SQLCaseType.LITERAL == assertContext.getCaseType() && null != expected.getLiteralStopIndex() ? expected.getLiteralStopIndex() : expected.getStopIndex();
        assertThat(assertContext.getText(String.format("`%s`'s stop index assertion error: ", actual.getClass().getSimpleName())), actual.getStopIndex(), is(expectedStopIndex));
    }
}
