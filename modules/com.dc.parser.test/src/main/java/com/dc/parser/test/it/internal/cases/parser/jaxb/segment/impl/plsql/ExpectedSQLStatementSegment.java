package com.dc.parser.test.it.internal.cases.parser.jaxb.segment.impl.plsql;

import com.dc.parser.test.it.internal.cases.parser.jaxb.segment.AbstractExpectedSQLSegment;
import lombok.Getter;
import lombok.Setter;

import javax.xml.bind.annotation.XmlAttribute;

/**
 * Expected SQL statement segment.
 */
@Getter
@Setter
public final class ExpectedSQLStatementSegment extends AbstractExpectedSQLSegment {

    @XmlAttribute(name = "statement-class-simple-name")
    private String statementClassSimpleName;
}
