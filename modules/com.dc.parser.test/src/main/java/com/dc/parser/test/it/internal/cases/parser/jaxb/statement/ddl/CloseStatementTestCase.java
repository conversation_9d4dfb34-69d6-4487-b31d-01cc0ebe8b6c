package com.dc.parser.test.it.internal.cases.parser.jaxb.statement.ddl;

import lombok.Getter;
import lombok.Setter;
import com.dc.parser.test.it.internal.cases.parser.jaxb.SQLParserTestCase;
import com.dc.parser.test.it.internal.cases.parser.jaxb.segment.impl.cursor.ExpectedCursorName;

import javax.xml.bind.annotation.XmlAttribute;
import javax.xml.bind.annotation.XmlElement;

/**
 * Close statement test case.
 */
@Getter
@Setter
public final class CloseStatementTestCase extends SQLParserTestCase {

    @XmlElement(name = "cursor-name")
    private ExpectedCursorName cursorName;

    @XmlAttribute(name = "close-all")
    private boolean closeAll;
}
