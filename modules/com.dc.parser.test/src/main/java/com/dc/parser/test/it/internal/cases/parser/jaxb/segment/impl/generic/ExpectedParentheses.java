package com.dc.parser.test.it.internal.cases.parser.jaxb.segment.impl.generic;

import com.dc.parser.test.it.internal.cases.parser.jaxb.segment.AbstractExpectedSQLSegment;
import lombok.Getter;
import lombok.Setter;

import javax.xml.bind.annotation.XmlAttribute;

/**
 * Expected parentheses.
 */
@Getter
@Setter
public final class ExpectedParentheses extends AbstractExpectedSQLSegment {

    @XmlAttribute(name = "parentheses")
    private String parentheses;
}
