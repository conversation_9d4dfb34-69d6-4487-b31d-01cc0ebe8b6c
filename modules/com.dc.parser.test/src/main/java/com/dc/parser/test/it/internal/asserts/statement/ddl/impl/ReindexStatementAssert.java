package com.dc.parser.test.it.internal.asserts.statement.ddl.impl;

import com.dc.parser.model.statement.ddl.ReindexStatement;
import com.dc.parser.test.it.internal.asserts.SQLCaseAssertContext;
import com.dc.parser.test.it.internal.cases.parser.jaxb.statement.ddl.ReindexStatementTestCase;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

/**
 * Reindex statement assert.
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class ReindexStatementAssert {

    /**
     * Assert reindex statement is correct with expected parser result.
     *
     * @param assertContext assert context
     * @param actual        actual reindex statement
     * @param expected      expected reindex statement test case
     */
    public static void assertIs(final SQLCaseAssertContext assertContext, final ReindexStatement actual, final ReindexStatementTestCase expected) {
    }
}
