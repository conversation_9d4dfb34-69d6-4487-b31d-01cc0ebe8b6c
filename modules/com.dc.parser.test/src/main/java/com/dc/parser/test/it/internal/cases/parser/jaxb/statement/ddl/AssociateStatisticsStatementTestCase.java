package com.dc.parser.test.it.internal.cases.parser.jaxb.statement.ddl;

import lombok.Getter;
import lombok.Setter;
import com.dc.parser.test.it.internal.cases.parser.jaxb.SQLParserTestCase;
import com.dc.parser.test.it.internal.cases.parser.jaxb.segment.impl.column.ExpectedColumn;
import com.dc.parser.test.it.internal.cases.parser.jaxb.segment.impl.function.ExpectedFunction;
import com.dc.parser.test.it.internal.cases.parser.jaxb.segment.impl.index.ExpectedIndex;
import com.dc.parser.test.it.internal.cases.parser.jaxb.segment.impl.index.ExpectedIndexType;
import com.dc.parser.test.it.internal.cases.parser.jaxb.segment.impl.packages.ExpectedPackage;
import com.dc.parser.test.it.internal.cases.parser.jaxb.segment.impl.table.ExpectedSimpleTable;
import com.dc.parser.test.it.internal.cases.parser.jaxb.segment.impl.type.ExpectedType;

import javax.xml.bind.annotation.XmlElement;
import java.util.LinkedList;
import java.util.List;

/**
 * Associate Statistics statement test case.
 */
@Getter
@Setter
public final class AssociateStatisticsStatementTestCase extends SQLParserTestCase {

    @XmlElement(name = "index")
    private final List<ExpectedIndex> indexes = new LinkedList<>();

    @XmlElement(name = "table")
    private final List<ExpectedSimpleTable> tables = new LinkedList<>();

    @XmlElement(name = "column")
    private final List<ExpectedColumn> columns = new LinkedList<>();

    @XmlElement(name = "function")
    private final List<ExpectedFunction> functions = new LinkedList<>();

    @XmlElement(name = "package")
    private final List<ExpectedPackage> packages = new LinkedList<>();

    @XmlElement(name = "type")
    private final List<ExpectedType> types = new LinkedList<>();

    @XmlElement(name = "index-type")
    private final List<ExpectedIndexType> indexTypes = new LinkedList<>();
}
