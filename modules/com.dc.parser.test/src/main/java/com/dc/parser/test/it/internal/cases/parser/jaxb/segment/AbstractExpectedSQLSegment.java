package com.dc.parser.test.it.internal.cases.parser.jaxb.segment;

import lombok.Getter;
import lombok.Setter;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlAttribute;

/**
 * Abstract expected SQL segment.
 */
@XmlAccessorType(XmlAccessType.FIELD)
@Getter
@Setter
public abstract class AbstractExpectedSQLSegment implements ExpectedSQLSegment {

    @XmlAttribute(name = "start-index")
    private int startIndex;

    @XmlAttribute(name = "stop-index")
    private int stopIndex;

    @XmlAttribute(name = "literal-start-index")
    private Integer literalStartIndex;

    @XmlAttribute(name = "literal-stop-index")
    private Integer literalStopIndex;
}
