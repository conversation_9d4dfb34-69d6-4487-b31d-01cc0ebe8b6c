package com.dc.parser.test.it.internal.cases.parser.jaxb.segment.impl.lock;

import com.dc.parser.test.it.internal.cases.parser.jaxb.segment.AbstractExpectedSQLSegment;
import lombok.Getter;

import javax.xml.bind.annotation.XmlAttribute;

/**
 * Expected lock type clause.
 */
@Getter
public final class ExpectedLockTypeClause extends AbstractExpectedSQLSegment {

    @XmlAttribute
    private String type;
}
