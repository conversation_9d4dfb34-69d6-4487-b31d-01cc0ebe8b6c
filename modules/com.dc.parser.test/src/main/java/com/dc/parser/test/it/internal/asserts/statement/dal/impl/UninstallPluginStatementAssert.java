package com.dc.parser.test.it.internal.asserts.statement.dal.impl;

import com.dc.parser.model.statement.dal.UninstallPluginStatement;
import com.dc.parser.test.it.internal.asserts.SQLCaseAssertContext;
import com.dc.parser.test.it.internal.cases.parser.jaxb.statement.dal.UninstallPluginStatementTestCase;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

import static org.hamcrest.CoreMatchers.is;
import static org.hamcrest.MatcherAssert.assertThat;

/**
 * Uninstall plugin statement assert.
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class UninstallPluginStatementAssert {

    /**
     * Assert uninstall plugin statement is correct with expected uninstall plugin statement test case.
     *
     * @param assertContext assert context
     * @param actual        actual uninstall plugin statement
     * @param expected      expected uninstall plugin statement test case
     */
    public static void assertIs(final SQLCaseAssertContext assertContext, final UninstallPluginStatement actual, final UninstallPluginStatementTestCase expected) {
        assertThat(assertContext.getText("Actual plugin name does not match: "), actual.getPluginName(), is(expected.getPlugin().getName()));
    }
}
