<?xml version="1.0" encoding="UTF-8"?>
<sql-cases>
    <sql-case id="revoke_user_without_hostname" value="REVOKE SELECT, UPDATE on t_order from user_dev"
              db-types="Oracle,PostgreSQL,GaussDB,SQLServer"/>
    <sql-case id="revoke_role" value="RE<PERSON><PERSON> role_dev from user_dev" db-types="Oracle,PostgreSQL,GaussDB"/>
    <sql-case id="revoke_user_from" value="REVOKE ADVISOR, ALTER DATABASE from user_dev" db-types="Oracle"/>
    <sql-case id="revoke_user_with_hostname_on_db" value="REVO<PERSON> select, insert ON * FROM 'user_dev'@'localhost'"
              db-types="MySQL"/>
    <sql-case id="revoke_user_with_hostname_on_table"
              value="REVOKE select, insert ON t_order FROM 'user_dev'@'localhost'" db-types="MySQL"/>
    <sql-case id="revoke_user_with_hostname_on_db_and_table"
              value="R<PERSON><PERSON><PERSON> select, insert ON primary_ds.t_order FROM 'user_dev'@'localhost'" db-types="MySQL"/>
    <sql-case id="revoke_user_with_hostname" value="REVOKE ALL PRIVILEGES, GRANT OPTION FROM 'user_dev'@'localhost'"
              db-types="MySQL"/>
    <sql-case id="revoke_select" value="REVOKE SELECT ON *.* FROM user1" db-types="MySQL"/>
    <sql-case id="revoke_select_column" value="REVOKE SELECT (order_id) ON ds_0.t_order FROM user1" db-types="MySQL"/>
    <sql-case id="revoke_select_from_local_user" value="REVOKE SELECT ON  *.* FROM 'user1'@'localhost'"
              db-types="MySQL"/>
    <sql-case id="revoke_crud" value="REVOKE INSERT, SELECT, UPDATE, DELETE ON *.* FROM user1" db-types="MySQL"/>
    <sql-case id="revoke_all" value="REVOKE ALL PRIVILEGES ON *.* FROM user1" db-types="MySQL"/>
    <sql-case id="revoke_all_on_schema" value="REVOKE ALL PRIVILEGES ON ds_0.* FROM user1" db-types="MySQL"/>
    <sql-case id="revoke_all_on_table" value="REVOKE ALL PRIVILEGES ON ds_0.t_order FROM user1"
              db-types="MySQL,PostgreSQL,GaussDB"/>
    <sql-case id="revoke_role_from_user" value="REVOKE role1 FROM user1" db-types="MySQL,Oracle"/>
    <sql-case id="revoke_roles_from_users" value="REVOKE role1, role2 FROM user1, user2" db-types="MySQL"/>
    <sql-case id="revoke_system_privilege" value="REVOKE CREATE SESSION FROM user1" db-types="Oracle"/>
    <sql-case id="revoke_system_privileges"
              value="REVOKE CREATE ANY MATERIALIZED VIEW, ALTER ANY MATERIALIZED VIEW, DROP ANY MATERIALIZED VIEW FROM user1"
              db-types="Oracle"/>
    <sql-case id="revoke_all_system_privileges" value="REVOKE ALL PRIVILEGES FROM user1" db-types="Oracle"/>
    <sql-case id="revoke_system_privilege_from_users" value="REVOKE CREATE SESSION FROM user1, user2"
              db-types="Oracle"/>
    <sql-case id="revoke_object_privilege" value="REVOKE SELECT ON ds_0.t_order FROM user1" db-types="Oracle"/>
    <sql-case id="revoke_object_privileges" value="REVOKE INSERT, SELECT, UPDATE, DELETE ON ds_0.t_order FROM user1"
              db-types="Oracle"/>
    <sql-case id="revoke_all_object_privileges" value="REVOKE ALL PRIVILEGES ON ds_0.t_order FROM user1"
              db-types="Oracle"/>
    <sql-case id="revoke_object_privilege_from_users" value="REVOKE SELECT ON ds_0.t_order FROM user1, user2"
              db-types="Oracle"/>
    <sql-case id="revoke_object_privilege_column" value="REVOKE SELECT (order_id) ON ds_0.t_order FROM user1"
              db-types="Oracle"/>
    <sql-case id="revoke_program" value="REVOKE role1 FROM FUNCTION ds_0.function1" db-types="Oracle"/>
    <sql-case id="revoke_roles_from_programs"
              value="REVOKE role1, role2 FROM FUNCTION ds_0.function1, FUNCTION ds_0.function2" db-types="Oracle"/>
    <sql-case id="revoke_all_on_table_from_roles" value="REVOKE ALL PRIVILEGES ON TABLE t_order FROM role1, role2"
              db-types="PostgreSQL,GaussDB"/>
    <sql-case id="revoke_select_on_tables" value="REVOKE SELECT ON TABLE t_order, t_order_item FROM role1"
              db-types="PostgreSQL,GaussDB"/>
    <sql-case id="revoke_select_on_all_tables" value="REVOKE SELECT ON ALL TABLES IN SCHEMA schema1 FROM role1"
              db-types="PostgreSQL,GaussDB"/>
    <sql-case id="revoke_all_column_on_table" value="REVOKE ALL PRIVILEGES (order_id) ON TABLE t_order FROM role1"
              db-types="PostgreSQL,GaussDB"/>
    <sql-case id="revoke_all_column_on_table_from_roles"
              value="REVOKE ALL PRIVILEGES (order_id) ON TABLE t_order FROM role1, role2"
              db-types="PostgreSQL,GaussDB"/>
    <sql-case id="revoke_select_column_on_table" value="REVOKE SELECT (order_id) ON TABLE t_order FROM role1"
              db-types="PostgreSQL,GaussDB"/>
    <sql-case id="revoke_select_column_on_tables"
              value="REVOKE SELECT (order_id) ON TABLE t_order, t_order_item FROM role1"
              db-types="PostgreSQL,GaussDB"/>
    <sql-case id="revoke_all_on_sequence" value="REVOKE ALL PRIVILEGES ON SEQUENCE seq_order_id FROM role1"
              db-types="PostgreSQL,GaussDB"/>
    <sql-case id="revoke_all_on_sequence_from_roles"
              value="REVOKE ALL PRIVILEGES ON SEQUENCE seq_order_id FROM role1, role2" db-types="PostgreSQL,GaussDB"/>
    <sql-case id="revoke_select_on_sequence" value="REVOKE SELECT ON SEQUENCE seq_order_id FROM role1"
              db-types="PostgreSQL,GaussDB"/>
    <sql-case id="revoke_select_on_sequences"
              value="REVOKE SELECT ON SEQUENCE seq_order_id, seq_order_item_id FROM role1"
              db-types="PostgreSQL,GaussDB"/>
    <sql-case id="revoke_select_on_all_sequences" value="REVOKE SELECT ON ALL SEQUENCES IN SCHEMA schema1 FROM role1"
              db-types="PostgreSQL,GaussDB"/>
    <sql-case id="revoke_all_on_database" value="REVOKE ALL PRIVILEGES ON DATABASE database1 FROM role1"
              db-types="PostgreSQL,GaussDB"/>
    <sql-case id="revoke_all_on_database_from_roles"
              value="REVOKE ALL PRIVILEGES ON DATABASE database1 FROM role1, role2" db-types="PostgreSQL,GaussDB"/>
    <sql-case id="revoke_create_on_database" value="REVOKE CREATE ON DATABASE database1 FROM role1"
              db-types="PostgreSQL,GaussDB"/>
    <sql-case id="revoke_create_on_databases" value="REVOKE CREATE ON DATABASE database1, database2 FROM role1"
              db-types="PostgreSQL,GaussDB"/>
    <sql-case id="revoke_all_on_domain" value="REVOKE ALL PRIVILEGES ON DOMAIN domain1 FROM role1"
              db-types="PostgreSQL,GaussDB"/>
    <sql-case id="revoke_all_on_domain_from_roles" value="REVOKE ALL PRIVILEGES ON DOMAIN domain1 FROM role1, role2"
              db-types="PostgreSQL,GaussDB"/>
    <sql-case id="revoke_usage_on_domain" value="REVOKE USAGE ON DOMAIN domain1 FROM role1"
              db-types="PostgreSQL,GaussDB"/>
    <sql-case id="revoke_usage_on_domains" value="REVOKE USAGE ON DOMAIN domain1, domain2 FROM role1"
              db-types="PostgreSQL,GaussDB"/>
    <sql-case id="revoke_all_on_foreign_data_wrapper"
              value="REVOKE ALL PRIVILEGES ON FOREIGN DATA WRAPPER fdw1 FROM role1" db-types="PostgreSQL,GaussDB"/>
    <sql-case id="revoke_all_on_foreign_data_wrapper_from_roles"
              value="REVOKE ALL PRIVILEGES ON FOREIGN DATA WRAPPER fdw1 FROM role1, role2"
              db-types="PostgreSQL,GaussDB"/>
    <sql-case id="revoke_usage_on_foreign_data_wrapper" value="REVOKE USAGE ON FOREIGN DATA WRAPPER fdw1 FROM role1"
              db-types="PostgreSQL,GaussDB"/>
    <sql-case id="revoke_usage_on_foreign_data_wrappers"
              value="REVOKE USAGE ON FOREIGN DATA WRAPPER fdw1, fdw2 FROM role1" db-types="PostgreSQL,GaussDB"/>
    <sql-case id="revoke_all_on_foreign_server" value="REVOKE ALL PRIVILEGES ON FOREIGN SERVER server1 FROM role1"
              db-types="PostgreSQL,GaussDB"/>
    <sql-case id="revoke_all_on_foreign_server_from_roles"
              value="REVOKE ALL PRIVILEGES ON FOREIGN SERVER server1 FROM role1, role2"
              db-types="PostgreSQL,GaussDB"/>
    <sql-case id="revoke_usage_on_foreign_server" value="REVOKE USAGE ON FOREIGN SERVER server1 FROM role1"
              db-types="PostgreSQL,GaussDB"/>
    <sql-case id="revoke_usage_on_foreign_servers" value="REVOKE USAGE ON FOREIGN SERVER server1, server2 FROM role1"
              db-types="PostgreSQL,GaussDB"/>
    <sql-case id="revoke_all_on_function" value="REVOKE ALL PRIVILEGES ON FUNCTION routine1 FROM role1"
              db-types="PostgreSQL,GaussDB"/>
    <sql-case id="revoke_all_on_function_from_roles"
              value="REVOKE ALL PRIVILEGES ON FUNCTION routine1 FROM role1, role2" db-types="PostgreSQL,GaussDB"/>
    <sql-case id="revoke_execute_on_function" value="REVOKE EXECUTE ON FUNCTION routine1 FROM role1"
              db-types="PostgreSQL,GaussDB"/>
    <sql-case id="revoke_execute_on_functions" value="REVOKE EXECUTE ON FUNCTION routine1, routine2 FROM role1"
              db-types="PostgreSQL,GaussDB"/>
    <sql-case id="revoke_execute_on_all_functions" value="REVOKE EXECUTE ON ALL FUNCTIONS IN SCHEMA schema1 FROM role1"
              db-types="PostgreSQL,GaussDB"/>
    <sql-case id="revoke_all_on_language" value="REVOKE ALL PRIVILEGES ON LANGUAGE lang1 FROM role1"
              db-types="PostgreSQL,GaussDB"/>
    <sql-case id="revoke_all_on_language_from_roles" value="REVOKE ALL PRIVILEGES ON LANGUAGE lang1 FROM role1, role2"
              db-types="PostgreSQL,GaussDB"/>
    <sql-case id="revoke_usage_on_language" value="REVOKE USAGE ON LANGUAGE lang1 FROM role1"
              db-types="PostgreSQL,GaussDB"/>
    <sql-case id="revoke_usage_on_languages" value="REVOKE USAGE ON LANGUAGE lang1, lang2 FROM role1"
              db-types="PostgreSQL,GaussDB"/>
    <sql-case id="revoke_all_on_large_object" value="REVOKE ALL PRIVILEGES ON LARGE OBJECT 100 FROM role1"
              db-types="PostgreSQL,GaussDB"/>
    <sql-case id="revoke_all_on_large_object_from_roles"
              value="REVOKE ALL PRIVILEGES ON LARGE OBJECT 100 FROM role1, role2" db-types="PostgreSQL,GaussDB"/>
    <sql-case id="revoke_select_large_object" value="REVOKE SELECT ON LARGE OBJECT 100 FROM role1"
              db-types="PostgreSQL,GaussDB"/>
    <sql-case id="revoke_all_on_schema_from_role" value="REVOKE ALL PRIVILEGES ON SCHEMA schema1 FROM role1"
              db-types="PostgreSQL,GaussDB"/>
    <sql-case id="revoke_select_on_large_objects" value="REVOKE SELECT ON LARGE OBJECT 100, 101 FROM role1"
              db-types="PostgreSQL,GaussDB"/>
    <sql-case id="revoke_all_on_schema_from_roles" value="REVOKE ALL PRIVILEGES ON SCHEMA schema1 FROM role1, role2"
              db-types="PostgreSQL,GaussDB"/>
    <sql-case id="revoke_create_on_schema" value="REVOKE CREATE ON SCHEMA schema1 FROM role1"
              db-types="PostgreSQL,GaussDB"/>
    <sql-case id="revoke_create_on_schemas" value="REVOKE CREATE ON SCHEMA schema1, schema2 FROM role1"
              db-types="PostgreSQL,GaussDB"/>
    <sql-case id="revoke_all_on_tablespace" value="REVOKE ALL PRIVILEGES ON TABLESPACE tablespace1 FROM role1"
              db-types="PostgreSQL,GaussDB"/>
    <sql-case id="revoke_all_on_tablespace_from_roles"
              value="REVOKE ALL PRIVILEGES ON TABLESPACE tablespace1 FROM role1, role2"
              db-types="PostgreSQL,GaussDB"/>
    <sql-case id="revoke_create_on_tablespace" value="REVOKE CREATE ON TABLESPACE tablespace1 FROM role1"
              db-types="PostgreSQL,GaussDB"/>
    <sql-case id="revoke_create_on_tablespaces" value="REVOKE CREATE ON TABLESPACE tablespace1, tablespace2 FROM role1"
              db-types="PostgreSQL,GaussDB"/>
    <sql-case id="revoke_all_on_type" value="REVOKE ALL PRIVILEGES ON TYPE type1 FROM role1"
              db-types="PostgreSQL,GaussDB"/>
    <sql-case id="revoke_all_on_type_from_roles" value="REVOKE ALL PRIVILEGES ON TYPE type1 FROM role1, role2"
              db-types="PostgreSQL,GaussDB"/>
    <sql-case id="revoke_usage_on_type" value="REVOKE USAGE ON TYPE type1 FROM role1" db-types="PostgreSQL,GaussDB"/>
    <sql-case id="revoke_usage_on_types" value="REVOKE USAGE ON TYPE type1, type2 FROM role1"
              db-types="PostgreSQL,GaussDB"/>
    <sql-case id="revoke_roles" value="REVOKE role1, role2 FROM role3, role4" db-types="PostgreSQL,GaussDB"/>
    <sql-case id="revoke_select_to_users" value="REVOKE SELECT (order_id) ON t_order FROM user1, user2"
              db-types="SQLServer"/>
    <sql-case id="revoke_crud_on_table" value="REVOKE INSERT, SELECT, UPDATE, DELETE ON t_order FROM user1"
              db-types="SQLServer"/>
    <sql-case id="revoke_select_on_table_for_postgresql" value="REVOKE SELECT ON TABLE t_order FROM role1"
              db-types="PostgreSQL,GaussDB"/>
    <sql-case id="revoke_select_on_table_for_sqlserver" value="REVOKE SELECT ON t_order FROM user1"
              db-types="SQLServer"/>
    <sql-case id="revoke_view_definition_on_availability_group_to_login"
              value="REVOKE VIEW DEFINITION ON AVAILABILITY GROUP::group1 TO login1" db-types="SQLServer"/>
    <sql-case id="revoke_take_ownership_on_availability_group_to_user"
              value="REVOKE TAKE OWNERSHIP ON AVAILABILITY GROUP::group1 TO user1 CASCADE" db-types="SQLServer"/>
    <sql-case id="revoke_grant_option_on_availability_group_to_user"
              value="REVOKE GRANT OPTION FOR CONTROL ON AVAILABILITY GROUP::group1 TO user1 CASCADE"
              db-types="SQLServer"/>
    <sql-case id="revoke_create_certificate_from_user" value="REVOKE CREATE CERTIFICATE FROM user1"
              db-types="SQLServer"/>
    <sql-case id="revoke_references_from_role" value="REVOKE REFERENCES FROM role1" db-types="SQLServer"/>
    <sql-case id="revoke_view_definition_from_user" value="REVOKE VIEW DEFINITION FROM user1 CASCADE"
              db-types="SQLServer"/>
    <sql-case id="revoke_control_on_user" value="REVOKE CONTROL ON USER::user1 FROM user2" db-types="SQLServer"/>
    <sql-case id="revoke_view_definition_on_role_from_user"
              value="REVOKE VIEW DEFINITION ON ROLE::role1 FROM user1 CASCADE" db-types="SQLServer"/>
    <sql-case id="revoke_impersonate_on_user" value="REVOKE IMPERSONATE ON USER::user1 FROM role1"
              db-types="SQLServer"/>
    <sql-case id="revoke_view_definition_on_endpoint_from_login"
              value="REVOKE VIEW DEFINITION ON ENDPOINT::endpoint1 FROM login1" db-types="SQLServer"/>
    <sql-case id="revoke_take_ownership_on_endpoint_from_user"
              value="REVOKE TAKE OWNERSHIP ON ENDPOINT::endpoint1 FROM user1 CASCADE" db-types="SQLServer"/>
    <sql-case id="revoke_select_on_object_from_user" value="REVOKE SELECT ON OBJECT::db1.t_order FROM user1"
              db-types="SQLServer"/>
    <sql-case id="revoke_execute_on_object_from_role" value="REVOKE EXECUTE ON OBJECT::db1.t_order FROM role1"
              db-types="SQLServer"/>
    <sql-case id="revoke_references_on_object_from_user"
              value="REVOKE REFERENCES (order_id) ON OBJECT::db1.t_order FROM user1 CASCADE" db-types="SQLServer"/>
    <sql-case id="revoke_view_server_state_from_login" value="REVOKE VIEW SERVER STATE FROM login1"
              db-types="SQLServer"/>
    <sql-case id="revoke_grant_option_for_connect_sql_from_login"
              value="REVOKE GRANT OPTION FOR CONNECT SQL FROM login1" db-types="SQLServer"/>
    <sql-case id="revoke_impersonate_on_login_from_windows_user"
              value="REVOKE IMPERSONATE ON LOGIN::login1 FROM [windows\user]" db-types="SQLServer"/>
    <sql-case id="revoke_view_definition_on_login" value="REVOKE VIEW DEFINITION ON LOGIN::login1 FROM login2 CASCADE"
              db-types="SQLServer"/>
    <sql-case id="revoke_view_definition_on_server_role" value="REVOKE VIEW DEFINITION ON SERVER ROLE::role1 TO role2"
              db-types="SQLServer"/>
    <sql-case id="revoke_alter_on_symmetric_key_to_user" value="REVOKE ALTER ON SYMMETRIC KEY::key1 TO user1 CASCADE"
              db-types="SQLServer"/>
    <sql-case id="revoke_execute_on_system_object" value="REVOKE EXECUTE ON sys.sp_addlinkedserver FROM public"
              db-types="SQLServer"/>
    <sql-case id="revoke_view_definition_on_type"
              value="REVOKE VIEW DEFINITION ON TYPE::schema1.type1 FROM user1 CASCADE" db-types="SQLServer"/>
    <sql-case id="revoke_execute_on_xml_schema_collection"
              value="REVOKE EXECUTE ON XML SCHEMA COLLECTION::schema1.xmlschemacollection1 FROM user1"
              db-types="SQLServer"/>
    <sql-case id="revoke_usage_on_column_encryption_key" value="REVOKE USAGE ON COLUMN_ENCRYPTION_KEY MyCEK1 FROM user1"
              db-types="GaussDB"/>
    <sql-case id="revoke_usage_on_client_master_key" value="REVOKE USAGE ON CLIENT_MASTER_KEY MyCMK1 FROM user1"
              db-types="GaussDB"/>
</sql-cases>
