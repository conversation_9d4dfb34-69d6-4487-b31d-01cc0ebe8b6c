<?xml version="1.0" encoding="UTF-8"?>
<sql-cases>
    <sql-case id="drop_foreign_data_wrapper" value="DROP FOREIGN DATA WRAPPER foo" db-types="PostgreSQL"/>
    <sql-case id="drop_foreign_data_wrapper_if_exists" value="DROP FOREIGN DATA WRAPPER IF EXISTS foo"
              db-types="PostgreSQL"/>
    <sql-case id="drop_foreign_data_wrapper_cascade" value="DROP FOREIGN DATA WRAPPER foo CASCADE"
              db-types="PostgreSQL"/>
</sql-cases>
