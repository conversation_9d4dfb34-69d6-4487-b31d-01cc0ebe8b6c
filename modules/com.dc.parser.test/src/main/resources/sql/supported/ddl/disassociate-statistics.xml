<?xml version="1.0" encoding="UTF-8"?>
<sql-cases>
    <sql-case id="disassociate_statistics_from_column" value="DISASSOCIATE STATISTICS FROM COLUMNS employee.age"
              db-types="Oracle"/>
    <sql-case id="disassociate_statistics_from_columns"
              value="DISASSOCIATE STATISTICS FROM COLUMNS employee.age, employee.salary FORCE" db-types="Oracle"/>
    <sql-case id="disassociate_statistics_from_index" value="DISASSOCIATE STATISTICS FROM INDEXES salary_index"
              db-types="Oracle"/>
    <sql-case id="disassociate_statistics_from_function" value="DISASSOCIATE STATISTICS FROM FUNCTIONS myFunction FORCE"
              db-types="Oracle"/>
    <sql-case id="disassociate_statistics_from_package" value="DISASSOCIATE STATISTICS FROM PACKAGES emp_mgmt"
              db-types="Oracle"/>
    <sql-case id="disassociate_statistics_from_type" value="DISASSOCIATE STATISTICS FROM TYPES example_typ"
              db-types="Oracle"/>
    <sql-case id="disassociate_statistics_from_index_type" value="DISASSOCIATE STATISTICS FROM INDEXTYPES indtype"
              db-types="Oracle"/>
</sql-cases>
