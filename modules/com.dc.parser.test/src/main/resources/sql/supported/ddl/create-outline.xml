<?xml version="1.0" encoding="UTF-8"?>
<sql-cases>
    <sql-case id="create_outline_for_category"
              value="CREATE OUTLINE salaries FOR CATEGORY special ON SELECT last_name, salary FROM employees;"
              db-types="Oracle"/>
    <sql-case id="create_or_replace_private_outline"
              value="CREATE OR REPLACE PRIVATE OUTLINE my_salaries FROM salaries;" db-types="Oracle"/>
    <sql-case id="create_or_replace_outline" value="CREATE OR REPLACE OUTLINE public_salaries FROM PRIVATE my_salaries;"
              db-types="Oracle"/>
</sql-cases>
