<?xml version="1.0" encoding="UTF-8"?>
<sql-cases>
    <!--    TODO Fix me. -->
    <!--    <sql-case id="create_procedure" value="CREATE PROCEDURE insert_data(a integer, b integer)-->
    <!--        LANGUAGE SQL-->
    <!--        AS $$-->
    <!--        INSERT INTO tbl VALUES (a);-->
    <!--        INSERT INTO tbl VALUES (b);-->
    <!--        $$" db-types="PostgreSQL,GaussDB" />-->
    <sql-case id="create_procedure_with_parameters" value="CREATE PROCEDURE HumanResources.uspGetEmployees
            @LastName NVARCHAR(50),
            @FirstName NVARCHAR(50)
        AS
            SELECT FirstName, LastName, JobTitle, Department
            FROM HumanResources.vEmployeeDepartment
            WHERE FirstName = @FirstName AND LastName = @LastName;" db-types="SQLServer"/>
    <sql-case id="create_procedure_declare_without_at"
              value="CREATE PROCEDURE proc (ofs INT, count INT) BEGIN DECLARE i INT DEFAULT ofs; WHILE i &lt; count DO SELECT i AS i; IF LOWER(CHAR(i USING utf8) COLLATE utf8_tolower_ci) != LOWER(CHAR(i USING utf8mb4) COLLATE utf8mb4_0900_as_ci) THEN SELECT i AS &apos;found funny character&apos;; END IF; SET i = i + 1; END WHILE; END"
              db-types="MySQL"/>
    <sql-case id="create_procedure_with_declare_and_view"
              value="CREATE PROCEDURE bug20953() BEGIN DECLARE i INT; CREATE VIEW v AS SELECT i; END" db-types="MySQL"/>
    <sql-case id="create_procedure_with_create_view_as_select"
              value="CREATE PROCEDURE p1() CREATE VIEW v1 AS SELECT * FROM t1" db-types="MySQL"/>
    <sql-case id="create_procedure_with_create_view_as_double_select"
              value="CREATE PROCEDURE bug20953() CREATE VIEW v AS SELECT 1 FROM (SELECT 1) AS d1" db-types="MySQL"/>
    <sql-case id="create_procedure_with_create_view_as_select_lowercase"
              value="create procedure p1() create view v1 as select * from t1" db-types="MySQL"/>
    <sql-case id="create_procedure_with_create_view_as_select_i"
              value="CREATE PROCEDURE bug20953(i INT) CREATE VIEW v AS SELECT i" db-types="MySQL"/>
    <sql-case id="create_procedure_with_create_view_as_select_into"
              value="CREATE PROCEDURE bug20953() CREATE VIEW v AS SELECT 1 INTO @a" db-types="MySQL"/>
    <sql-case id="create_procedure_with_create_view_as_select_into_dumpfile"
              value="CREATE PROCEDURE bug20953() CREATE VIEW v AS SELECT 1 INTO DUMPFILE &quot;file&quot;"
              db-types="MySQL"/>
    <sql-case id="create_procedure_with_create_view_as_select_into_outfile"
              value="CREATE PROCEDURE bug20953() CREATE VIEW v AS SELECT 1 INTO OUTFILE &quot;file&quot;"
              db-types="MySQL"/>
    <sql-case id="create_procedure_with_sqlexception_and_create_view"
              value="create procedure p() begin declare continue handler for sqlexception begin end; create view a as select 1; end"
              db-types="MySQL"/>
    <sql-case id="create_procedure_with_deterministic_create_view"
              value="create procedure p1 () deterministic begin create view v1 as select 1; end;" db-types="MySQL"/>
    <sql-case id="create_procedure_with_update_statement_oracle"
              value="CREATE PROCEDURE update_order (order_id NUMBER,status NUMBER) AS var NUMBER(1, 0) := 0; BEGIN  UPDATE t_order SET status = status WHERE order_id = order_id;END;"
              db-types="Oracle"/>
    <sql-case id="create_procedure_with_prepare_commit"
              value="CREATE DEFINER=`sys_data`@`%` PROCEDURE `TEST1` (a VARCHAR(64),b VARCHAR(64),c VARCHAR(64),d VARCHAR(8),e INT) BEGIN DECLARE f VARCHAR(8); SET f=DATE_FORMAT(DATE_ADD((STR_TO_DATE(d,'%Y%m%d')),INTERVAL -(e) MONTH),'%Y%m%d'); SET @aaa=CONCAT('delete from ',a,'.',b,' where ',c,'&lt;=',f,' or ', c,'&lt;&gt; LAST_DAY(',c,')', 'or ',c,'=', d); PREPARE stmt FROM @aaa; EXECUTE stmt; COMMIT; DEALLOCATE PREPARE stmt; END"
              db-types="MySQL"/>
    <sql-case id="create_procedure_with_cursor_definition"
              value="CREATE OR REPLACE EDITIONABLE PROCEDURE my_proc AS cursor cursor1 is select distinct a from tbl; var1 varchar2(500); var2 varchar2(500); BEGIN INSERT INTO t VALUES (var1, var2); END;"
              db-types="Oracle"/>
    <sql-case id="create_procedure_with_collection_type_definition"
              value="CREATE OR REPLACE EDITIONABLE PROCEDURE my_proc AS TYPE my_type is table of my_table.id%TYPE; BEGIN INSERT INTO t VALUES (var1, var2); END;"
              db-types="Oracle"/>
    <sql-case id="create_plsql_block"
              value="DECLARE warehouse NUMBER := 1;   ground    NUMBER := 1;   insured   NUMBER := 1;   result    NUMBER; BEGIN   SELECT BIN_TO_NUM(warehouse, ground, insured) INTO result FROM DUAL;   UPDATE orders SET order_status = result WHERE order_id = 2441; END;"
              db-types="Oracle"/>
    <sql-case id="create_procedure_with_insert_into_values"
              value="create procedure T522_PROC (i int =10) as begin insert into T522 (ROW_INT) values (:i); end"
              db-types="Firebird"/>
    <sql-case id="create_procedure_with_declare_and_cursor_for_in_select_and_open"
              value="CREATE PROCEDURE F865_PROC (offset_value INT, page_size INT) as DECLARE catalog_page CURSOR FOR (SELECT * FROM company ORDER BY namecompany OFFSET :offset_value ROWS FETCH NEXT :page_size ROWS ONLY); BEGIN OPEN catalog_page; END"
              db-types="Firebird"/>
</sql-cases>
