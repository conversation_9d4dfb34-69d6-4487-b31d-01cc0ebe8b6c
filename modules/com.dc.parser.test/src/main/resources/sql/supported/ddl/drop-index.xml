<?xml version="1.0" encoding="UTF-8"?>
<sql-cases>
    <sql-case id="drop_index_with_algorithm" value="DROP INDEX idx_name ON t_order ALGORITHM=INPLACE" db-types="MySQL"/>
    <sql-case id="drop_index_with_lock" value="DROP INDEX idx_name ON t_order LOCK=EXCLUSIVE" db-types="MySQL"/>
    <sql-case id="drop_index_with_algorithm_lock"
              value="DROP INDEX idx_name ON t_order ALGORITHM=INPLACE LOCK=EXCLUSIVE" db-types="MySQL"/>
    <sql-case id="drop_index_with_lock_algorithm"
              value="DROP INDEX idx_name ON t_order LOCK=EXCLUSIVE ALGORITHM=INPLACE" db-types="MySQL"/>
    <sql-case id="drop_index" value="DROP INDEX t_log_index ON t_log" db-types="MySQL,SQLServer"/>
    <sql-case id="drop_index_without_on" value="DROP INDEX order_index" db-types="PostgreSQL,GaussDB,Oracle"/>
    <sql-case id="drop_index_if_exists" value="DROP INDEX IF EXISTS order_index" db-types="PostgreSQL,GaussDB"/>
    <sql-case id="drop_index_with_space" value="    DROP INDEX
        order_index
    ON
        t_order    " db-types="MySQL,SQLServer"/>
    <sql-case id="drop_index_only_with_name" value="DROP INDEX order_index" db-types="Oracle,PostgreSQL,GaussDB"/>
    <sql-case id="drop_index_with_back_quota" value="DROP INDEX `order_index` ON `t_order`" db-types="MySQL"/>
    <sql-case id="drop_index_with_quota" value="DROP INDEX &quot;order_index&quot; ON &quot;t_order&quot;"
              db-types="Oracle"/>
    <sql-case id="drop_index_with_double_quota" value="DROP INDEX &quot;order_index&quot;"
              db-types="PostgreSQL,GaussDB"/>
    <sql-case id="drop_index_concurrently" value="DROP INDEX CONCURRENTLY order_index" db-types="PostgreSQL,GaussDB"/>
    <sql-case id="drop_index_with_schema" value="DROP INDEX public.order_index" db-types="PostgreSQL,GaussDB"/>
    <sql-case id="drop_index_with_bracket" value="DROP INDEX [order_index] ON [t_order]" db-types="SQLServer"/>
    <sql-case id="drop_index_if_exists_on_table" value="DROP INDEX IF EXISTS order_index ON t_order"
              db-types="SQLServer"/>
    <sql-case id="drop_index_with_online_force_invalidation"
              value="DROP INDEX order_index ONLINE FORCE DEFERRED INVALIDATION" db-types="Oracle"/>
</sql-cases>
