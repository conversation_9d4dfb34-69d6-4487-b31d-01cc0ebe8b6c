<?xml version="1.0" encoding="UTF-8"?>
<sql-parser-test-cases>
    <purge sql-case-id="purge_table">
        <table name="orders" start-index="12" stop-index="17"/>
    </purge>

    <purge sql-case-id="purge_index">
        <index name="order_index" start-index="12" stop-index="22"/>
    </purge>

    <purge sql-case-id="purge_tablespace"/>
    <purge sql-case-id="purge_tablespace_set"/>
    <purge sql-case-id="purge_recyclebin"/>
    <purge sql-case-id="purge_dba_recyclebin"/>
</sql-parser-test-cases>
