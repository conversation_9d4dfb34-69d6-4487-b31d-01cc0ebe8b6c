package com.dc.summer.ext.mycat;

import com.dc.summer.DBException;
import com.dc.summer.ext.mycat.model.MyCatDataSource;
import com.dc.summer.ext.mysql.MySQLDataSourceProvider;
import com.dc.summer.model.DBPDataSource;
import com.dc.summer.model.DBPDataSourceContainer;
import com.dc.summer.model.runtime.DBRProgressMonitor;

public class MyCatDataSourceProvider extends MySQLDataSourceProvider {

    @Override
    public DBPDataSource openDataSource(DBRProgressMonitor monitor, DBPDataSourceContainer container) throws DBException {
        return new MyCatDataSource(monitor, container);
    }

}
