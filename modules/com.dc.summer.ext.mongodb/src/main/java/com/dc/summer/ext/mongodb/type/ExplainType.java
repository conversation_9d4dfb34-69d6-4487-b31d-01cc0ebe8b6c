package com.dc.summer.ext.mongodb.type;

import com.mongodb.ExplainVerbosity;

public enum ExplainType {
    QUERY_PLANNER("queryPlanner", ExplainVerbosity.QUERY_PLANNER),
    EXECUTION_STATS("executionStats", ExplainVerbosity.EXECUTION_STATS),
    ALL_PLANS_EXECUTIONS("allPlansExecution", ExplainVerbosity.ALL_PLANS_EXECUTIONS);

    private String key;

    private ExplainVerbosity value;

    ExplainType(String key, ExplainVerbosity value) {
        this.key = key;
        this.value = value;
    }

    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }

    public ExplainVerbosity getValue() {
        return value;
    }

    public void setValue(ExplainVerbosity value) {
        this.value = value;
    }

    public static ExplainVerbosity getExplainVerbosityByType(String type) {
        for (ExplainType explainType : ExplainType.values()) {
            if (type.equals(explainType.getKey())) {
                return explainType.getValue();
            }
        }
        return ExplainVerbosity.QUERY_PLANNER;
    }
}
