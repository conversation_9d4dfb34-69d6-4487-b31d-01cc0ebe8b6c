package com.dc.summer.ext.mongodb.exec.js;

import com.dc.summer.DBException;
import com.dc.summer.ext.mongodb.exec.sql.MGCustomStatement;
import com.dc.summer.ext.mongodb.model.MGDatabase;
import com.mongodb.client.*;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.StreamSupport;

public class MongoJSDataSource {

    private final MongoJSProcessor processor;
    private final MGDatabase database;

    private final static MongoJSHelpInfo MONGO_JS_HELP_INFO = new MongoJSHelpInfo(MongoJSDataSource.class, MGCustomStatement.class);

    public MongoJSDataSource(MongoJSProcessor processor, MGDatabase database) {
        this.processor = processor;
        this.database = database;
    }

    @MongoJSHelp(name = "show", value = "'show databases'/'show dbs': Print a list of all available databases.")
    public List<String> showDbs() {
        return showDatabases();
    }

    @MongoJSHelp(name = "show", value = "'show databases'/'show dbs': Print a list of all available databases.")
    public List<String> showDatabases() {
        Iterable<String> databaseNames = processor.getSession().getExecutionContext().getClient().listDatabaseNames();
        return StreamSupport.stream(databaseNames.spliterator(), false).collect(Collectors.toList());
    }

    @MongoJSHelp(name = "show", value = "'show collections'/'show tables': Print a list of all collections for current database.")
    public List<String> showTables() {
        return showCollections();
    }

    @MongoJSHelp(name = "show", value = "'show collections'/'show tables': Print a list of all collections for current database.")
    public List<String> showCollections() {
        MongoClient client = processor.getSession().getExecutionContext().getClient();
        return StreamSupport
                .stream(client.getDatabase(database.getName()).listCollectionNames().spliterator(), false)
                .collect(Collectors.toList());
    }

    @MongoJSHelp(name = "show", value = "'show users': Print a list of all users for current database.")
    public Map<String, Object> showUsers() throws DBException {
        MongoJSDatabase jsDatabase = new MongoJSDatabase(processor, database);
        return jsDatabase.runCommand("usersInfo");
    }

    @MongoJSHelp(name = "db", value = "Show current database.")
    public String db() {
        return database.getName();
    }

    @MongoJSCommand("db.system.users.find()")
    public Object systemUsers() {
        MongoClient client = processor.getSession().getExecutionContext().getClient();
        return client.getDatabase(database.getName()).getCollection("system.users").find();
    }

    public List<Map<String, Object>> help() {
        return MONGO_JS_HELP_INFO.getInfos();
    }

}
