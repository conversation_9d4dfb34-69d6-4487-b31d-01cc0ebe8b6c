package com.dc.summer.ext.mongodb.data.format;

import com.dc.summer.model.document.data.format.DBFunctionObject;
import com.dc.utils.DateUtil;
import com.dc.utils.StringUtils;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.TimeZone;

@Slf4j
public class ISODate implements DBFunctionObject {

    public static final SimpleDateFormat SIMPLE_DATE_FORMAT;

    static {
        SIMPLE_DATE_FORMAT = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'");
        SIMPLE_DATE_FORMAT.setTimeZone(TimeZone.getTimeZone("GMT"));
    }

    private final Date date;


    public ISODate(Date date) {
        this.date = date;
    }

    @SneakyThrows
    public ISODate(String str) {
        String date = StringUtils.substring(str, "ISODate(\"", -2);
        this.date = SIMPLE_DATE_FORMAT.parse(date);
    }
    @Override
    public String toString() {
        return String.format("ISODate(\"%s\")", SIMPLE_DATE_FORMAT.format(date));
    }
}
