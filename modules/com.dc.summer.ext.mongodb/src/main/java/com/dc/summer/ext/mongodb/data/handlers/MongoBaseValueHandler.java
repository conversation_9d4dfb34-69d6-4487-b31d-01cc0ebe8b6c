package com.dc.summer.ext.mongodb.data.handlers;

import com.dc.summer.ext.mongodb.exec.MGSession;
import java.util.ArrayList;
import java.util.List;
import com.dc.code.NotNull;
import com.dc.summer.Log;
import com.dc.summer.ext.mongodb.exec.MGStatement;
import com.dc.summer.model.DBPDataKind;
import com.dc.summer.model.exec.DBCException;
import com.dc.summer.model.exec.DBCLogicalOperator;
import com.dc.summer.model.exec.DBCResultSet;
import com.dc.summer.model.exec.DBCSession;
import com.dc.summer.model.exec.DBCStatement;
import com.dc.summer.model.impl.data.BaseValueHandler;
import com.dc.summer.model.struct.DBSAttributeBase;
import com.dc.summer.model.struct.DBSTypedObject;

public abstract class MongoBaseValueHandler extends BaseValueHandler {
   private static final Log log = Log.getLog(MongoBaseValueHandler.class);

   public Object fetchValueObject(@NotNull DBCSession session, @NotNull DBCResultSet resultSet, @NotNull DBSTypedObject type, int index) throws DBCException {
      return type instanceof DBSAttributeBase && type.getDataKind() != DBPDataKind.DOCUMENT ? resultSet.getAttributeValue(((DBSAttributeBase)type).getName()) : resultSet.getAttributeValue(index);
   }

   public final void bindValueObject(@NotNull DBCSession session, @NotNull DBCStatement statement, @NotNull DBSTypedObject columnMetaData, int index, Object value) throws DBCException {
      try {
         this.bindParameter((MGSession)session, (MGStatement) statement, columnMetaData, index, value);
      } catch (Exception var7) {
         throw new DBCException("Error binding statement parameter", var7);
      }
   }

   protected abstract void bindParameter(MGSession session, MGStatement statement, DBSTypedObject paramType, int paramIndex, Object value) throws DBCException;

   public @NotNull DBCLogicalOperator[] getSupportedOperators(@NotNull DBSTypedObject attribute) {
      List<DBCLogicalOperator> operators = new ArrayList<>();
      DBPDataKind dataKind = attribute.getDataKind();
      if (dataKind == DBPDataKind.BOOLEAN) {
         operators.add(DBCLogicalOperator.EQUALS);
         operators.add(DBCLogicalOperator.NOT_EQUALS);
      } else if (dataKind == DBPDataKind.NUMERIC || dataKind == DBPDataKind.DATETIME || dataKind == DBPDataKind.STRING || dataKind == DBPDataKind.ROWID) {
         operators.add(DBCLogicalOperator.EQUALS);
         operators.add(DBCLogicalOperator.NOT_EQUALS);
         operators.add(DBCLogicalOperator.GREATER);
         operators.add(DBCLogicalOperator.LESS);
         operators.add(DBCLogicalOperator.IN);
         if (dataKind == DBPDataKind.STRING) {
            operators.add(DBCLogicalOperator.LIKE);
         }
      }

      return operators.toArray(new DBCLogicalOperator[0]);
   }
}
