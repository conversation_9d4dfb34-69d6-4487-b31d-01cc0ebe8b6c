package com.dc.summer.ext.mongodb.exec;

import com.dc.utils.DateUtil;
import com.mongodb.event.*;
import lombok.AccessLevel;
import lombok.Getter;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.concurrent.ConcurrentHashMap;

@Getter(value = AccessLevel.PRIVATE)
public class MGClientPoolListener implements ConnectionPoolListener {
    @Getter
    private final String thread = Thread.currentThread().toString();

    private final LocalDateTime createdTime = LocalDateTime.now();
    private long createCount;
    private long destroyCount;
    private long activeCount;
    private long poolingCount;
    private long closeCount;
    private long connectCount;
    private final ConcurrentHashMap<Integer, ConnectionInfo> connections = new ConcurrentHashMap<>();

    @Override
    public void connectionCreated(ConnectionCreatedEvent event) {
        createCount++;
        connectCount++;
        int connectionId = event.getConnectionId().getLocalValue();
        connections.put(connectionId, new ConnectionInfo(connectionId, LocalDateTime.now()));
    }

    @Override
    public void connectionClosed(ConnectionClosedEvent event) {
        destroyCount++;
        closeCount++;
        connections.remove(event.getConnectionId().getLocalValue());
    }

    @Override
    public void connectionCheckedOut(ConnectionCheckedOutEvent event) {
        activeCount++;
        int connectionId = event.getConnectionId().getLocalValue();
        ConnectionInfo info = connections.get(connectionId);
        if (info != null) {
            info.incrementUseCount();
            info.setLastActiveTime(LocalDateTime.now());
        }
    }

    @Override
    public void connectionCheckedIn(ConnectionCheckedInEvent event) {
        activeCount--;
    }

    @Override
    public void connectionPoolOpened(ConnectionPoolOpenedEvent event) {
        poolingCount++;
    }

    @Override
    public void connectionPoolClosed(ConnectionPoolClosedEvent event) {
        poolingCount--;
    }

    @Override
    public String toString() {
        StringBuilder buf = new StringBuilder();

        buf.append("{");

        buf.append("\n\tCreateTime:\"");

        buf.append(getCreatedTime().format(DateTimeFormatter.ofPattern(DateUtil.ymd_hms_str_1)));
        buf.append("\"");

        buf.append(",\n\tActiveCount:");
        buf.append(getActiveCount());

        buf.append(",\n\tPoolingCount:");
        buf.append(getPoolingCount());

        buf.append(",\n\tCreateCount:");
        buf.append(getCreateCount());

        buf.append(",\n\tDestroyCount:");
        buf.append(getDestroyCount());

        buf.append(",\n\tCloseCount:");
        buf.append(getCloseCount());

        buf.append(",\n\tConnectCount:");
        buf.append(getConnectCount());

        buf.append(",\n\tConnections:[");

        connections.values()
                .stream()
                .map(connection -> "\n\t\t" + connection.toString()).reduce((s1, s2) -> s1 + "," + s2)
                .ifPresent(buf::append);

        buf.append("\n\t]");

        buf.append("\n}");

        return buf.toString();
    }

    @Getter(value = AccessLevel.PRIVATE)
    static class ConnectionInfo {
        private final int id;
        private final LocalDateTime connectTime;
        private int useCount = 0;
        private LocalDateTime lastActiveTime;

        public ConnectionInfo(int id, LocalDateTime connectTime) {
            this.id = id;
            this.connectTime = connectTime;
            this.lastActiveTime = connectTime;
        }

        public void incrementUseCount() {
            this.useCount++;
        }

        public void setLastActiveTime(LocalDateTime lastActiveTime) {
            this.lastActiveTime = lastActiveTime;
        }

        @Override
        public String toString() {
            StringBuilder buf = new StringBuilder();

            buf.append("{ID:");
            buf.append(id);
            buf.append(", ConnectTime:\"");
            buf.append(getConnectTime().format(DateTimeFormatter.ofPattern(DateUtil.ymd_hms_str_1)));

            buf.append("\", UseCount:");
            buf.append(useCount);

            buf.append(", LastActiveTime:\"");
            buf.append(lastActiveTime.format(DateTimeFormatter.ofPattern(DateUtil.ymd_hms_str_1)));
            buf.append("\"");

            buf.append("}");

            return buf.toString();
        }
    }
}
