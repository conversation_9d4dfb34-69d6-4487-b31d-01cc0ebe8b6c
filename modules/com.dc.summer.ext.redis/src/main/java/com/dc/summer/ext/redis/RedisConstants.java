package com.dc.summer.ext.redis;

import com.dc.summer.model.struct.DBSEntityType;
import redis.clients.jedis.HostAndPortMapper;

public class RedisConstants {
   public static final String HANDLER_SSL = "redis_ssl";
   public static final String PROP_USE_CLUSTER = "redis.use.cluster";
   public static final String PROP_SET_CLIENT_NAME = "redis.set.client";
   public static final String PROP_KEY_DIVIDER = "@dbeaver-redis.key.divider";
   public static final String PROP_KEY_READ_COUNT = "@dbeaver-redis.key.read.count";
   public static final String PROP_PATTERN_READ_COUNT = "@dbeaver-redis.pattern.read.count";
   public static final String PROP_KEY_FILTERS = "@dbeaver-redis.key.filters";
   public static final String PROP_CONNECT_TO = "@dbeaver-redis.timeout.connect";
   public static final String PROP_SOCKET_TO = "@dbeaver-redis.timeout.socket";
   public static final String PROP_SSL_ENABLE = "@dbeaver-redis.use.enable";
   public static final String PROP_SSL_SKIP_HOST_VALIDATION = "@dbeaver-redis.ssl.skipHostValidation";
   public static final DBSEntityType TYPE_KEY;
   public static final String[] REDIS_KEYWORDS;
   public static final String[] REDIS_TYPES;
   public static final int DEF_MAX_KEYS_READ = 10000;
   public static final int DEF_MAX_PATTERN_READ = 100000;
   public static final String DEF_KEY_DEVIDER = ":";
   public static final int DEF_CONNECT_TO = 10000;
   public static final int DEF_SOCKET_TO = 100000;
   public static final String SQL_LUA_BEGIN = "BEGIN";
   public static final String SQL_LUA_END = "END";
   public static final String ATTR_NAME = "name";
   public static final String ATTR_VALUE = "value";
   public static final String ATTR_OUTPUT = "output";
   public static final String ATTR_SCORE = "score";
   public static final HostAndPortMapper NULL_HOST_AND_PORT_MAP;

   static {
      TYPE_KEY = new DBSEntityType("key", "Key", true);
      REDIS_KEYWORDS = new String[]{"ABSTRACT", "ARGUMENTS", "BREAK", "CASE", "CATCH", "CLASS", "CONST", "CONTINUE", "DEFAULT", "DO", "ELSE", "EVAL", "FALSE", "FINALLY", "FOR", "FUNCTION", "GOTO", "IF", "IN", "INSTANCEOF", "INTERFACE", "LET", "NATIVE", "NEW", "NULL", "PACKAGE", "PRIVATE", "PROTECTED", "PUBLIC", "RETURN", "STATIC", "SUPER", "SWITCH", "THIS", "THROW", "THROWS", "TRANSIENT", "TRUE", "TRY", "TYPEOF", "VAR", "VOLATILE", "WHILE"};
      REDIS_TYPES = new String[]{"BOOLEAN", "BYTE", "CHAR", "DOUBLE", "FLOAT", "INT", "LONG", "SHORT", "VOID"};
      NULL_HOST_AND_PORT_MAP = (hostAndPort) -> {
         return null;
      };
   }
}
