package com.dc.summer.ext.redis.model;

import com.dc.summer.model.DBPDataKind;
import com.dc.summer.model.impl.struct.AbstractAttribute;
import com.dc.summer.model.struct.DBSEntityAttribute;

public class RedisKeyAttribute extends AbstractAttribute implements DBSEntityAttribute {
   private final RedisKey key;

   public RedisKeyAttribute(RedisKey key, String name, int ordinalPosition) {
      super(name, "String", 0, ordinalPosition, 2147483647L, 0, 0, true, false);
      this.key = key;
   }

   public String getDefaultValue() {
      return null;
   }

   public RedisKey getParentObject() {
      return this.key;
   }

   public RedisDataSource getDataSource() {
      return this.key.getDataSource();
   }

   public DBPDataKind getDataKind() {
      return this.name.equals("name") ? DBPDataKind.STRING : DBPDataKind.OBJECT;
   }
}
