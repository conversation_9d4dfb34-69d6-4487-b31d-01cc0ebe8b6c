package com.dc.summer.ext.redis.model;

import com.dc.code.NotNull;
import com.dc.summer.model.struct.DBSEntityConstraint;
import com.dc.summer.model.struct.DBSEntityConstraintType;

public class RedisKeySimpleConstraint implements DBSEntityConstraint {
   private final RedisKey key;

   public RedisKeySimpleConstraint(RedisKey key) {
      this.key = key;
   }

   public @NotNull RedisKey getParentObject() {
      return this.key;
   }

   public @NotNull DBSEntityConstraintType getConstraintType() {
      return DBSEntityConstraintType.UNIQUE_KEY;
   }

   public String getDescription() {
      return "Virtual key unique key";
   }

   public RedisDataSource getDataSource() {
      return this.key.getDataSource();
   }

   public String getName() {
      return "PK";
   }

   public boolean isPersisted() {
      return true;
   }
}
