
package com.dc.summer.ext.hana;

import com.dc.summer.ext.generic.GenericDataSourceProvider;
import com.dc.summer.model.connection.DBPConnectionConfiguration;
import com.dc.summer.model.connection.DBPDriver;

public class HANADataSourceProvider extends GenericDataSourceProvider {

    @Override
    public String getConnectionURL(DBPDriver driver, DBPConnectionConfiguration connectionInfo) {
        String connectionURL = super.getConnectionURL(driver, connectionInfo);
        String databaseName = connectionInfo.getDatabaseName();
        if (databaseName != null && !databaseName.isBlank()) {
            connectionURL += "?databaseName=" + databaseName;
        }
        return connectionURL;
    }
}
