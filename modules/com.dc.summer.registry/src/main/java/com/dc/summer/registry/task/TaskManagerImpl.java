
package com.dc.summer.registry.task;

import com.dc.summer.DBException;
import com.dc.summer.Log;
import com.dc.summer.model.DBUtils;
import com.dc.summer.model.data.json.JSONUtils;
import com.dc.summer.model.runtime.RunJob;
import com.dc.summer.model.task.*;
import com.dc.summer.registry.BaseProjectImpl;
import com.dc.summer.utils.ContentUtils;
import com.dc.summer.utils.GeneralUtils;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.stream.JsonWriter;
import org.eclipse.core.runtime.jobs.Job;
import com.dc.code.NotNull;
import com.dc.code.Nullable;
import com.dc.summer.model.app.DBPProject;
import com.dc.summer.model.task.*;
import com.dc.utils.CommonUtils;
import com.dc.utils.IOUtils;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.OutputStreamWriter;
import java.io.Reader;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * TaskManagerImpl
 */
public class TaskManagerImpl implements DBTTaskManager {

    private static final Log log = Log.getLog(TaskManagerImpl.class);

    private static final Gson CONFIG_GSON = new GsonBuilder()
        .setLenient()
        .serializeNulls()
        .setPrettyPrinting()
        .create();

    static final SimpleDateFormat systemDateFormat = new SimpleDateFormat(GeneralUtils.DEFAULT_TIMESTAMP_PATTERN, Locale.ENGLISH);

    private final BaseProjectImpl projectMetadata;
    private final List<TaskImpl> tasks = new ArrayList<>();
    private final List<TaskFolderImpl> tasksFolders = new ArrayList<>();
    private final Path statisticsFolder;

    public TaskManagerImpl(BaseProjectImpl projectMetadata) {
        this.projectMetadata = projectMetadata;
        this.statisticsFolder = projectMetadata.getWorkspace().getMetadataFolder().resolve(TaskConstants.TASK_STATS_FOLDER);

        loadConfiguration();
    }

    @NotNull
    @Override
    public DBTTaskRegistry getRegistry() {
        return TaskRegistry.getInstance();
    }

    @NotNull
    @Override
    public DBPProject getProject() {
        return projectMetadata;
    }

    @NotNull
    @Override
    public DBTTask[] getAllTasks() {
        return tasks.toArray(new DBTTask[0]);
    }

    @Nullable
    @Override
    public DBTTask getTaskById(@NotNull String id) {
        for (DBTTask task : tasks) {
            if (id.equals(task.getId())) {
                return task;
            }
        }
        return null;
    }

    @Nullable
    @Override
    public DBTTask getTaskByName(@NotNull String name) {
        for (DBTTask task : tasks) {
            if (name.equalsIgnoreCase(task.getName())) {
                return task;
            }
        }
        return null;
    }

    @NotNull
    @Override
    public DBTTaskType[] getExistingTaskTypes() {
        Set<DBTTaskType> result = new LinkedHashSet<>();
        for (DBTTask tc : tasks) {
            result.add(tc.getType());
        }
        return result.toArray(new DBTTaskType[0]);
    }

    @NotNull
    @Override
    public DBTTask[] getAllTaskByType(DBTTaskType task) {
        List<DBTTask> result = new ArrayList<>();
        for (DBTTask tc : tasks) {
            if (tc.getType() == task) {
                result.add(tc);
            }
        }
        return result.toArray(new DBTTask[0]);
    }

    public DBTTaskFolder[] getTasksFolders() {
        return tasksFolders.toArray(new DBTTaskFolder[0]);
    }

    @NotNull
    @Override
    public DBTTask createTask(
        @NotNull DBTTaskType taskDescriptor,
        @NotNull String label,
        @Nullable String description,
        @Nullable String taskFolderName,
        @NotNull Map<String, Object> properties) throws DBException
    {
        if (getTaskByName(label) != null) {
            throw new DBException("Task with name '" + label + "' already exists");
        }
        Date createTime = new Date();
        String id = UUID.randomUUID().toString();
        TaskFolderImpl taskFolder = searchTaskFolderByName(taskFolderName);
        TaskImpl task = new TaskImpl(projectMetadata, taskDescriptor, id, label, description, createTime, createTime, taskFolder);
        task.setProperties(properties);

        return task;
    }

    @NotNull
    @Override
    public DBTTask createTemporaryTask(@NotNull DBTTaskType type, @NotNull String label) {
        return new TaskImpl(getProject(), type, TaskConstants.TEMPORARY_ID, label, label, new Date(), null, null);
    }

    @NotNull
    @Override
    public DBTTaskFolder createTaskFolder(@NotNull DBPProject project,
                                          @NotNull String folderName,
                                          @Nullable DBTTaskFolder parentFolder,
                                          @Nullable DBTTask[] folderTasks) throws DBException {
        if (!CommonUtils.isEmpty(tasksFolders)
            && tasksFolders.stream().anyMatch(taskFolder -> taskFolder.getName().equals(folderName))) {
            throw new DBException("Task folder with name '" + folderName + "' already exists");
        }
        TaskFolderImpl taskFolder = new TaskFolderImpl(folderName, parentFolder, project, folderTasks != null ?
            new ArrayList<>(Arrays.asList(folderTasks))
            : new ArrayList<>());
        synchronized (tasksFolders) {
            tasksFolders.add(taskFolder);
        }
        if (parentFolder != null) {
            parentFolder.addFolderToFoldersList(taskFolder);
        }

        TaskRegistry.getInstance().notifyTaskFoldersListeners(new DBTTaskFolderEvent(taskFolder, DBTTaskFolderEvent.Action.TASK_FOLDER_ADD));
        return taskFolder;
    }

    @Override
    public void updateTaskConfiguration(@NotNull DBTTask task) throws DBException {
        if (task.isTemporary()) {
            return;
        }
        DBTTask prevTask = getTaskByName(task.getName());
        if (prevTask != null && prevTask != task) {
            throw new DBException("Task with name '" + task.getName() + "' already exists");
        }

        boolean newTask = false;
        synchronized (tasks) {
            if (!tasks.contains(task)) {
                tasks.add((TaskImpl) task);
                newTask = true;
            }
        }

        TaskRegistry.getInstance().notifyTaskListeners(
            new DBTTaskEvent(
                task,
                newTask ? DBTTaskEvent.Action.TASK_ADD : DBTTaskEvent.Action.TASK_UPDATE));

        if (task.getTaskFolder() != null) {
            TaskRegistry.getInstance().notifyTaskFoldersListeners(new DBTTaskFolderEvent(task.getTaskFolder(), DBTTaskFolderEvent.Action.TASK_FOLDER_UPDATE));
        }

        saveConfiguration();
    }

    @Override
    public void deleteTaskConfiguration(@NotNull DBTTask task) throws DBException {
        DBTScheduler scheduler = TaskRegistry.getInstance().getActiveSchedulerInstance();
        if (scheduler != null) {
            DBTTaskScheduleInfo info = scheduler.getScheduledTaskInfo(task);
            if (info != null) {
                scheduler.removeTaskSchedule(task, info);
            }
        }
        synchronized (tasks) {
            tasks.remove(task);
        }
        saveConfiguration();

        TaskRegistry.getInstance().notifyTaskListeners(new DBTTaskEvent(task, DBTTaskEvent.Action.TASK_REMOVE));
    }

    @Override
    public void removeTaskFolder(@NotNull DBTTaskFolder taskFolder) throws DBException {
        if (!tasksFolders.contains(taskFolder)) {
            throw new DBException("Task folder with name '" + taskFolder.getName() + "' is missing");
        }

        DBTTaskFolder parentFolder = taskFolder.getParentFolder();
        if (parentFolder != null) {
            // Remove folder from parent
            parentFolder.removeFolderFromFoldersList(taskFolder);
        }

        // Remove empty task folder or make task folder empty and then remove it
        // Move all task to the parent folder if it exists
        List<DBTTask> folderTasks = taskFolder.getTasks();
        if (!CommonUtils.isEmpty(folderTasks)) {
            for (DBTTask task : folderTasks) {
                if (task instanceof TaskImpl) {
                    ((TaskImpl) task).setTaskFolder(parentFolder);
                }
            }
        }

        synchronized (tasksFolders) {
            tasksFolders.remove(taskFolder);
        }
        saveConfiguration();

        TaskRegistry.getInstance().notifyTaskFoldersListeners(new DBTTaskFolderEvent(taskFolder, DBTTaskFolderEvent.Action.TASK_FOLDER_REMOVE));
    }

    @NotNull
    @Override
    public Path getStatisticsFolder() {
        return statisticsFolder;
    }

    @Override
    public RunJob runTask(@NotNull DBTTask task, @NotNull DBTTaskExecutionListener listener, @NotNull Map<String, Object> options) {
        TaskRunJob runJob = new TaskRunJob((TaskImpl) task, Locale.getDefault(), listener);
        runJob.schedule();
        return runJob;
    }

    private void loadConfiguration() {
        Path configFile = getConfigFile(false);
        if (!Files.exists(configFile)) {
            return;
        }
        try (Reader configReader = Files.newBufferedReader(configFile, StandardCharsets.UTF_8)) {
            Map<String, Object> jsonMap = JSONUtils.parseMap(CONFIG_GSON, configReader);

            // First read and create folders
            for (Map.Entry<String, Map<String, Object>> folderMap : JSONUtils.getNestedObjects(jsonMap, TaskConstants.TASKS_FOLDERS_TAG)) {
                String folderName = folderMap.getKey();
                if (CommonUtils.isNotEmpty(folderName)) {
                    Object property = JSONUtils.getObjectProperty(folderMap.getValue(), TaskConstants.TAG_PARENT);
                    TaskFolderImpl parentFolder = null;
                    if (property != null) {
                        Optional<TaskFolderImpl> first = tasksFolders.stream()
                            .filter(e -> e.getName().equals(property.toString()))
                            .findFirst();
                        if (first.isPresent()) {
                            parentFolder = first.get();
                        }
                    }
                    createTaskFolder(projectMetadata, folderName, parentFolder, new DBTTask[0]);
                }
            }

            for (Map.Entry<String, Object> taskMap : jsonMap.entrySet()) {
                Map<String, Object> taskJSON = (Map<String, Object>) taskMap.getValue();

                try {
                    String id = taskMap.getKey();
                    if (!id.startsWith(TaskConstants.TASKS_FOLDERS_TAG)) {
                        String task = JSONUtils.getString(taskJSON, TaskConstants.TAG_TASK);
                        String label = CommonUtils.toString(JSONUtils.getString(taskJSON, TaskConstants.TAG_LABEL), id);
                        String description = JSONUtils.getString(taskJSON, TaskConstants.TAG_DESCRIPTION);
                        String taskFolderName = JSONUtils.getString(taskJSON, TaskConstants.TAG_TASK_FOLDER);
                        Date createTime = systemDateFormat.parse(JSONUtils.getString(taskJSON, TaskConstants.TAG_CREATE_TIME));
                        Date updateTime = systemDateFormat.parse(JSONUtils.getString(taskJSON, TaskConstants.TAG_UPDATE_TIME));
                        Map<String, Object> state = JSONUtils.getObject(taskJSON, TaskConstants.TAG_STATE);

                        DBTTaskType taskDescriptor = getRegistry().getTaskType(task);
                        if (taskDescriptor == null) {
                            log.error("Can't find task descriptor " + task);
                            continue;
                        }

                        TaskFolderImpl taskFolder = searchTaskFolderByName(taskFolderName);
                        TaskImpl taskConfig = new TaskImpl(projectMetadata, taskDescriptor, id, label, description, createTime, updateTime, taskFolder);
                        taskConfig.setProperties(state);
                        if (taskFolder != null) {
                            taskFolder.addTaskToFolder(taskConfig);
                            if (!tasksFolders.contains(taskFolder)) {
                                synchronized (tasksFolders) {
                                    tasksFolders.add(taskFolder);
                                }
                            }
                        }

                        synchronized (tasks) {
                            tasks.add(taskConfig);
                        }
                    }

                } catch (Exception e) {
                    log.warn("Error parsing task configuration", e);
                }

            }
        } catch (Exception e) {
            log.error("Error loading tasks configuration", e);
        }
    }

    private TaskFolderImpl searchTaskFolderByName(String taskFolderName) {
        TaskFolderImpl taskFolder = null;
        if (CommonUtils.isNotEmpty(taskFolderName)) {
            taskFolder = DBUtils.findObject(tasksFolders, taskFolderName);
            if (taskFolder == null) {
                taskFolder = new TaskFolderImpl(taskFolderName, null, projectMetadata, new ArrayList<>());
                synchronized (tasksFolders) {
                    tasksFolders.add(taskFolder);
                }
            }
        }
        return taskFolder;
    }

    public void saveConfiguration() {
        Path configFile = getConfigFile(true);
        try {
            if (Files.exists(configFile)) {
                ContentUtils.makeFileBackup(configFile);
            }
            if (tasks.isEmpty() && CommonUtils.isEmpty(tasksFolders)) {
                try {
                    Files.delete(configFile);
                } catch (IOException e) {
                    log.error("Error deleting file " + configFile.toAbsolutePath(), e);
                }
                return;
            }
        } catch (Exception e) {
            log.error("Error processing config file", e);
        }

        ByteArrayOutputStream dsConfigBuffer = new ByteArrayOutputStream(10000);
        try (OutputStreamWriter osw = new OutputStreamWriter(dsConfigBuffer, StandardCharsets.UTF_8)) {
            try (JsonWriter jsonWriter = CONFIG_GSON.newJsonWriter(osw)) {
                synchronized (tasks) {
                    serializeTasks(jsonWriter);
                }
            }
        } catch (IOException e) {
            log.error(e);
            return;
        }

        try {
            IOUtils.writeFileFromBuffer(configFile.toFile(), dsConfigBuffer.toByteArray());
        } catch (Exception e) {
            log.error("Error saving configuration to a file " + configFile.toAbsolutePath(), e);
        }
    }

    @Override
    public void updateConfiguration() {
        saveConfiguration();
    }

    private void serializeTasks(@NotNull JsonWriter jsonWriter) throws IOException {
        jsonWriter.setIndent("\t");
        jsonWriter.beginObject();
        if (!CommonUtils.isEmpty(tasksFolders)) {
            jsonWriter.name(TaskConstants.TASKS_FOLDERS_TAG);
            jsonWriter.beginObject();
            for (TaskFolderImpl taskFolder : tasksFolders) {
                jsonWriter.name(taskFolder.getName());
                jsonWriter.beginObject();
                if (taskFolder.getParentFolder() != null) {
                    JSONUtils.field(jsonWriter, TaskConstants.TAG_PARENT, taskFolder.getParentFolder().getName());
                }
                jsonWriter.endObject();
            }
            jsonWriter.endObject();
        }
        for (TaskImpl task : tasks) {
            jsonWriter.name(task.getId());
            jsonWriter.beginObject();
            JSONUtils.field(jsonWriter, TaskConstants.TAG_TASK, task.getType().getId());
            JSONUtils.field(jsonWriter, TaskConstants.TAG_LABEL, task.getName());
            JSONUtils.field(jsonWriter, TaskConstants.TAG_DESCRIPTION, task.getDescription());
            DBTTaskFolder taskFolder = task.getTaskFolder();
            if (taskFolder != null) {
                JSONUtils.field(jsonWriter, TaskConstants.TAG_TASK_FOLDER, taskFolder.getName());
            }
            JSONUtils.field(jsonWriter, TaskConstants.TAG_CREATE_TIME, systemDateFormat.format(task.getCreateTime()));
            JSONUtils.field(jsonWriter, TaskConstants.TAG_UPDATE_TIME, systemDateFormat.format(task.getUpdateTime()));
            JSONUtils.serializeProperties(jsonWriter, TaskConstants.TAG_STATE, task.getProperties());
            jsonWriter.endObject();
        }
        jsonWriter.endObject();
    }

    private Path getConfigFile(boolean create) {
        return projectMetadata.getMetadataFolder(create).resolve(TaskConstants.CONFIG_FILE);
    }

}
