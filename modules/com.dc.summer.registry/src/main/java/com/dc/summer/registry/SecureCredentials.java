
package com.dc.summer.registry;

import com.dc.code.NotNull;
import com.dc.code.Nullable;
import com.dc.summer.model.DBPDataSourceContainer;
import com.dc.summer.model.access.DBAAuthProfile;
import com.dc.summer.model.net.DBWHandlerConfiguration;

import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.Map;

class SecureCredentials {

    @Nullable
    private String userName;
    @Nullable
    private String userPassword;
    @Nullable
    private Map<String, String> properties;

    public SecureCredentials() {
    }

    public SecureCredentials(@NotNull DBPDataSourceContainer dataSource) {
        this.userName = dataSource.getConnectionConfiguration().getUserName();
        this.userPassword = dataSource.isSavePassword() ? dataSource.getConnectionConfiguration().getUserPassword() : null;
    }

    public SecureCredentials(@NotNull DBAAuthProfile profile) {
        this.userName = profile.getUserName();
        this.userPassword = profile.getUserPassword();
        this.properties = profile.getProperties();
    }

    public SecureCredentials(@NotNull DBWHandlerConfiguration handlerConfiguration) {
        this.userName = handlerConfiguration.getUserName();
        this.userPassword = handlerConfiguration.isSavePassword() ? handlerConfiguration.getPassword() : null;
    }

    @Nullable
    public String getUserName() {
        return userName;
    }

    public void setUserName(@Nullable String userName) {
        this.userName = userName;
    }

    @Nullable
    public String getUserPassword() {
        return userPassword;
    }

    public void setUserPassword(@Nullable String userPassword) {
        this.userPassword = userPassword;
    }

    @Nullable
    public Map<String, String> getProperties() {
        return properties;
    }

    public void setProperties(@NotNull Map<String, String> properties) {
        if (this.properties != null) {
            this.properties.clear();
            this.properties.putAll(properties);
        } else {
            this.properties = new HashMap<>(properties);
        }
    }

    public void setSecureProp(String key, String value) {
        if (this.properties == null) {
            this.properties = new LinkedHashMap<>();
        }
        this.properties.put(key, value);
    }
}
