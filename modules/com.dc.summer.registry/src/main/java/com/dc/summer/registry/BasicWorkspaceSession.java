
package com.dc.summer.registry;

import com.dc.summer.model.app.DBPWorkspace;
import com.dc.summer.model.auth.SMSession;
import com.sun.security.auth.module.NTSystem;
import com.sun.security.auth.module.UnixSystem;
import com.dc.code.NotNull;
import com.dc.code.Nullable;
import com.dc.summer.model.DBConstants;
import com.dc.summer.model.app.DBPProject;
import com.dc.summer.model.auth.SMAuthSpace;
import com.dc.summer.model.auth.SMSessionContext;
import com.dc.summer.model.auth.SMSessionPrincipal;
import com.dc.summer.model.auth.impl.AbstractSessionPersistent;
import com.dc.summer.model.security.SMSessionType;
import com.dc.summer.utils.RuntimeUtils;
import com.dc.utils.CommonUtils;
import com.dc.utils.StandardConstants;

public class BasicWorkspaceSession extends AbstractSessionPersistent implements SMSession, SMSessionPrincipal {

    public static final SMSessionType DB_SESSION_TYPE = new SMSessionType("DBeaver");

    private final DBPWorkspace workspace;
    private String userName;
    private String domainName;

    public BasicWorkspaceSession(@NotNull DBPWorkspace workspace) {
        this.workspace = workspace;
        try {
            if (RuntimeUtils.isWindows()) {
                NTSystem ntSystem = new NTSystem();
                userName = ntSystem.getName();
                domainName = ntSystem.getDomain();
            } else {
                UnixSystem unixSystem = new UnixSystem();
                userName = unixSystem.getUsername();
            }
        } catch (Exception e) {
            // Not supported on this system
        }
        if (CommonUtils.isEmpty(userName)) {
            userName = System.getProperty(StandardConstants.ENV_USER_NAME);
        }
        if (CommonUtils.isEmpty(userName)) {
            userName = "unknown";
        }

        if (CommonUtils.isEmpty(domainName)) {
            if (RuntimeUtils.isWindows()) {
                domainName = System.getenv("USERDOMAIN");
            }
            if (CommonUtils.isEmpty(domainName)) {
                domainName = DBConstants.LOCAL_DOMAIN_NAME;
            }
        }
    }

    @NotNull
    @Override
    public SMAuthSpace getSessionSpace() {
        return workspace;
    }

    @NotNull
    @Override
    public SMSessionContext getSessionContext() {
        return workspace.getAuthContext();
    }

    @Override
    public SMSessionPrincipal getSessionPrincipal() {
        return this;
    }

    @NotNull
    @Override
    public String getSessionId() {
        return workspace.getWorkspaceId();
    }

    @Override
    public boolean isApplicationSession() {
        return true;
    }

    @Nullable
    @Override
    public DBPProject getSingletonProject() {
        return null;
    }

    @Override
    public String getUserDomain() {
        return domainName;
    }

    @Override
    public String getUserName() {
        return userName;
    }
}
