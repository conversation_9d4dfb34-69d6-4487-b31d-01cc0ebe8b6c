package com.dc.parser.model.context.segment.select.projection.impl;

import com.dc.parser.model.context.segment.select.projection.Projection;
import com.dc.parser.model.segment.SQLSegment;
import com.dc.parser.model.value.identifier.IdentifierValue;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.ToString;

import java.util.Optional;

/**
 * Derived projection.
 */
@RequiredArgsConstructor
@Getter
@EqualsAndHashCode
@ToString
public final class DerivedProjection implements Projection {

    private final String expression;

    private final IdentifierValue alias;

    private final SQLSegment derivedProjectionSegment;

    @Override
    public String getColumnName() {
        return expression;
    }

    @Override
    public String getColumnLabel() {
        return getAlias().map(IdentifierValue::getValue).orElse(expression);
    }

    @Override
    public Optional<IdentifierValue> getAlias() {
        return Optional.ofNullable(alias);
    }
}
