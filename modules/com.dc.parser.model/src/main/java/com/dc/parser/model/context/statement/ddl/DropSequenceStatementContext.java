package com.dc.parser.model.context.statement.ddl;

import com.dc.parser.model.context.statement.CommonSQLStatementContext;
import com.dc.parser.model.statement.ddl.DropSequenceStatement;
import com.dc.summer.parser.sql.constants.SqlConstant;
import com.dc.summer.parser.sql.model.SqlAuthModel;
import lombok.Getter;

/**
 * Drop sequence statement context.
 */
@Getter
public class DropSequenceStatementContext extends CommonSQLStatementContext {

    public DropSequenceStatementContext(final DropSequenceStatement sqlStatement, final String currentDatabaseName) {
        super(sqlStatement);

        // 构造鉴权模型
        extractSqlAuthModel(sqlStatement, currentDatabaseName);
    }

    public void extractSqlAuthModel(final DropSequenceStatement sqlStatement, final String currentDatabaseName) {
        SqlAuthModel sqlAuthModel = new SqlAuthModel();
        sqlAuthModel.setOperation(SqlConstant.KEY_DROP);
        sqlAuthModel.setType(SqlConstant.KEY_SEQUENCE);
        sqlAuthModel.setSchemaName(currentDatabaseName);
        sqlAuthModel.setName(sqlStatement.getSequenceName().getValue());
        sqlAuthModel.setSchemaName(sqlStatement.getOwner().map(owner -> owner.getIdentifier().getValue()).orElse(currentDatabaseName));
        addSqlAuthModel(sqlAuthModel);
    }

    @Override
    public DropSequenceStatement getSqlStatement() {
        return (DropSequenceStatement) super.getSqlStatement();
    }
}
