
package com.dc.parser.model.segment.dml.expr;

import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Setter;

/**
 * Binary operation expression.
 */
@RequiredArgsConstructor
@Getter
@Setter
public final class BinaryOperationExpression implements ExpressionSegment {
    
    private final int startIndex;
    
    private final int stopIndex;
    
    private final ExpressionSegment left;
    
    private final ExpressionSegment right;
    
    private final String operator;
    
    private final String text;
}
