package com.dc.parser.model.metadata.identifier;

import com.dc.infra.database.DatabaseTypedSPI;
import com.dc.infra.spi.annotation.SingletonSPI;

/**
 * Database dialect identifier handler.
 */
@SingletonSPI
public interface DatabaseDialectIdentifierHandler extends DatabaseTypedSPI {

    /**
     * Whether identifier is case-sensitive.
     *
     * @return is case-sensitive or insensitive
     */
    boolean isCaseSensitive();
}
