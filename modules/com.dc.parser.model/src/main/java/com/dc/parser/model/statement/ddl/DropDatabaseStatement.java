package com.dc.parser.model.statement.ddl;

import com.dc.parser.model.segment.generic.DatabaseSegment;
import com.dc.parser.model.statement.AbstractSQLStatement;
import lombok.Getter;
import lombok.Setter;

/**
 * Drop database statement.
 */
@Getter
@Setter
public abstract class DropDatabaseStatement extends AbstractSQLStatement implements DDLStatement {
    
    private boolean ifExists;

    private DatabaseSegment databaseName;
}
