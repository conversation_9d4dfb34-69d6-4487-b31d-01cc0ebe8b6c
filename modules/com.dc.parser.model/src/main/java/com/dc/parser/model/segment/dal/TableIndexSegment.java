package com.dc.parser.model.segment.dal;

import com.dc.parser.model.segment.SQLSegment;
import com.dc.parser.model.segment.generic.table.SimpleTableSegment;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * Table index segment.
 */
@RequiredArgsConstructor
@Getter
public abstract class TableIndexSegment implements SQLSegment {

    private final int startIndex;

    private final int stopIndex;

    private final SimpleTableSegment table;
}
