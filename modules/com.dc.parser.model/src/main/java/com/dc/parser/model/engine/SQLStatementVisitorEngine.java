
package com.dc.parser.model.engine;

import com.dc.parser.model.api.ASTNode;
import lombok.RequiredArgsConstructor;
import org.antlr.v4.runtime.Token;
import com.dc.infra.database.type.DatabaseType;
import com.dc.infra.spi.TypedSPILoader;
import com.dc.parser.model.api.visitor.statement.SQLStatementVisitor;
import com.dc.parser.model.engine.visitor.SQLStatementVisitorFactory;
import com.dc.parser.model.engine.visitor.SQLVisitorRule;
import com.dc.parser.model.segment.generic.CommentSegment;
import com.dc.parser.model.statement.AbstractSQLStatement;
import com.dc.parser.model.statement.SQLStatement;

/**
 * SQL statement visitor engine.
 */
@RequiredArgsConstructor
public final class SQLStatementVisitorEngine {
    
    private final DatabaseType databaseType;
    
    public SQLStatementVisitorEngine(final String databaseType) {
        this(TypedSPILoader.getService(DatabaseType.class, databaseType));
    }
    
    /**
     * Visit parse context.
     *
     * @param parseASTNode parse AST node
     * @return SQL visitor result
     */
    public SQLStatement visit(final ParseASTNode parseASTNode) {
        SQLStatementVisitor visitor = SQLStatementVisitorFactory.newInstance(databaseType, SQLVisitorRule.valueOf(parseASTNode.getRootNode().getClass()));
        ASTNode result = parseASTNode.getRootNode().accept(visitor);
        appendSQLComments(parseASTNode, result);
        apppendParseTree(parseASTNode, result);
        return (SQLStatement) result;
    }
    
    private <T> void appendSQLComments(final ParseASTNode parseASTNode, final T visitResult) {
        if (visitResult instanceof AbstractSQLStatement) {
            for (Token each : parseASTNode.getHiddenTokens()) {
                ((AbstractSQLStatement) visitResult).getCommentSegments().add(new CommentSegment(each.getText(), each.getStartIndex(), each.getStopIndex()));
            }
        }
    }

    private <T> void apppendParseTree(final ParseASTNode parseASTNode, final T visitResult) {
        if (visitResult instanceof AbstractSQLStatement) {
            ((AbstractSQLStatement) visitResult).addParseTree(parseASTNode.getRootNode());
        }
    }
}
