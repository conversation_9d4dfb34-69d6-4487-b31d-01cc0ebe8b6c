
package com.dc.parser.model.segment.dml.expr;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * Not expression.
 */
@RequiredArgsConstructor
@Getter
public final class NotExpression implements ExpressionSegment {
    
    private final int startIndex;
    
    private final int stopIndex;
    
    private final ExpressionSegment expression;
    
    private final Boolean notSign;
    
    @Override
    public String getText() {
        return expression.getText();
    }
}
