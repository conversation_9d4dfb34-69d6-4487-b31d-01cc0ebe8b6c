package com.dc.parser.model.metadata.data.loader;

import com.dc.infra.database.DatabaseTypedSPI;
import com.dc.infra.spi.annotation.SingletonSPI;
import com.dc.parser.model.metadata.model.TableMetaData;

import java.sql.SQLException;

/**
 * Dialect meta data loader.
 */
@SingletonSPI
public interface DialectMetaDataLoader extends DatabaseTypedSPI {

    /**
     * Load table meta data.
     *
     * @param material meta data loader material
     * @return table meta data collection
     * @throws SQLException SQL exception
     */
    TableMetaData loadTableMetaData(MetaDataLoaderMaterial material) throws SQLException;
}
