
package com.dc.parser.model.segment.dml.predicate;

import lombok.Getter;
import com.dc.parser.model.segment.SQLSegment;
import com.dc.parser.model.segment.dml.expr.ExpressionSegment;

import java.util.Collection;
import java.util.LinkedList;

/**
 * And predicate.
 */
@Getter
public final class AndPredicate implements SQLSegment {
    
    private final int startIndex = 0;
    
    private final int stopIndex = 0;
    
    private final Collection<ExpressionSegment> predicates = new LinkedList<>();
}
