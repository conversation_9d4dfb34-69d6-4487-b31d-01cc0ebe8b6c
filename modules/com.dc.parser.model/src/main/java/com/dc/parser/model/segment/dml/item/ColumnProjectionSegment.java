
package com.dc.parser.model.segment.dml.item;

import lombok.Getter;
import lombok.Setter;
import com.dc.parser.model.segment.dml.column.ColumnSegment;
import com.dc.parser.model.segment.generic.AliasAvailable;
import com.dc.parser.model.segment.generic.AliasSegment;
import com.dc.parser.model.value.identifier.IdentifierValue;

import java.util.Optional;

/**
 * Column projection segment.
 */
@Setter
@Getter
public final class ColumnProjectionSegment implements ProjectionSegment, AliasAvailable {
    
    private final ColumnSegment column;
    
    private AliasSegment alias;
    
    private boolean visible = true;
    
    public ColumnProjectionSegment(final ColumnSegment columnSegment) {
        column = columnSegment;
    }
    
    @Override
    public String getColumnLabel() {
        return getAliasName().orElse(column.getIdentifier().getValue());
    }
    
    @Override
    public Optional<String> getAliasName() {
        return null == alias ? Optional.empty() : Optional.ofNullable(alias.getIdentifier().getValue());
    }
    
    @Override
    public Optional<IdentifierValue> getAlias() {
        return Optional.ofNullable(alias).map(AliasSegment::getIdentifier);
    }
    
    @Override
    public int getStartIndex() {
        return null != alias && alias.getStartIndex() < column.getStartIndex() ? alias.getStartIndex() : column.getStartIndex();
    }
    
    @Override
    public int getStopIndex() {
        return null != alias && alias.getStopIndex() > column.getStopIndex() ? alias.getStopIndex() : column.getStopIndex();
    }
    
    /**
     * Get alias segment.
     * @return alias segment
     */
    public Optional<AliasSegment> getAliasSegment() {
        return Optional.ofNullable(alias);
    }
}
