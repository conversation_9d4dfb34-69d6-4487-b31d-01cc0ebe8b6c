
package com.dc.parser.model.segment.generic;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import com.dc.parser.model.segment.SQLSegment;
import com.dc.parser.model.segment.dml.expr.complex.CommonTableExpressionSegment;

import java.util.Collection;

/**
 * With segment.
 */
@AllArgsConstructor
@RequiredArgsConstructor
@Getter
public final class WithSegment implements SQLSegment {
    
    private final int startIndex;
    
    private final int stopIndex;
    
    private final Collection<CommonTableExpressionSegment> commonTableExpressions;

    private boolean recursive;
}
