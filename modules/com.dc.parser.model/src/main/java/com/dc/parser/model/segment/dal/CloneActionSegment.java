package com.dc.parser.model.segment.dal;

import com.dc.parser.model.segment.SQLSegment;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Setter;

/**
 * Clone action segment.
 */
@RequiredArgsConstructor
@Getter
@Setter
public final class CloneActionSegment implements SQLSegment {
    
    private final int startIndex;
    
    private final int stopIndex;
    
    private String cloneDir;
    
    private CloneInstanceSegment cloneInstance;
}
