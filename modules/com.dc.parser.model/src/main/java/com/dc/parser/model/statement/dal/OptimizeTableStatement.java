package com.dc.parser.model.statement.dal;

import com.dc.parser.model.segment.generic.table.SimpleTableSegment;
import com.dc.parser.model.statement.AbstractSQLStatement;
import lombok.Getter;

import java.util.Collection;
import java.util.LinkedList;

/**
 * Optimize table statement.
 */
@Getter
public abstract class OptimizeTableStatement extends AbstractSQLStatement implements DALStatement {

    private final Collection<SimpleTableSegment> tables = new LinkedList<>();
}
