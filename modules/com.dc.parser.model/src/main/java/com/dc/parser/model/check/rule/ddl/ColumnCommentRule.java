package com.dc.parser.model.check.rule.ddl;

import com.dc.parser.model.enums.CheckRuleUniqueKey;
import com.dc.parser.model.check.rule.CheckResult;
import com.dc.parser.model.check.rule.CheckRuleParameter;
import com.dc.parser.model.check.rule.SQLRule;
import com.dc.parser.model.segment.ddl.column.ColumnDefinitionSegment;
import com.dc.parser.model.segment.ddl.column.alter.AddColumnDefinitionSegment;
import com.dc.parser.model.statement.SQLStatement;
import com.dc.parser.model.statement.ddl.AlterTableStatement;
import com.dc.parser.model.statement.ddl.CreateTableStatement;

import java.util.Collection;
import java.util.function.Predicate;

import static com.dc.parser.model.util.CreateTableHelper.getColumnDefinitionsFromAlterTableStatement;

public class ColumnCommentRule implements SQLRule {

    @Override
    public String getType() {
        return CheckRuleUniqueKey.DDL_CHECK_COLUMN_COMMENT.getValue();
    }

    protected Predicate<ColumnDefinitionSegment> predicate = columnDefinitionSegment -> columnDefinitionSegment.getComment() != null;

    @Override
    public CheckResult check(SQLStatement sqlStatement, CheckRuleParameter parameter) {

        boolean checkColumnComment = true;

        if (sqlStatement instanceof CreateTableStatement) {
            CreateTableStatement createTableStatement = (CreateTableStatement) sqlStatement;

            checkColumnComment = getColumnDefinitionsFromAlterTableStatement(createTableStatement)
                    .allMatch(predicate);

        } else if (sqlStatement instanceof AlterTableStatement) {

            AlterTableStatement alterTableStatement = (AlterTableStatement) sqlStatement;

            checkColumnComment = alterTableStatement.getAddColumnDefinitions()
                    .stream()
                    .map(AddColumnDefinitionSegment::getColumnDefinitions)
                    .flatMap(Collection::stream)
                    .allMatch(predicate);
        }

        if (!checkColumnComment) {
            return CheckResult.buildFailResult(parameter.getCheckRuleContent());
        }

        return CheckResult.DEFAULT_SUCCESS_RESULT;
    }
}
