
package com.dc.parser.model.segment.generic;

import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Setter;
import com.dc.parser.model.segment.SQLSegment;
import com.dc.parser.model.segment.dml.column.ColumnSegment;
import com.dc.parser.model.segment.dml.item.ProjectionsSegment;
import com.dc.parser.model.segment.generic.table.SimpleTableSegment;

import java.util.Collection;
import java.util.LinkedList;

/**
 * Output segment.
 */
@RequiredArgsConstructor
@Getter
public final class OutputSegment implements SQLSegment {
    
    private final int startIndex;
    
    private final int stopIndex;
    
    @Setter
    private SimpleTableSegment table;
    
    @Setter
    private ProjectionsSegment outputColumns;
    
    private final Collection<ColumnSegment> tableColumns = new LinkedList<>();
}
