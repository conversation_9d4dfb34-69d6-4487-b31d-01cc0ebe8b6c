
package com.dc.parser.model.segment.generic;

import lombok.Getter;
import lombok.RequiredArgsConstructor;
import com.dc.parser.model.segment.SQLSegment;
import com.dc.parser.model.value.identifier.IdentifierValue;

/**
 * Name segment.
 */
@RequiredArgsConstructor
@Getter
public final class NameSegment implements SQLSegment {
    
    private final int startIndex;
    
    private final int stopIndex;
    
    private final IdentifierValue identifier;
}
