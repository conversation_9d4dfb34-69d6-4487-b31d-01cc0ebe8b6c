package com.dc.parser.model.context.type;

import com.dc.parser.model.segment.ddl.index.IndexSegment;
import com.dc.parser.model.segment.dml.column.ColumnSegment;

import java.util.Collection;

/**
 * Index available.
 */
public interface IndexAvailable {

    /**
     * Get index segments.
     *
     * @return index segments
     */
    Collection<IndexSegment> getIndexes();

    /**
     * Get index columns.
     *
     * @return index columns
     */
    Collection<ColumnSegment> getIndexColumns();
}
