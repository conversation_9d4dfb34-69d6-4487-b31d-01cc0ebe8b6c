package com.dc.parser.model.util.token;

public class StringToken extends SQLToken {

    private String text;

    public StringToken(int startIndex) {
        super(startIndex, startIndex);
    }

    public StringToken(int startIndex, int stopIndex) {
        super(startIndex, stopIndex);
    }

    public String getText() {
        return this.text;
    }

    public void setText(String text) {
        this.text = text;
    }
}
