package com.dc.parser.model.check.rule.ddl;

import com.dc.parser.model.enums.CheckRuleUniqueKey;
import com.dc.parser.model.check.rule.CheckResult;
import com.dc.parser.model.check.rule.CheckRuleParameter;
import com.dc.parser.model.check.rule.SQLRule;
import com.dc.parser.model.segment.ddl.column.ColumnDefinitionSegment;
import com.dc.parser.model.segment.ddl.column.alter.AddColumnDefinitionSegment;
import com.dc.parser.model.segment.ddl.column.alter.RenameColumnSegment;
import com.dc.parser.model.segment.dml.column.ColumnSegment;
import com.dc.parser.model.statement.SQLStatement;
import com.dc.parser.model.statement.ddl.AlterTableStatement;
import com.dc.parser.model.statement.ddl.CreateTableStatement;

import java.util.Arrays;
import java.util.Collection;
import java.util.Locale;

public class ColumnNameUsingKeywordRule implements SQLRule {

    @Override
    public CheckResult check(SQLStatement sqlStatement, CheckRuleParameter parameter) {

        if (!(sqlStatement instanceof CreateTableStatement) && !(sqlStatement instanceof AlterTableStatement)) {
            return CheckResult.DEFAULT_SUCCESS_RESULT;
        }

        String[] objectName = parameter.getCheckRuleContent().getValue().toUpperCase(Locale.ROOT).split(",");

        if (sqlStatement instanceof CreateTableStatement) {
            CreateTableStatement checkStatement = (CreateTableStatement) sqlStatement;
            if (checkStatement.getRelationalTable().isPresent() && !checkStatement.getRelationalTable().get().getColumnDefinitions().isEmpty()) {
                Collection<ColumnDefinitionSegment> collect = checkStatement.getRelationalTable().get().getColumnDefinitions();
                for (ColumnDefinitionSegment columnDefinitionSegment : collect) {
                    if (buildFailResult(columnDefinitionSegment.getColumnName(), objectName)) {
                        return CheckResult.buildFailResult(parameter.getCheckRuleContent());
                    }
                }
            }
        } else {
            AlterTableStatement checkStatement = (AlterTableStatement) sqlStatement;
            for (AddColumnDefinitionSegment addColumnDefinition : checkStatement.getAddColumnDefinitions()) {
                if (addColumnDefinition.getColumnDefinitions() != null) {
                    for (ColumnDefinitionSegment columnDefinition : addColumnDefinition.getColumnDefinitions()) {
                        if (buildFailResult(columnDefinition.getColumnName(), objectName)) {
                            return CheckResult.buildFailResult(parameter.getCheckRuleContent());
                        }
                    }
                }
            }
            for (RenameColumnSegment renameColumnDefinition : checkStatement.getRenameColumnDefinitions()) {
                if (buildFailResult(renameColumnDefinition.getColumnName(), objectName)) {
                    return CheckResult.buildFailResult(parameter.getCheckRuleContent());
                }
            }
        }

        return CheckResult.DEFAULT_SUCCESS_RESULT;
    }

    public static boolean buildFailResult(ColumnSegment columnSegment, String[] objectName) {
        if (columnSegment != null && columnSegment.getIdentifier() != null) {
            String columnName = columnSegment.getIdentifier().getValue();
            return Arrays.asList(objectName).contains(columnName.toUpperCase(Locale.ROOT));
        }
        return false;
    }

    @Override
    public String getType() {
        return CheckRuleUniqueKey.DDL_CHECK_COLUMN_NAME_USING_KEYWORD.getValue();
    }

}
