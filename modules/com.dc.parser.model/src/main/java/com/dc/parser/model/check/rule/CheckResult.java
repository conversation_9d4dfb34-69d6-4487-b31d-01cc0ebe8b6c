package com.dc.parser.model.check.rule;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Data
@AllArgsConstructor
@ToString
@EqualsAndHashCode
public class CheckResult {

    public static CheckResult DEFAULT_SUCCESS_RESULT = new CheckResult(true);

    private boolean success;
    private String ruleName; // 命中的规则名称
    private String ruleDesc; // 规则描述
    private int level; // 等级
    private int status; // 规则开关
    private int isAlert; // 是否触发告警

    public CheckResult(boolean success) {
        this.success = success;
    }

    public static CheckResult buildFailResult(CheckRuleContent checkRuleContent) {
        return new CheckResult(false, checkRuleContent.getRuleName(), checkRuleContent.getRuleDesc(), checkRuleContent.getLevel(), checkRuleContent.getStatus(), checkRuleContent.getIsAlert());
    }

}
