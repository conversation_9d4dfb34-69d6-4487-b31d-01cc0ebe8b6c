package com.dc.parser.model.check.rule.ddl;

import com.dc.parser.model.enums.CheckRuleUniqueKey;
import com.dc.parser.model.check.rule.CheckResult;
import com.dc.parser.model.check.rule.CheckRuleParameter;
import com.dc.parser.model.check.rule.SQLRule;
import com.dc.parser.model.statement.SQLStatement;
import com.dc.parser.model.statement.ddl.CreateTableStatement;

public class TableMustTablespaceRule implements SQLRule {

    @Override
    public CheckResult check(SQLStatement sqlStatement, CheckRuleParameter parameter) {

        if (!(sqlStatement instanceof CreateTableStatement)) {
            return CheckResult.DEFAULT_SUCCESS_RESULT;
        }

        return CheckResult.buildFailResult(parameter.getCheckRuleContent());
    }

    @Override
    public String getType() {
        return CheckRuleUniqueKey.DDL_CHECK_TABLE_MUST_TABLESPACE.getValue();
    }

}
