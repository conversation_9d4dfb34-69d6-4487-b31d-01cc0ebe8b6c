package com.dc.summer.struct.compare.model;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

@AllArgsConstructor
@Getter
public enum CMPDatabaseObjectType {


    TABLE(liquibase.structure.core.Table.class, true),
    COLUMNS(liquibase.structure.core.Column.class, true),
    PRIMARY_KEY(liquibase.structure.core.PrimaryKey.class, true),
    UNIQUE_CONSTRAINT(liquibase.structure.core.UniqueConstraint.class, true),
    FOREIGN_KEY(liquibase.structure.core.ForeignKey.class, true),
    INDEX(liquibase.structure.core.Index.class, true),
    CHECK_CONSTRAINT(com.datical.liquibase.ext.storedlogic.checkconstraint.CheckConstraint.class, true),
    TRIGGER(com.datical.liquibase.ext.storedlogic.trigger.Trigger.class, true),
    EXCLUDE_CONSTRAINT(liquibase.structure.core.ExcludeConstraint.class, true),

    VIEW(liquibase.structure.core.View.class, true),
    SEQUENCE(liquibase.structure.core.Sequence.class, true),

    STORED_PROCEDURE(liquibase.structure.core.StoredProcedure.class, false),
    SYNONYM(com.datical.liquibase.ext.appdba.synonym.Synonym.class, false),
    DATABASE_PACKAGE(com.datical.liquibase.ext.storedlogic.databasepackage.DatabasePackage.class, false),
    DATABASE_PACKAGE_BODY(com.datical.liquibase.ext.storedlogic.databasepackage.DatabasePackageBody.class, false),
    FUNCTION(com.datical.liquibase.ext.storedlogic.function.Function.class, false),
    ;

    private final Class<? extends liquibase.structure.DatabaseObject> databaseObject;

    private final boolean supports;

    @SuppressWarnings("unchecked")
    public static final Class<? extends liquibase.structure.DatabaseObject>[] SUPPORTS_SNAPSHOT_TYPES =
            Arrays.stream(CMPDatabaseObjectType.values())
                    .filter(CMPDatabaseObjectType::isSupports)
                    .map(CMPDatabaseObjectType::getDatabaseObject)
                    .toArray(Class[]::new);

}
