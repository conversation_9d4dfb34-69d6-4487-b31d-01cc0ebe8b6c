
package com.dc.summer.ext.postgresql.model.data;

import com.dc.summer.model.data.DBDFormatSettings;
import com.dc.summer.model.impl.jdbc.data.handlers.JDBCTemporalAccessorValueHandler;

/**
 * PostgreTemporalAccessorValueHandler.
 */
public class PostgreTemporalAccessorValueHandler extends JDBCTemporalAccessorValueHandler {


    public PostgreTemporalAccessorValueHandler(DBDFormatSettings formatterProfile) {
        super(formatterProfile);
    }


}