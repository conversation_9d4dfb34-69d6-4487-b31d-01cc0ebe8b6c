
package com.dc.summer.ext.postgresql.model;

import com.dc.summer.model.DBPNamedObject;
import com.dc.code.NotNull;

/**
 * PostgreTypeType
 */
public enum PostgreTypeType implements DBPNamedObject
{
    a("Abstract"), // This is non-standard extension (PG Enterprise?)
    b("Base"),
    c("Composite"),
    d("Domain"),
    e("Enum type"),
    m("Multirange"), // Starting with the 14 PG version
    p("Pseudo-type"),
    r("Range");

    private final String desc;

    PostgreTypeType(String desc) {
        this.desc = desc;
    }

    @NotNull
    @Override
    public String getName() {
        return desc;
    }
}
