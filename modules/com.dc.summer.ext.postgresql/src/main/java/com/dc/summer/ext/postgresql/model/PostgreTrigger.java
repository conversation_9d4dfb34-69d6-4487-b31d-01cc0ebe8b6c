
package com.dc.summer.ext.postgresql.model;

import com.dc.summer.Log;
import com.dc.summer.model.DBPEvaluationContext;
import com.dc.summer.model.DBUtils;
import com.dc.summer.model.exec.DBCException;
import com.dc.summer.model.impl.jdbc.JDBCUtils;
import com.dc.summer.model.runtime.DBRProgressMonitor;
import com.dc.summer.model.sql.format.SQLFormatUtils;
import com.dc.summer.model.struct.DBSActionTiming;
import com.dc.summer.model.struct.DBSEntityElement;
import com.dc.code.NotNull;
import com.dc.code.Nullable;
import com.dc.summer.DBException;
import com.dc.summer.ext.postgresql.PostgreUtils;
import com.dc.summer.model.DBPScriptObject;
import com.dc.summer.model.exec.jdbc.JDBCSession;
import com.dc.summer.model.meta.IPropertyValueTransformer;
import com.dc.summer.model.meta.Property;
import com.dc.summer.model.meta.PropertyLength;
import com.dc.summer.model.sql.SQLUtils;
import com.dc.summer.model.struct.DBSObject;
import com.dc.summer.model.struct.DBSObjectState;
import com.dc.summer.model.struct.rdb.DBSManipulationType;
import com.dc.utils.CommonUtils;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * PostgreTrigger
 */
public class PostgreTrigger extends PostgreTriggerBase implements DBSEntityElement
{
    private static final Log log = Log.getLog(PostgreTrigger.class);

    /* Bits within tgtype */
    private static final int TRIGGER_TYPE_ROW        = (1 << 0);
    private static final int TRIGGER_TYPE_BEFORE     = (1 << 1);
    private static final int TRIGGER_TYPE_INSERT     = (1 << 2);
    private static final int TRIGGER_TYPE_DELETE     = (1 << 3);
    private static final int TRIGGER_TYPE_UPDATE     = (1 << 4);
    private static final int TRIGGER_TYPE_TRUNCATE   = (1 << 5);
    private static final int TRIGGER_TYPE_INSTEAD    = (1 << 6);

    private PostgreTableReal table;
    private long objectId;
    private String enabledState;
    private String whenExpression;
    private long functionSchemaId;
    private long functionId;
    private DBSActionTiming actionTiming;
    private DBSManipulationType[] manipulationTypes;
    private PostgreTriggerType type;
    private PostgreTableColumn[] columnRefs;
    protected String description;
    private String body;

    public PostgreTrigger(
        @NotNull DBRProgressMonitor monitor,
        @NotNull PostgreTableReal table,
        @NotNull String triggerName,
        @NotNull ResultSet dbResult) throws DBException {
        super(table.getDatabase(), triggerName, true);
        this.table = table;
        this.objectId = JDBCUtils.safeGetLong(dbResult, "oid");
        this.enabledState = JDBCUtils.safeGetString(dbResult, "tgenabled");
        this.whenExpression = JDBCUtils.safeGetString(dbResult, "tgqual");

        // Get procedure
        this.functionSchemaId = JDBCUtils.safeGetLong(dbResult, "func_schema_id");
        this.functionId = JDBCUtils.safeGetLong(dbResult, "tgfoid");

        // Parse trigger type bits
        int tgType = JDBCUtils.safeGetInt(dbResult, "tgtype");
        if (CommonUtils.isBitSet(tgType, TRIGGER_TYPE_BEFORE)) {
            actionTiming = DBSActionTiming.BEFORE;
        } else if (CommonUtils.isBitSet(tgType, TRIGGER_TYPE_INSTEAD)) {
            actionTiming = DBSActionTiming.INSTEAD;
        } else {
            actionTiming = DBSActionTiming.AFTER;
        }
        List<DBSManipulationType> mt = new ArrayList<>(1);
        if (CommonUtils.isBitSet(tgType, TRIGGER_TYPE_INSERT)) {
            mt.add(DBSManipulationType.INSERT);
        }
        if (CommonUtils.isBitSet(tgType, TRIGGER_TYPE_DELETE)) {
            mt.add(DBSManipulationType.DELETE);
        }
        if (CommonUtils.isBitSet(tgType, TRIGGER_TYPE_UPDATE)) {
            mt.add(DBSManipulationType.UPDATE);
        }
        if (CommonUtils.isBitSet(tgType, TRIGGER_TYPE_TRUNCATE)) {
            mt.add(DBSManipulationType.TRUNCATE);
        }
        this.manipulationTypes = mt.toArray(new DBSManipulationType[0]);

        if (CommonUtils.isBitSet(tgType, TRIGGER_TYPE_ROW)) {
            type = PostgreTriggerType.ROW;
        } else {
            type = PostgreTriggerType.STATEMENT;
        }

        Object attrNumbersObject = JDBCUtils.safeGetObject(dbResult, "tgattr");
        if (attrNumbersObject != null) {
            int[] attrNumbers = PostgreUtils.getIntVector(attrNumbersObject);
            if (attrNumbers != null) {
                int attrCount = attrNumbers.length;
                columnRefs = new PostgreTableColumn[attrCount];
                for (int i = 0; i < attrCount; i++) {
                    int colNumber = attrNumbers[i];
                    final PostgreTableColumn attr = PostgreUtils.getAttributeByNum(getTable().getAttributes(monitor), colNumber);
                    if (attr == null) {
                        log.warn("Bad trigger attribute ref index: " + colNumber);
                        continue;
                    }
                    columnRefs[i] = attr;
                }
            }
        }

        this.description = JDBCUtils.safeGetString(dbResult, "description");
    }

    public PostgreTrigger(@NotNull PostgreTableReal parent, @NotNull String name) {
        super(parent.getDatabase(), name, false);
        this.table = parent;
        this.name = name;
    }

    @NotNull
    @Override
    @Property(viewable = true, order = 1)
    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    @Property(viewable = true, order = 2)
    public DBSActionTiming getActionTiming() {
        return actionTiming;
    }

    @Property(viewable = true, order = 3)
    public DBSManipulationType[] getManipulationTypes() {
        return manipulationTypes;
    }

    @Property(viewable = true, order = 4)
    public PostgreTriggerType getType() {
        return type;
    }

    @Property(viewable = true, order = 5, valueRenderer = ColumnNameTransformer.class)
    public PostgreTableColumn[] getColumnRefs() {
        return columnRefs;
    }

    @Property(viewable = true, order = 6)
    public String getEnabledState() {
        return enabledState;
    }

    @Override
    public PostgreTableBase getTable()
    {
        return table;
    }

    @Override
    @Property(viewable = true, order = 10)
    public long getObjectId() {
        return objectId;
    }

    @Property(viewable = true, order = 11)
    public String getWhenExpression()
    {
        return whenExpression;
    }

    @Override
    @Property(viewable = true, order = 12)
    public PostgreProcedure getFunction(DBRProgressMonitor monitor) throws DBException {
        if (functionId == 0) {
            return null;
        }
        return getDatabase().getProcedure(monitor, functionSchemaId, functionId);
    }

    public void setFunction(PostgreProcedure function) {
        if (function == null){
            this.functionId = 0;
            this.functionSchemaId = 0;
        } else{
            this.functionId = function.getObjectId();
            this.functionSchemaId = function.getSchema().getObjectId();
        }
    }

    @Property(viewable = true, editable = true, updatable = true, length = PropertyLength.MULTILINE, order = 100)
    @Nullable
    @Override
    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    @NotNull
    @Override
    public PostgreTableReal getParentObject()
    {
        return table;
    }

    @NotNull
    @Override
    public PostgreDataSource getDataSource()
    {
        return table.getDataSource();
    }

    @NotNull
    @Override
    public PostgreDatabase getDatabase() {
        return table.getDatabase();
    }

    @Property(hidden = true, editable = true, updatable = true, order = -1)
    public String getObjectDefinitionText(DBRProgressMonitor monitor, Map<String, Object> options) throws DBException {
        StringBuilder ddl = new StringBuilder();
        if (CommonUtils.isEmpty(body)) {
            if (isPersisted()) {
                try (JDBCSession session = DBUtils.openMetaSession(monitor, this, "Read trigger definition")) {
                    body = JDBCUtils.queryString(session,
                            "SELECT @_catalog.@_get_triggerdef(?)".replace("@", getDataSource().getInstancePrefix()),
                            objectId);
                    if (body != null) {
                        body = SQLFormatUtils.formatSQL(getDataSource(), body);
                    }
                } catch (SQLException e) {
                    throw new DBException(e, getDataSource());
                }
            } else {
                body = "CREATE TRIGGER " + DBUtils.getQuotedIdentifier(this)
                    + "\n    AFTER INSERT"
                    + "\n    ON " + table.getFullyQualifiedName(DBPEvaluationContext.DDL)
                    + "\n    FOR EACH ROW"
                    + "\n    EXECUTE PROCEDURE " + getFunction(monitor).getFullyQualifiedName(DBPEvaluationContext.DDL) + "();\n";
            }
        }

        ddl.append(body);
        if (!CommonUtils.isEmpty(getDescription()) && CommonUtils.getOption(options, DBPScriptObject.OPTION_INCLUDE_COMMENTS)) {
            ddl.append(";\n\nCOMMENT ON TRIGGER ").append(DBUtils.getQuotedIdentifier(this))
                .append(" ON ").append(getTable().getFullyQualifiedName(DBPEvaluationContext.DDL))
                .append(" IS ")
                .append(SQLUtils.quoteString(this, getDescription())).append(";");
        }

        return ddl.toString();
    }

    @Override
    public void setObjectDefinitionText(String sourceText) {
        this.body = sourceText;
    }

    public String getBody() {
        return body;
    }

    @Nullable
    @Override
    public DBSObject refreshObject(@NotNull DBRProgressMonitor monitor) throws DBException {
        return getParentObject().getTriggerCache().refreshObject(monitor, getParentObject(), this);
    }

    @NotNull
    @Override
    public String getFullyQualifiedName(DBPEvaluationContext context) {
        return DBUtils.getFullQualifiedName(getDataSource(),
                getParentObject(),
                this);
    }

    @NotNull
    @Override
    public DBSObjectState getObjectState() {
        if ("D".equals(enabledState)) {
            return DBSObjectState.INVALID;
        }
        return DBSObjectState.NORMAL;
    }

    @Override
    public void refreshObjectState(@NotNull DBRProgressMonitor monitor) throws DBCException {
        try (JDBCSession session = DBUtils.openMetaSession(monitor, this, "Refresh triggers state")) {
            try {
                enabledState = JDBCUtils.queryString(session,
                        "SELECT tgenabled FROM @_catalog.@_trigger WHERE oid=?".replace("@", getDataSource().getInstancePrefix()),
                        getObjectId());
            } catch (SQLException e) {
                throw new DBCException(e, session.getExecutionContext());
            }
        }

    }

    @Override
    public String toString() {
        return getFullyQualifiedName(DBPEvaluationContext.UI);
    }

    public static class ColumnNameTransformer implements IPropertyValueTransformer {
        @Override
        public Object transform(Object object, Object value) throws IllegalArgumentException {
            if (value instanceof PostgreTableColumn[]) {
                StringBuilder sb = new StringBuilder();
                for (PostgreTableColumn col : (PostgreTableColumn[])value) {
                    if (sb.length() > 0) sb.append(", ");
                    sb.append(col.getName());
                }
                return sb.toString();
            }
            return value;
        }
    }

}
