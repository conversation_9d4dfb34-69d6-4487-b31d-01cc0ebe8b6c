

package com.dc.summer.ext.postgresql.model.impls.yellowbrick;

import com.dc.summer.Log;
import com.dc.summer.ext.postgresql.model.PostgreTableBase;
import com.dc.summer.model.DBPEvaluationContext;
import com.dc.summer.model.DBUtils;
import com.dc.summer.model.exec.jdbc.JDBCPreparedStatement;
import com.dc.summer.model.exec.jdbc.JDBCResultSet;
import com.dc.summer.model.exec.jdbc.JDBCSession;
import com.dc.summer.model.runtime.DBRProgressMonitor;

/**
 * YellowBrickUtils
 */
public class YellowBrickUtils {

    private static final Log log = Log.getLog(YellowBrickUtils.class);
    
    private static final int UNKNOWN_LENGTH = -1;

    public static String extractTableDDL(DBRProgressMonitor monitor, PostgreTableBase tableBase)
    {
        try (JDBCSession session = DBUtils.openMetaSession(monitor, tableBase, "Load Yellowbrick DDL")) {
            try (JDBCPreparedStatement dbStat = session.prepareStatement("describe " + tableBase.getFullyQualifiedName(DBPEvaluationContext.DDL) + " only ddl")) {
                try (JDBCResultSet resultSet = dbStat.executeQuery()) {
                    StringBuilder sql = new StringBuilder();
                    boolean ddlStarted = false;
                    while (resultSet.next()) {
                        String line = resultSet.getString(1);
                        if (line == null) {
                            continue;
                        }
                        if (!ddlStarted) {
                            if (line.startsWith("CREATE ")) {
                                ddlStarted = true;
                            } else {
                                continue;
                            }
                        }
                        if (sql.length() > 0) sql.append("\n");
                        sql.append(line);
                    }
                    String ddl = sql.toString();
                    if (ddl.endsWith(";")) {
                        ddl = ddl.substring(0, ddl.length() - 1);
                    }
                    return ddl;
                }
            }
        } catch (Exception e) {
            log.debug("Error generating Yellowbrick DDL", e);
            return null;
        }
    }

}
