

package com.dc.summer.ext.oceanbase.mysql;

import com.dc.summer.ext.oceanbase.mysql.model.OceanbaseMySQLDataSource;
import com.dc.summer.model.DBPDataSource;
import com.dc.summer.model.DBPDataSourceContainer;
import com.dc.summer.model.connection.DBPDriver;
import com.dc.summer.model.runtime.DBRProgressMonitor;
import com.dc.code.NotNull;
import com.dc.summer.DBException;
import com.dc.summer.ext.mysql.MySQLDataSourceProvider;
import com.dc.summer.model.connection.DBPConnectionConfiguration;
import com.dc.summer.model.impl.jdbc.JDBCURL;

public class OceanbaseMySQLDataSourceProvider extends MySQLDataSourceProvider {

    @Override
    public String getConnectionURL(DBPDriver driver, DBPConnectionConfiguration connectionInfo) {
        return JDBCURL.generateUrlByTemplate(driver, connectionInfo);
    }

    @NotNull
    @Override
    public DBPDataSource openDataSource(@NotNull DBRProgressMonitor monitor, @NotNull DBPDataSourceContainer container)
            throws DBException {
        return new OceanbaseMySQLDataSource(monitor, container);
    }

}
