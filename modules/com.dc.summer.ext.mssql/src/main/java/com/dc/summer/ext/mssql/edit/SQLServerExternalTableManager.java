
package com.dc.summer.ext.mssql.edit;

import com.dc.summer.DBException;
import com.dc.summer.Log;
import com.dc.summer.ext.mssql.model.SQLServerExternalTable;
import com.dc.summer.ext.mssql.model.SQLServerTableColumn;
import com.dc.summer.model.DBUtils;
import com.dc.summer.model.edit.DBECommandContext;
import com.dc.summer.model.exec.DBCException;
import com.dc.summer.model.impl.sql.edit.SQLObjectEditor;
import com.dc.summer.model.runtime.DBRProgressMonitor;
import com.dc.code.NotNull;
import com.dc.summer.ext.mssql.model.SQLServerSchema;
import com.dc.summer.model.sql.SQLUtils;
import com.dc.summer.model.struct.DBSObject;
import com.dc.utils.CommonUtils;

import java.util.Map;

public class SQLServerExternalTableManager extends SQLServerBaseTableManager<SQLServerExternalTable> {
    private static final Log log = Log.getLog(SQLServerExternalTableManager.class);

    private static final Class<? extends DBSObject>[] CHILD_TYPES = CommonUtils.array(
        SQLServerTableColumn.class);

    @Override
    protected SQLServerExternalTable createDatabaseObject(DBRProgressMonitor monitor, DBECommandContext context, Object container, Object copyFrom, Map<String, Object> options) throws DBException {
        final SQLServerSchema schema = (SQLServerSchema) container;
        final SQLServerExternalTable table = new SQLServerExternalTable(schema);
        setNewObjectName(monitor, schema, table);
        return table;
    }

    @Override
    public boolean canCreateObject(Object container) {
        return false;
    }

    @Override
    protected void appendTableModifiers(DBRProgressMonitor monitor, SQLServerExternalTable table, SQLObjectEditor.NestedObjectCommand tableProps, StringBuilder ddl, boolean alter) {
        try {
            final SQLServerExternalTable.AdditionalInfo info = table.getAdditionalInfo(monitor);
            ddl.append(" WITH (\n\tLOCATION = ").append(SQLUtils.quoteString(table, info.getExternalLocation()));
            ddl.append(",\n\tDATA_SOURCE = ").append(DBUtils.getQuotedIdentifier(table.getDataSource(), info.getExternalDataSource()));
            if (CommonUtils.isNotEmpty(info.getExternalFileFormat())) {
                ddl.append(",\n\tFILE_FORMAT = ").append(SQLUtils.quoteString(table, info.getExternalFileFormat()));
            }
            ddl.append("\n)");
        } catch (DBCException e) {
            log.error("Error retrieving external table info");
        }
    }

    @Override
    public void renameObject(@NotNull DBECommandContext commandContext, @NotNull SQLServerExternalTable object, @NotNull Map<String, Object> options, @NotNull String newName) throws DBException {
        processObjectRename(commandContext, object, options, newName);
    }

    @NotNull
    @Override
    public Class<? extends DBSObject>[] getChildTypes() {
        return CHILD_TYPES;
    }

    @Override
    protected String getCreateTableType(SQLServerExternalTable table) {
        return "EXTERNAL TABLE";
    }
}
