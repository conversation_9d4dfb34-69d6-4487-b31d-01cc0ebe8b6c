

package com.dc.summer.ext.mssql.auth;

import com.dc.summer.DBException;
import com.dc.summer.ext.mssql.SQLServerConstants;
import com.dc.summer.model.DBConstants;
import com.dc.summer.model.DBPDataSource;
import com.dc.summer.model.DBPDataSourceContainer;
import com.dc.summer.model.runtime.DBRProgressMonitor;
import com.dc.code.NotNull;
import com.dc.summer.model.connection.DBPConnectionConfiguration;
import com.dc.summer.model.impl.auth.AuthModelDatabaseNativeCredentials;

import java.util.Properties;

/**
 * SQL Server AD integrated auth model.
 */
public class SQLServerAuthModelADIntegrated extends SQLServerAuthModelAbstract {

    public static final String ID = "sqlserver_ad_integrated";

    @Override
    public boolean isUserNameApplicable() {
        return false;
    }

    @Override
    public boolean isUserPasswordApplicable() {
        return false;
    }

    @Override
    public Object initAuthentication(@NotNull DBRProgressMonitor monitor, @NotNull DBPDataSource dataSource, AuthModelDatabaseNativeCredentials credentials, DBPConnectionConfiguration configuration, @NotNull Properties connProperties) throws DBException {
        connProperties.put(SQLServerConstants.PROP_CONNECTION_AUTHENTICATION, SQLServerConstants.AUTH_ACTIVE_DIRECTORY_INTEGRATED);
        connProperties.remove(DBConstants.DATA_SOURCE_PROPERTY_USER);
        connProperties.remove(DBConstants.DATA_SOURCE_PROPERTY_PASSWORD);
        return credentials;
    }

    @Override
    public void endAuthentication(@NotNull DBPDataSourceContainer dataSource, @NotNull DBPConnectionConfiguration configuration, @NotNull Properties connProperties) {
        super.endAuthentication(dataSource, configuration, connProperties);
    }

}
