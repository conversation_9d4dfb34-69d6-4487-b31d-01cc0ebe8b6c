

package com.dc.summer.ext.mssql.edit;

import com.dc.summer.ext.mssql.SQLServerUtils;
import com.dc.summer.ext.mssql.model.SQLServerExecutionContext;
import com.dc.summer.ext.mssql.model.SQLServerTableBase;
import com.dc.summer.ext.mssql.model.SQLServerTableTrigger;
import com.dc.summer.model.DBUtils;
import com.dc.summer.model.edit.DBECommandContext;
import com.dc.summer.model.edit.DBEPersistAction;
import com.dc.summer.model.exec.DBCExecutionContext;
import com.dc.summer.model.impl.sql.edit.SQLObjectEditor;
import com.dc.summer.model.impl.sql.edit.struct.SQLTriggerManager;
import com.dc.code.Nullable;
import com.dc.summer.model.DBPEvaluationContext;
import com.dc.summer.model.impl.edit.SQLDatabasePersistAction;
import com.dc.summer.model.runtime.DBRProgressMonitor;
import com.dc.summer.model.struct.DBSObject;
import com.dc.summer.model.struct.cache.DBSObjectCache;

import java.util.List;
import java.util.Map;

/**
 * SQLServerTableTriggerManager
 */
public class SQLServerTableTriggerManager extends SQLTriggerManager<SQLServerTableTrigger, SQLServerTableBase> {
    @Nullable
    @Override
    public DBSObjectCache<? extends DBSObject, SQLServerTableTrigger> getObjectsCache(SQLServerTableTrigger object)
    {
        return object.getSchema().getTriggerCache();
    }

    @Override
    protected SQLServerTableTrigger createDatabaseObject(DBRProgressMonitor monitor, DBECommandContext context, Object container,
                                                         Object copyFrom, Map<String, Object> options) {
        SQLServerTableBase table = (SQLServerTableBase) container;
        String newTriggerName = "NewTrigger";
        SQLServerTableTrigger newTrigger = new SQLServerTableTrigger(table, newTriggerName);
        newTrigger.setBody(
            "CREATE OR ALTER TRIGGER " + newTriggerName + " ON " + table.getFullyQualifiedName(DBPEvaluationContext.DDL) + "\n" +
            "AFTER INSERT\n" +
            "AS\n" +
            ";\n"
        );
        return newTrigger;
    }

    protected void createOrReplaceTriggerQuery(DBRProgressMonitor monitor, DBCExecutionContext executionContext, List<DBEPersistAction> actions, SQLServerTableTrigger trigger, boolean create) {
        DBSObject defaultDatabase = ((SQLServerExecutionContext)executionContext).getDefaultCatalog();
        if (defaultDatabase != trigger.getTable().getDatabase()) {
            actions.add(new SQLDatabasePersistAction("Set current database", "USE " + DBUtils.getQuotedIdentifier(trigger.getTable().getDatabase()), false)); //$NON-NLS-2$
        }

        if (create) {
            actions.add(new SQLDatabasePersistAction("Create trigger", trigger.getBody(), true)); //$NON-NLS-2$
        } else {
            actions.add(new SQLDatabasePersistAction("Alter trigger",
                SQLServerUtils.changeCreateToAlterDDL(trigger.getDataSource().getSQLDialect(), trigger.getBody()), true)); //$NON-NLS-2$
        }

        if (defaultDatabase != trigger.getTable().getDatabase()) {
            actions.add(new SQLDatabasePersistAction("Set current database ", "USE " + DBUtils.getQuotedIdentifier(defaultDatabase), false)); //$NON-NLS-2$
        }
    }

    @Override
    protected void addObjectDeleteActions(DBRProgressMonitor monitor, DBCExecutionContext executionContext, List<DBEPersistAction> actions, SQLObjectEditor<SQLServerTableTrigger, SQLServerTableBase>.ObjectDeleteCommand command, Map<String, Object> options) {
        SQLServerTableTrigger trigger = command.getObject();
        DBSObject defaultDatabase = ((SQLServerExecutionContext)executionContext).getDefaultCatalog();
        if (defaultDatabase != trigger.getTable().getDatabase()) {
            actions.add(new SQLDatabasePersistAction("Set current database", "USE " + DBUtils.getQuotedIdentifier(trigger.getTable().getDatabase()), false)); //$NON-NLS-2$
        }

        super.addObjectDeleteActions(monitor, executionContext, actions, command, options);

        if (defaultDatabase != trigger.getTable().getDatabase()) {
            actions.add(new SQLDatabasePersistAction("Set current database ", "USE " + DBUtils.getQuotedIdentifier(defaultDatabase), false)); //$NON-NLS-2$
        }
    }
}
