
package com.dc.summer.ext.mssql.model;

import com.dc.summer.DBException;
import com.dc.summer.ext.mssql.SQLServerUtils;
import com.dc.summer.model.data.DBDAttributeBinding;
import com.dc.summer.model.exec.DBCExecutionContext;
import com.dc.summer.model.exec.DBExecUtils;
import com.dc.summer.model.exec.jdbc.JDBCDatabaseMetaData;
import com.dc.summer.model.impl.jdbc.JDBCDataSourceInfo;
import com.dc.summer.model.runtime.DBRProgressMonitor;
import com.dc.summer.model.sql.SqlFieldData;
import com.dc.summer.model.sql.SQLQueryType;
import com.dc.summer.model.struct.DBSObjectType;
import com.dc.utils.CommonUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * SQLServerDataSourceInfo
 */
@Slf4j
class SQLServerDataSourceInfo extends JDBCDataSourceInfo {

    private SQLServerDataSource dataSource;
    private boolean isSybase;

    public SQLServerDataSourceInfo(SQLServerDataSource dataSource, JDBCDatabaseMetaData metaData) {
        super(metaData);
        this.dataSource = dataSource;
        this.isSybase = !SQLServerUtils.isDriverSqlServer(dataSource.getContainer().getDriver());
    }

    @Override
    public boolean supportsResultSetLimit() {
        return true;
    }

    @Override
    public boolean supportsMultipleResults() {
        return true;
    }

    @Override
    public boolean isMultipleResultsFetchBroken() {
        return isSybase;
    }

    @Override
    public String getDatabaseProductVersion() {
        String serverVersion = dataSource.getServerVersion();
        return CommonUtils.isEmpty(serverVersion) ? super.getDatabaseProductVersion() : super.getDatabaseProductVersion() + "\n" + serverVersion;
    }

    @Override
    public DBSObjectType[] getSupportedObjectTypes() {
        return SQLServerObjectType.values();
    }

    @Override
    public boolean needsTableMetaForColumnResolution() {
        return false;
    }

    

    @Override
    public boolean supportsDDLAutoCommit() {
        return false;
    }

    @Override
    public String getPrimaryKeySql(String schemaName, String tableName) {

        return "SELECT a.name as column_name, \n" +
                "       (case when (SELECT count(*) FROM [" + schemaName + "].sys.sysobjects \n" +
                "             WHERE (name in (SELECT name FROM [" + schemaName + "].sys.sysindexes \n" +
                "                WHERE (id = a.id) AND (indid in \n" +
                "                    (SELECT indid FROM [" + schemaName + "].sys.sysindexkeys  \n" +
                "                     WHERE (id = a.id) AND (colid in  \n" +
                "                        (SELECT colid FROM [" + schemaName + "].sys.syscolumns WHERE (id = a.id) AND (name = a.name))))))) \n" +
                "             AND (xtype = 'PK'))>0 \n" +
                "       then 1 else 0 end) as is_primary_key \n" +
                "FROM  [" + schemaName + "].sys.syscolumns a \n" +
                "inner join [" + schemaName + "].sys.sysobjects d on a.id=d.id and d.xtype='U' and d.name<>'dtproperties' \n" +
                "inner join [" + schemaName + "].sys.objects h on d.id = h.object_id \n" +
                "inner join [" + schemaName + "].sys.schemas s on h.schema_id = s.schema_id \n" +
                "where  s.name+'.'+d.name in ('" + tableName + "')";
    }

    @Override
    public List<String> getPrimaryKeyColumns(List<Map<String, Object>> list) {

        List<String> columns = new ArrayList<>();

        for (Map<String, Object> map : list) {
            if (map.get("is_primary_key") != null && map.get("is_primary_key").equals(1)) {
                if (map.get("COLUMN_NAME") != null) {
                    columns.add(map.get("COLUMN_NAME").toString());
                } else if (map.get("column_name") != null) {
                    columns.add(map.get("column_name").toString());
                }
            } else if (map.get("IS_PRIMARY_KEY") != null && map.get("IS_PRIMARY_KEY").equals(1)) {
                if (map.get("COLUMN_NAME") != null) {
                    columns.add(map.get("COLUMN_NAME").toString());
                } else if (map.get("column_name") != null) {
                    columns.add(map.get("column_name").toString());
                }
            }
        }

        return columns;
    }

    @Override
    public String getTableRealNameSql(String schemaName, String tableName) {

        String framework = "";
        String table = "";

        String[] split = tableName.split("\\.");
        if (split.length == 2) {
            framework = split[0];
            table = split[1];
        } else if (split.length == 1) {
            table = split[0];
        }

        return "select b.name as tabname,a.name as schname from [" + schemaName + "].sys.schemas a,[" + schemaName + "].sys.tables b " +
                "where a.schema_id = b.schema_id and upper(a.name)=upper('" + framework + "') " +
                "and upper(b.name)=upper('" + table + "');";
    }

    @Override
    public String getTableRealName(List<Map<String, Object>> list) {

        String realName = "";

        for (Map<String, Object> map : list) {
            if (map.get("tabname") != null && map.get("schname") != null) {
                realName = map.get("schname") + "." + map.get("tabname"); // 需要真实framework: schname
                break;
            } else if (map.get("TABNAME") != null && map.get("SCHNAME") != null) {
                realName = map.get("SCHNAME") + "." + map.get("TABNAME");
                break;
            }
        }

        return realName;
    }

    @Override
    public List<String> getBigDataColumnType() {
        return Arrays.asList(
                "TEXT", "NTEXT", "IMAGE", "XML", "VARBINARY", "VARCHAR(MAX)", "NVARCHAR(MAX)", "BINARY"
        );
    }

    @Override
    public String getTableColumnSql(String schemaName, String tableName) {
        boolean queryComment = isQueryComment();
        String query = "SELECT\n" +
                "        a.colorder ordinal_position,\n" +
                "        '" + schemaName + "' as schema_name,\n" +
                "        s.name as dbo_name,\n" +
                "        d.name table_name,\n" +
                "        a.name column_name,\n" +
                "        b.name data_type,\n" +
                "        b.name column_type,\n" +
                "        (case when a.isnullable=1 then 1 else 0 end) nullable,\n" +
                "        isnull(e.text,'') data_default,\n";
        if (queryComment) {
            query += "        isnull(g.[value], '') AS comments,\n";
        }
        query += "        a.xtype column_key,\n" +
                "        (\n" +
                "        case\n" +
                "        when (\n" +
                "        SELECT\n" +
                "        count(*)\n" +
                "        FROM\n" +
                "        [" + schemaName + "].sys.sysobjects\n" +
                "        WHERE\n" +
                "        (name in (\n" +
                "        SELECT\n" +
                "        name\n" +
                "        FROM\n" +
                "        [" + schemaName + "].sys.sysindexes\n" +
                "        WHERE\n" +
                "        (id = a.id) AND (indid in\n" +
                "        (SELECT\n" +
                "        indid\n" +
                "        FROM\n" +
                "        [" + schemaName + "].sys.sysindexkeys\n" +
                "        WHERE\n" +
                "        (id = a.id) AND (colid in\n" +
                "        (SELECT\n" +
                "        colid\n" +
                "        FROM\n" +
                "        [" + schemaName + "].sys.syscolumns\n" +
                "        WHERE\n" +
                "        (id = a.id) AND (name = a.name)\n" +
                "        ))\n" +
                "        ))\n" +
                "        ))\n" +
                "        AND (xtype = 'PK')\n" +
                "        ) > 0 then 1\n" +
                "        else 0\n" +
                "        end) is_primary_key,\n" +
                "        (CASE WHEN i.object_id > 0 THEN 1 ELSE 0 END ) is_increment,\n" +
                "        a.length as data_length,\n" +
                "        a.prec as prec,\n" +
                "        isc.character_maximum_length as char_max_length,\n" +
                "        isnull(a.scale,0) as precision,\n" +
                "        ic.is_identity as is_identity,\n" +
                "        ic.seed_value as identity_start,\n" +
                "        ic.increment_value as identity_step\n" +
                "        FROM\n" +
                "        [" + schemaName + "].sys.syscolumns a\n" +
                "        left join\n" +
                "        [" + schemaName + "].sys.types b on a.xusertype=b.user_type_id\n" +
                "        inner join\n" +
                "        [" + schemaName + "].sys.sysobjects d on a.id=d.id and d.xtype='U' and d.name != 'dtproperties'\n" +
                "        left join\n" +
                "        [" + schemaName + "].sys.syscomments e on a.cdefault=e.id\n";
        if (queryComment) {
            query += "        left join\n" +
                    "        [" + schemaName + "].sys.extended_properties g on a.id=g.major_id AND a.colid=g.minor_id\n" +
                    "        and g.class = 1 and g.name = 'MS_Description'  ";
        }
        query += "        left join    [" + schemaName + "].sys.extended_properties f on d.id=f.class and f.minor_id=0  left join\n" +
                "        [" + schemaName + "].sys.identity_columns ic on ic.object_id = a.id and a.colid = ic.column_id\n" +
                "        inner join\n" +
                "        [" + schemaName + "].sys.objects h on d.id = h.object_id\n" +
                "        inner join\n" +
                "        [" + schemaName + "].sys.schemas s on h.schema_id = s.schema_id\n" +
                "        left join\n" +
                "        [" + schemaName + "].sys.identity_columns i on i.object_id=h.object_id and i.column_id= a.colid\n" +
                "        left join\n" +
                "        [" + schemaName + "].INFORMATION_SCHEMA.COLUMNS isc on isc.table_schema = s.name\n" +
                "        and isc.table_name = d.name and isc.column_name = a.name\n" +
                "        where\n" +
                "        1=1  and s.name+'.'+d.name = '" + tableName + "' order by ordinal_position ";
        return query;
    }

    @Override
    public boolean supportsTableColumnSQL() {
        return true;
    }

    @Override
    public List<SQLQueryType> getBackupTypes() {
        return Arrays.asList(
                SQLQueryType.COMMIT,
                SQLQueryType.ROLLBACK);
    }

    @Override
    public List<SQLQueryType> getDmlTypes() {
        return Arrays.asList(
                SQLQueryType.UPDATE,
                SQLQueryType.DELETE,
                SQLQueryType.INSERT,
                SQLQueryType.TRUNCATE);
    }

    @Override
    public String generateInsertSql(String schemaName, String tableName, List<SqlFieldData> list, String content) {

        if (list == null) {
            return "";
        }

        List<String> fields = list.stream().map(SqlFieldData::getFieldName).collect(Collectors.toList());
        List<String> data = new ArrayList<>();
        for (SqlFieldData item : list) {
            if (null != item.getFieldValue() && StringUtils.isNotBlank(item.getFieldValue().toString())) {
                if (Arrays.asList("VARCHAR", "NVARCHAR", "TEXT", "VARCHAR2").contains(item.getFieldType().toUpperCase(Locale.ROOT))) {
                    String varcharData = item.getFieldValue().toString().contains("'") ? item.getFieldValue().toString().replace("'", "''") : item.getFieldValue().toString();
                    data.add(String.format("N'%s'", varcharData));
                } else {
                    data.add(String.format("'%s'", item.getFieldValue()));
                }
            } else {
                data.add(String.format("%s", "NULL"));
            }
        }
        String columns = StringUtils.join(fields, "\",\"");
        String values = StringUtils.join(data, ",");

        String dboName = null;
        String newTableName;

        if (tableName.contains(".")) {
            String[] names = tableName.split("\\.");
            dboName = names[0];
            newTableName = names[1];
        } else {
            newTableName = tableName;
        }
        if (StringUtils.isNotBlank(dboName)) {
            newTableName = String.format("[%s].[%s]", dboName, newTableName);
        } else {
            newTableName = String.format("[%s]", newTableName);
        }

        if (null != schemaName) {
            return String.format("INSERT INTO [%s].%s (%s) VALUES (%s)", schemaName, newTableName, "\"" + columns + "\"", values);
        }
        return String.format("INSERT INTO %s (%s) VALUES (%s)", newTableName, "\"" + columns + "\"", values);
    }

    @Override
    public String generateInsertSqlBatch(String schemaName, String tableName, List<List<SqlFieldData>> list, List<String> contents) {
        if (CollectionUtils.isEmpty(list)) {
            return "";
        }
        String fields = list.get(0).stream().map(SqlFieldData::getFieldName).collect(Collectors.joining("\",\""));

        String values = list.stream().map(sqlFieldDataList -> {
            List<String> data = new ArrayList<>();
            for (SqlFieldData item : sqlFieldDataList) {
                if (null != item.getFieldValue() && StringUtils.isNotBlank(item.getFieldValue().toString())) {
                    if (Arrays.asList("VARCHAR", "NVARCHAR", "TEXT", "VARCHAR2").contains(item.getFieldType().toUpperCase(Locale.ROOT))) {
                        String varcharData = item.getFieldValue().toString().contains("'") ? item.getFieldValue().toString().replace("'", "''") : item.getFieldValue().toString();
                        data.add(String.format("N'%s'", varcharData));
                    } else {
                        data.add(String.format("'%s'", item.getFieldValue()));
                    }
                } else {
                    data.add(String.format("%s", "NULL"));
                }
            }
            return String.format("(%s)", StringUtils.join(data, ","));
        }).collect(Collectors.joining(","));

        String dboName = null;
        String newTableName;

        if (tableName.contains(".")) {
            String[] names = tableName.split("\\.");
            dboName = names[0];
            newTableName = names[1];
        } else {
            newTableName = tableName;
        }
        if (StringUtils.isNotBlank(dboName)) {
            newTableName = String.format("[%s].[%s]", dboName, newTableName);
        } else {
            newTableName = String.format("[%s]", newTableName);
        }

        if (null != schemaName) {
            return String.format("INSERT INTO [%s].%s (%s) VALUES %s", schemaName, newTableName, "\"" + fields + "\"", values);
        }
        return String.format("INSERT INTO %s (%s) VALUES %s", newTableName, "\"" + fields + "\"", values);
    }

    @Override
    public String generateTruncateSql(String schemaName, String tableName) {
        String dboName = null;
        String newTableName = null;
        if (tableName.contains(".")) {
            String[] names = tableName.split("\\.");
            dboName = names[0];
            newTableName = names[1];
        } else {
            newTableName = tableName;
        }
        if (StringUtils.isNotBlank(dboName)) {
            newTableName = String.format("[%s].[%s]", dboName, newTableName);
        } else {
            newTableName = String.format("[%s]", newTableName);
        }
        if (StringUtils.isNotBlank(schemaName)) {
            return String.format("TRUNCATE TABLE [%s].%s", schemaName, newTableName);
        } else {
            return String.format("TRUNCATE TABLE %s", newTableName);
        }
    }

    @Override
    public List<Map<String, Object>> getSchemasInfo(String userName, DBRProgressMonitor monitor, DBCExecutionContext context, Function<String, DBCExecutionContext> dbNameContextFunc) throws DBException {

        List<Map<String, Object>> list = DBExecUtils.executeQuery(monitor, context, "get schema list",
                "select name,collation_name from master.sys.databases");

        ArrayList<Map<String, Object>> returnList = new ArrayList<>();
        for (Map<String, Object> map : list) {
            Map<String, Object> returnMap = new LinkedHashMap<>();
            returnMap.put("addLabel", "dc_sqlserver_db_schema");
            returnMap.put("username", map.get("name"));
            returnMap.put("charset", map.get("collation_name") != null ? map.get("collation_name") : "");

            if (Arrays.asList("master", "tempdb", "model", "msdb", "resource")
                    .contains(((String) map.get("name")).toLowerCase())) {
                returnMap.put("is_sys", 1);
            } else {
                returnMap.put("is_sys", 0);
            }

            returnMap.put("count", 0L);
            returnMap.put("def_dbo_name", "dbo");

            try {
                List<Map<String, Object>> listCount = DBExecUtils.executeQuery(monitor, context, "get schema count",
                        "select count(1) as count from [" + map.get("name") + "].sys.tables");
                if (null != listCount && listCount.size() > 0) {
                    returnMap.put("count", Long.parseLong(listCount.get(0).get("count").toString()));
                }

                //获取默认dbo
                List<Map<String, Object>> listDefdbo = DBExecUtils.executeQuery(monitor, context, "get schema defdbo",
                        "[" + map.get("name") + "].sys.sp_helpuser");
                for (Map<String, Object> mapDefdbo : listDefdbo) {
                    if (userName.equals(mapDefdbo.get("LoginName"))) {
                        returnMap.put("def_dbo_name", mapDefdbo.get("DefSchemaName"));
                        break;
                    }
                }

            } catch (Exception e) {
                log.error("getSchemasInfo error : ", e);
            }

            returnList.add(returnMap);
        }

        return returnList;
    }

    @Override
    public boolean showInteger(DBDAttributeBinding column) {
        if (List.of("REAL", "FLOAT", "MONEY", "SMALLMONEY").contains(column.getTypeName().toUpperCase(Locale.ROOT))) {
            return false;
        }
        return super.showInteger(column);
    }

    @Override
    public boolean catalogIsSchema() {
        return true;
    }
}
