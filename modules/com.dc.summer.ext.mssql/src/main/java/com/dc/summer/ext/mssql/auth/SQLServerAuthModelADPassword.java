

package com.dc.summer.ext.mssql.auth;

import com.dc.summer.model.DBPDataSource;
import com.dc.code.NotNull;
import com.dc.summer.DBException;
import com.dc.summer.ext.mssql.SQLServerConstants;
import com.dc.summer.model.DBPDataSourceContainer;
import com.dc.summer.model.connection.DBPConnectionConfiguration;
import com.dc.summer.model.impl.auth.AuthModelDatabaseNativeCredentials;
import com.dc.summer.model.runtime.DBRProgressMonitor;
import com.dc.utils.CommonUtils;

import java.util.Properties;

/**
 * SQL Server AD password based auth model.
 */
public class SQLServerAuthModelADPassword extends SQLServerAuthModelAbstract {

    public static final String ID = "sqlserver_ad_password";

    @Override
    public Object initAuthentication(@NotNull DBRProgressMonitor monitor, @NotNull DBPDataSource dataSource, AuthModelDatabaseNativeCredentials credentials, DBPConnectionConfiguration configuration, @NotNull Properties connProperties) throws DBException {
        connProperties.put(SQLServerConstants.PROP_CONNECTION_INTEGRATED_SECURITY, String.valueOf(false));
        connProperties.put(SQLServerConstants.PROP_CONNECTION_AUTHENTICATION, SQLServerConstants.AUTH_ACTIVE_DIRECTORY_PASSWORD);

        if (!CommonUtils.isEmpty(configuration.getUserName())) {
            connProperties.put("UserName", configuration.getUserName());
        }
        if (!CommonUtils.isEmpty(configuration.getUserPassword())) {
            connProperties.put("Password", configuration.getUserPassword());
        }
        return credentials;
    }

    @Override
    public void endAuthentication(@NotNull DBPDataSourceContainer dataSource, @NotNull DBPConnectionConfiguration configuration, @NotNull Properties connProperties) {
        super.endAuthentication(dataSource, configuration, connProperties);
    }

}
