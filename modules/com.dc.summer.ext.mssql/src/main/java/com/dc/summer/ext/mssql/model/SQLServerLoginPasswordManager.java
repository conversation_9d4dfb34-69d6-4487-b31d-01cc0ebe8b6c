
package com.dc.summer.ext.mssql.model;

import com.dc.summer.DBException;
import com.dc.summer.model.DBUtils;
import com.dc.summer.model.access.DBAUserPasswordManager;
import com.dc.summer.model.exec.DBCException;
import com.dc.summer.model.exec.jdbc.JDBCSession;
import com.dc.summer.model.impl.jdbc.JDBCUtils;
import com.dc.summer.model.runtime.DBRProgressMonitor;
import com.dc.summer.model.sql.SQLUtils;
import com.dc.utils.CommonUtils;

import java.sql.SQLException;

public class SQLServerLoginPasswordManager implements DBAUserPasswordManager {

    private final SQLServerDataSource dataSource;

    SQLServerLoginPasswordManager(SQLServerDataSource dataSource) {
        this.dataSource = dataSource;
    }

    @Override
    public void changeUserPassword(DBRProgressMonitor monitor, String loginName, String newPassword, String oldPassword) throws DBException {
        try (JDBCSession session = DBUtils.openMetaSession(monitor, dataSource, "Change user login password")) {
            session.enableLogging(false);
            JDBCUtils.executeSQL(session, "ALTER LOGIN " + DBUtils.getQuotedIdentifier(dataSource, loginName) + " WITH PASSWORD =" + SQLUtils.quoteString(dataSource, CommonUtils.notEmpty(newPassword)) +
                " OLD_PASSWORD =" + SQLUtils.quoteString(dataSource, CommonUtils.notEmpty(oldPassword)));
        } catch (SQLException e) {
            throw new DBCException("Error changing user password", e);
        }
    }
}
