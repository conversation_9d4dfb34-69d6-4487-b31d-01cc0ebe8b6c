
package com.dc.summer.ext.bigquery.model;

import com.dc.summer.model.impl.sql.QueryTransformerLimit;
import com.dc.code.NotNull;
import com.dc.code.Nullable;
import com.dc.summer.ext.generic.model.meta.GenericMetaModel;
import com.dc.summer.model.DBPErrorAssistant;
import com.dc.summer.model.exec.DBCQueryTransformProvider;
import com.dc.summer.model.exec.DBCQueryTransformType;
import com.dc.summer.model.exec.DBCQueryTransformer;
import com.dc.utils.CommonUtils;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * BigQuery meta model
 */
public class BigQueryMetaModel extends GenericMetaModel implements DBCQueryTransformProvider {

    private Pattern ERROR_POSITION_PATTERN = Pattern.compile(" at \\[([0-9]+)\\:([0-9]+)\\]");

    public BigQueryMetaModel() {
    }

    @Nullable
    @Override
    public DBCQueryTransformer createQueryTransformer(@NotNull DBCQueryTransformType type) {
        if (type == DBCQueryTransformType.RESULT_SET_LIMIT) {
            return new QueryTransformerLimit(false);
        }
        return null;
    }

    @Override
    public DBPErrorAssistant.ErrorPosition getErrorPosition(Throwable error) {
        String message = error.getMessage();
        if (!CommonUtils.isEmpty(message)) {
            Matcher matcher = ERROR_POSITION_PATTERN.matcher(message);
            if (matcher.find()) {
                DBPErrorAssistant.ErrorPosition pos = new DBPErrorAssistant.ErrorPosition();
                pos.line = Integer.parseInt(matcher.group(1)) - 1;
                pos.position = Integer.parseInt(matcher.group(2)) - 1;
                return pos;
            }
        }
        return null;
    }

}
