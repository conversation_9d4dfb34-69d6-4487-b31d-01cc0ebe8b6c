package com.dc.summer.exec.monitor.data;

import com.dc.summer.model.DBPDataSourcePermission;
import lombok.Data;

import java.util.Date;
import java.util.List;


@Data
public class DataSourceStatResult {

    private String containerId;

    private String name;

    private String username;

    private String url;

    private String dbType;

    private String driverClassName;

    private boolean initializeConnection;

    private long handleCount;

    private boolean autoCloseTransactions;

    private boolean defaultAutoCommit;

    private boolean connectionReadOnly;

    private boolean forceUseSingleConnection;

    private boolean temporary;

    private boolean hidden;

    private boolean connected;

    private Integer defaultTransactionsIsolation;

    private int activeTransactionsIsolation;

    private List<DBPDataSourcePermission> modifyPermission;

    private Date connectTime;

    private boolean accessCheckRequired;

    private boolean manageable;

    private boolean template;

    private String driverId;

    private int keepAliveInterval;

    private int closeIdleInterval;

    private String databaseName;

    private long numberOfTransactionStartups;

    private long[] transactionTimePart = new long[7];

    private long numberOfConnectionsInThePool;

    private long peakNumberOfConnectionsInThePool;

    private Date peakTimeOfConnectionsInThePool;

    private long numberOfActiveConnections;

    private long peakNumberOfActiveConnections;

    private Date PeakTimeOfActiveConnections;

    private long connectOpenCount;

    private long connectCloseCount;

    private long connectErrorCount;

    private long executeCount;

    private long executeQueryCount;

    private long executeUpdateCount;

    private long executeBatchCount;

    private long errorCount;

    private long commitCount;

    private long rollbackCount;

    private long[] connectHoldTimePart = new long[8];

}
