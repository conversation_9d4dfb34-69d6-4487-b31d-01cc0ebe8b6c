package com.dc.summer.exec.model.type;


import java.util.Arrays;

public enum RecordSignType {

    EXECUTE_START(),
    EXECUTE_OVER(),
    EXECUTE_ERROR(),
    WAIT_COMMIT(),
    NONE_COMMIT(),
    EXECUTE_COMMIT(),
    EXECUTE_ROLLBACK(),
    READ_START(),
    READ_OVER(),
    RENEW_START(),
    RENEW_OVER(),
    CONTEXT_ADD(),
    CONTEXT_GET(),
    CONTEXT_CLOSE(),
    CONTEXT_ERROR(),
    CONTEXT_OVER(),
    CONTAINER_HANDLE(),

    ;

    RecordSignType() {
    }

    public static RecordSignType of(String name) {
        return Arrays.stream(RecordSignType.values()).filter(type -> type.name().equals(name)).findFirst().orElse(null);
    }
}
