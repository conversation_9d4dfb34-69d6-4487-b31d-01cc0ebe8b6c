@startuml execute-export简化时序图
!theme plain
title execute-export接口核心时序图

skinparam backgroundColor #FFFFFF
skinparam sequenceArrowThickness 2
skinparam roundcorner 15

actor "用户" as User
participant "ExecuteController" as Controller
participant "ExecuteService" as Service
participant "SummerThreadScheduler" as Scheduler
participant "WebDataTransfer" as Transfer
participant "DataProcessor" as Processor
participant "ResultService" as ResultSvc
database "Redis" as Redis
participant "FileStorage" as Storage

== 请求阶段 ==
User -> Controller: POST /execute/execute-export\n(SqlExportMessage)
Controller -> Service: asyncSqlExport(message)
Service -> Service: 创建异步任务WebAsyncTaskInfo
Service --> Controller: 返回任务信息
Controller -> Scheduler: exec(EXECUTE_EXPORT, taskInfo)
Controller --> User: 返回taskId

== 异步执行阶段 ==
Scheduler -> Scheduler: 调度到ExportThread
activate Scheduler

Scheduler -> Service: 执行导出任务
activate Service

Service -> Service: 获取DataTransferProcessor
Service -> Service: 获取SQL结果集数据

Service -> Transfer: exportDataByContext()
activate Transfer

== 数据处理阶段 ==
Transfer -> Transfer: 初始化导出环境
Transfer -> Processor: 创建导出器实例
activate Processor

loop 处理每个结果集
    Transfer -> Processor: exportHeader()
    
    loop 逐行处理数据
        Transfer -> Processor: exportRow(data)
        Processor -> Processor: 数据格式转换
        alt 需要脱敏
            Processor -> Processor: 数据脱敏处理
        end
        Processor -> Processor: 写入文件
    end
    
    Transfer -> Processor: exportFooter()
end

Processor --> Transfer: 文件生成完成
deactivate Processor

== 文件处理阶段 ==
alt 多文件或需要加密
    Transfer -> Transfer: 创建ZIP压缩包
end

Transfer -> Storage: 上传文件到存储系统
Storage --> Transfer: 返回文件访问路径

Transfer --> Service: 返回导出结果
deactivate Transfer

== 结果保存阶段 ==
Service -> ResultSvc: saveStringValue(taskId, result)
ResultSvc -> Redis: 缓存任务结果
Redis --> ResultSvc: 保存成功

Service -> Service: 更新任务状态为完成
Service --> Scheduler: 任务执行完成
deactivate Service
deactivate Scheduler

== 结果查询阶段 ==
User -> Controller: POST /execute/task-result\n(taskId)
Controller -> ResultSvc: 查询任务结果
ResultSvc -> Redis: 获取缓存结果
Redis --> ResultSvc: 返回结果数据
ResultSvc --> Controller: WebSQLExecuteInfo
Controller --> User: 返回文件下载路径

@enduml
