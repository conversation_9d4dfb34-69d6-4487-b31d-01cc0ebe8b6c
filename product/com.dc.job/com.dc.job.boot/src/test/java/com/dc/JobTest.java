package com.dc;


import com.dc.executor.job.handler.StructCompareJobHandler;
import com.dc.executor.model.StructCompareJobParam;
import com.dc.repository.mysql.mapper.DcScmpParamMapper;
import com.dc.repository.mysql.model.DcScmpParam;
import com.dc.springboot.core.model.database.*;
import com.dc.test.springboot.BaseSpringBootTest;
import org.apache.commons.collections4.CollectionUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;
import java.util.List;


@SpringBootTest(classes = JobApplication.class, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
class JobTest extends BaseSpringBootTest {

    @Resource
    private DcScmpParamMapper dcScmpParamMapper;

    @BeforeEach
    public void init() throws Exception {

        setupMockMvc();
        setupDataBase();

    }

    @Test
    void mockStructCompareTable() {

        int dcJobId = 2;

        List<DcScmpParam> paramByDcJobId = dcScmpParamMapper.getParamByDcJobId(dcJobId);

        if (CollectionUtils.isEmpty(paramByDcJobId)) {
            DcScmpParam dcScmpParam = new DcScmpParam();
            dcScmpParam.setDcJobId(dcJobId);
            dcScmpParam.setCompareScope(2);
            dcScmpParam.setObjectType(StructCompareObjectType.TABLE.getCode());
            dcScmpParam.setSourceSchemaId("c07c5fc9d219d2fbc657c2e0ed21c482");
            dcScmpParam.setTargetSchemaId("f4fc35cf4bc58c08b59694529b6f8f35");
            dcScmpParam.setSourceObjectName("sales");
            dcScmpParam.setTargetObjectName("db");
            dcScmpParamMapper.insert(dcScmpParam);
        }

        StructCompareJobParam param = new StructCompareJobParam();
        param.setDcJobId(dcJobId);
        param.setConfigTypes(List.of(StructCompareConfigType.TABLE, StructCompareConfigType.COLUMNS, StructCompareConfigType.PRIMARY_KEY, StructCompareConfigType.FOREIGN_KEY, StructCompareConfigType.UNIQUE_CONSTRAINT, StructCompareConfigType.INDEX));
        param.setSourceConnectId("2623171bc34c144f742750ea0f977c8b");
        param.setTargetConnectId("2623171bc34c144f742750ea0f977c8b");

        System.out.println(gson.toJson(param));
    }

    @Test
    void mockStructCompareSchema() {

        int dcJobId = 1;

        List<DcScmpParam> paramByDcJobId = dcScmpParamMapper.getParamByDcJobId(dcJobId);

        if (CollectionUtils.isEmpty(paramByDcJobId)) {
            DcScmpParam dcScmpParam = new DcScmpParam();
            dcScmpParam.setDcJobId(dcJobId);
            dcScmpParam.setCompareScope(1);
            dcScmpParam.setSourceSchemaId("c07c5fc9d219d2fbc657c2e0ed21c482");
            dcScmpParam.setTargetSchemaId("f4fc35cf4bc58c08b59694529b6f8f35");
            dcScmpParamMapper.insert(dcScmpParam);
        }

        StructCompareJobParam param = new StructCompareJobParam();
        param.setDcJobId(dcJobId);
        param.setConfigTypes(List.of(StructCompareConfigType.TABLE, StructCompareConfigType.COLUMNS, StructCompareConfigType.PRIMARY_KEY, StructCompareConfigType.FOREIGN_KEY, StructCompareConfigType.UNIQUE_CONSTRAINT, StructCompareConfigType.INDEX));
        param.setSourceConnectId("2623171bc34c144f742750ea0f977c8b");
        param.setTargetConnectId("2623171bc34c144f742750ea0f977c8b");

        System.out.println(gson.toJson(param));

    }

}
