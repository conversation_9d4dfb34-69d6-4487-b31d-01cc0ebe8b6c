package com.dc.summer.service.transfer.request;

import com.dc.springboot.core.model.chain.ChainBuilder;
import com.dc.springboot.core.model.database.TokenConfig;
import com.dc.springboot.core.model.result.WebSQLQueryResultSet;
import com.dc.springboot.core.model.result.WebSQLTransferResult;
import com.dc.springboot.core.model.type.OriginType;
import com.dc.summer.data.transfer.registry.DataTransferProcessorDescriptor;
import com.dc.summer.model.export.context.ExportContext;
import com.dc.summer.model.runtime.DBRProgressMonitor;
import com.dc.summer.service.transfer.WebDataTransferName;

import java.util.List;
import java.util.function.BiFunction;
import java.util.function.Function;

/**
 * 导出数据请求类
 * 使用建造器模式封装 exportDataByContext 方法的复杂参数
 * 
 * <AUTHOR>
 */
public class ExportDataRequest {
    
    // 核心上下文信息
    private final ExportContext exportContext;
    
    private final DBRProgressMonitor monitor;
    private final DataTransferProcessorDescriptor processor;
    private final List<WebSQLQueryResultSet> resultSets;
    private final String userId;
    private final Function<List<WebSQLQueryResultSet>, ChainBuilder<WebSQLTransferResult>> streamFunction;
    private final WebDataTransferName transferZipName;
    private final WebDataTransferName transferFileName;
    private final boolean needToZipAndUpload;
    private final boolean isSingle;
    private final boolean deleteResultInfo;
    private final String customFileName;
    private final TokenConfig tokenConfig;
    private final OriginType originType;
    
    private ExportDataRequest(Builder builder) {
        this.exportContext = builder.exportContext;
        this.monitor = builder.monitor;
        this.processor = builder.processor;
        this.resultSets = builder.resultSets;
        this.userId = builder.userId;
        this.streamFunction = builder.streamFunction;
        this.transferZipName = builder.transferZipName;
        this.transferFileName = builder.transferFileName;
        this.needToZipAndUpload = builder.needToZipAndUpload;
        this.isSingle = builder.isSingle;
        this.deleteResultInfo = builder.deleteResultInfo;
        this.customFileName = builder.customFileName;
        this.tokenConfig = builder.tokenConfig;
        this.originType = builder.originType;
    }
    
    // Getters
    public ExportContext getExportContext() { return exportContext; }
    public DBRProgressMonitor getMonitor() { return monitor; }
    public DataTransferProcessorDescriptor getProcessor() { return processor; }
    public List<WebSQLQueryResultSet> getResultSets() { return resultSets; }
    public String getUserId() { return userId; }
    public Function<List<WebSQLQueryResultSet>, ChainBuilder<WebSQLTransferResult>> getStreamFunction() { return streamFunction; }
    public WebDataTransferName getTransferZipName() { return transferZipName; }
    public WebDataTransferName getTransferFileName() { return transferFileName; }
    public boolean isNeedToZipAndUpload() { return needToZipAndUpload; }
    public boolean isSingle() { return isSingle; }
    public boolean isDeleteResultInfo() { return deleteResultInfo; }
    public String getCustomFileName() { return customFileName; }
    public TokenConfig getTokenConfig() { return tokenConfig; }
    public OriginType getOriginType() { return originType; }
    
    /**
     * 建造器类
     */
    public static class Builder {
        // 必需参数
        private ExportContext exportContext;
        private DataTransferProcessorDescriptor processor;
        private List<WebSQLQueryResultSet> resultSets;
        private String userId;
        private Function<List<WebSQLQueryResultSet>, ChainBuilder<WebSQLTransferResult>> streamFunction;
        private WebDataTransferName transferZipName;
        private WebDataTransferName transferFileName;
        
        // 可选参数及默认值
        private DBRProgressMonitor monitor;
        private boolean needToZipAndUpload = true;
        private boolean isSingle = true;
        private boolean deleteResultInfo = false;
        private String customFileName;
        private TokenConfig tokenConfig;
        private OriginType originType = OriginType.BROWSER;
        
        public Builder(ExportContext exportContext) {
            this.exportContext = exportContext;
            // 从 ExportContext 中提取常用参数
            this.userId = exportContext.getUserId();
            this.tokenConfig = exportContext.getTokenConfig();
        }
        
        public Builder monitor(DBRProgressMonitor monitor) {
            this.monitor = monitor;
            return this;
        }
        
        public Builder processor(DataTransferProcessorDescriptor processor) {
            this.processor = processor;
            return this;
        }
        
        public Builder resultSets(List<WebSQLQueryResultSet> resultSets) {
            this.resultSets = resultSets;
            return this;
        }
        
        public Builder userId(String userId) {
            this.userId = userId;
            return this;
        }
        
        public Builder streamFunction(BiFunction<List<WebSQLQueryResultSet>, ChainBuilder<WebSQLTransferResult>> streamFunction) {
            this.streamFunction = streamFunction;
            return this;
        }
        
        public Builder transferZipName(WebDataTransferName transferZipName) {
            this.transferZipName = transferZipName;
            return this;
        }
        
        public Builder transferFileName(WebDataTransferName transferFileName) {
            this.transferFileName = transferFileName;
            return this;
        }
        
        public Builder needToZipAndUpload(boolean needToZipAndUpload) {
            this.needToZipAndUpload = needToZipAndUpload;
            return this;
        }
        
        public Builder isSingle(boolean isSingle) {
            this.isSingle = isSingle;
            return this;
        }
        
        public Builder deleteResultInfo(boolean deleteResultInfo) {
            this.deleteResultInfo = deleteResultInfo;
            return this;
        }
        
        public Builder customFileName(String customFileName) {
            this.customFileName = customFileName;
            return this;
        }
        
        public Builder tokenConfig(TokenConfig tokenConfig) {
            this.tokenConfig = tokenConfig;
            return this;
        }
        
        public Builder originType(OriginType originType) {
            this.originType = originType;
            return this;
        }
        
        public ExportDataRequest build() {
            // 验证必需参数
            if (exportContext == null) {
                throw new IllegalArgumentException("ExportContext 不能为空");
            }
            if (processor == null) {
                throw new IllegalArgumentException("DataTransferProcessorDescriptor 不能为空");
            }
            if (resultSets == null) {
                throw new IllegalArgumentException("ResultSetSupplier 不能为空");
            }
            if (streamFunction == null) {
                throw new IllegalArgumentException("StreamFunction 不能为空");
            }
            if (transferZipName == null) {
                throw new IllegalArgumentException("TransferZipName 不能为空");
            }
            if (transferFileName == null) {
                throw new IllegalArgumentException("TransferFileName 不能为空");
            }
            
            return new ExportDataRequest(this);
        }
    }
    
    /**
     * 创建建造器的便捷方法
     */
    public static Builder builder(ExportContext exportContext) {
        return new Builder(exportContext);
    }
}
