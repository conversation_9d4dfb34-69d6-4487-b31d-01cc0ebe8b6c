package com.dc.summer.model.data;

import com.dc.springboot.core.model.result.WebSQLExecuteInfo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel("Web异步任务反应")
public class WebAsyncTaskReact {

    @ApiModelProperty(value = "Web异步任务信息")
    private WebAsyncTaskInfo taskInfo;

    @ApiModelProperty(value = "Web SQL 执行信息")
    private WebSQLExecuteInfo executeInfo;

}
