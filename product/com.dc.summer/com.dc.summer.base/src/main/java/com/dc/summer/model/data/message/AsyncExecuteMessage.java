package com.dc.summer.model.data.message;

import com.dc.repository.redis.model.EnvConnection;
import com.dc.repository.redis.model.EnvParamsConfig;
import com.dc.repository.redis.model.EnvSchema;
import com.dc.repository.redis.model.EnvUser;
import com.dc.springboot.core.model.data.Message;
import com.dc.springboot.core.model.database.TokenConfig;
import com.dc.springboot.core.model.execution.ValidExecuteModel;
import com.dc.springboot.core.model.message.ExecuteEvent;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.List;

@Data
@ApiModel("异步执行")
public class AsyncExecuteMessage extends Message {


    @Valid
    @NotNull
    @ApiModelProperty(value = "批处理执行模型", required = true)
    private List<ValidExecuteModel> batchExecuteModels;

    @NotNull
    @ApiModelProperty(value = "异步执行id", required = true)
    private Integer id;

    @NotNull
    @ApiModelProperty(value = "user 信息")
    private EnvUser user;

    @NotNull
    @ApiModelProperty(value = "schema 信息")
    private EnvSchema schema;

    @NotNull
    @ApiModelProperty(value = "参数信息")
    private EnvParamsConfig params;

    @NotNull
    @ApiModelProperty(value = "envConnection")
    private EnvConnection connection;

    @ApiModelProperty("SQL导出信息")
    private SqlExportMessage sqlExportMessage = new SqlExportMessage();


    @Valid
    @NotNull
    @ApiModelProperty(value = "令牌配置 - 修改里面配置的时候，重新打开会话就行，不会生成新的连接。", required = true)
    private TokenConfig tokenConfig;

    @Valid
    @ApiModelProperty(value = "告警消息")
    private ExecuteEvent executeEvent = new ExecuteEvent();
}
