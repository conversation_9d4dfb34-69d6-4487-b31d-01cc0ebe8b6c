package com.dc.summer.model.type;

import lombok.Getter;

public enum AsyncExecuteStatus {

    WAITING_EXECUTION(1, "待执行"),
    EXECUTING(2, "执行中"),
    SUCCESS(3, "执行成功"),
    FAILURE(4, "执行失败"),
    INTERRUPTED(5, "已中断"),
    INTERRUPTING(6, "正在中断");

    @Getter
    private final Integer value;

    @Getter
    private final String desc;

    AsyncExecuteStatus(Integer value, String desc) {
        this.value = value;
        this.desc = desc;
    }
}
