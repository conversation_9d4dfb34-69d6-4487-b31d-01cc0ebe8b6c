package com.dc.summer.model.data.message;

import com.dc.springboot.core.model.data.Message;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

@Data
@ApiModel("异步执行中断")
public class AsyncInterruptMessage {
    @NotNull
    @ApiModelProperty(value = "异步执行id", required = true)
    private Integer id;
}
