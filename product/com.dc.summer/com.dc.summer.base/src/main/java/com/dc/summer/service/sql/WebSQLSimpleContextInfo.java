package com.dc.summer.service.sql;


import com.dc.summer.DBException;
import com.dc.summer.model.exec.DBCExecutionContext;
import com.dc.summer.model.exec.DBCExecutionPurpose;
import com.dc.summer.model.exec.DBCSession;
import com.dc.summer.model.impl.jdbc.JDBCExecutionContext;
import com.dc.summer.model.runtime.DBRProgressMonitor;

public class WebSQLSimpleContextInfo extends WebSQLContextInfo {

    protected WebSQLSimpleContextInfo(String token) {
        super(token);
    }

    @Override
    public void close() {
    }

    @Override
    public DBCExecutionContext getExecutionContext() throws DBException {
        return null;
    }

    @Override
    public DBCSession openSession(DBRProgressMonitor monitor, DBCExecutionPurpose purpose, String title) {
        return null;
    }
}
