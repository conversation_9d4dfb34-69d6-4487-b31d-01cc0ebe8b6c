package com.dc.summer.service.transfer;

import com.dc.springboot.core.model.database.TokenConfig;
import com.dc.springboot.core.model.result.WebSQLQueryResult;
import com.dc.springboot.core.model.type.OriginType;
import com.dc.summer.ModelPreferences;
import com.dc.summer.model.data.model.ResultsIndexModel;
import com.dc.summer.model.data.model.SqlExportModel;
import com.dc.summer.model.export.context.ExportContext;
import com.dc.summer.model.type.PageSelectedType;
import com.dc.summer.service.transfer.request.ExportDataRequest;
import com.dc.summer.service.transfer.strategy.ExportDataSourceStrategy;
import com.dc.summer.service.transfer.strategy.ExportDataSourceStrategyFactory;
import com.dc.summer.component.SummerMapper;
import com.dc.summer.model.DBPDataSource;
import com.dc.summer.model.DBUtils;
import com.dc.summer.model.data.DBDAttributeBinding;
import com.dc.summer.model.data.result.ConsoleType;
import com.dc.summer.model.data.result.DataResultNodeInfo;
import com.dc.summer.model.data.result.DataResultVisitor;
import com.dc.config.ApiConfig;
import com.dc.springboot.core.model.data.ResultFormat;
import com.dc.springboot.core.model.message.MessageConstants;
import com.dc.springboot.core.model.sensitive.DataDesensitizeProcessor;
import com.dc.springboot.core.model.sensitive.ExportDesensitizeProcessor;
import com.dc.summer.DBException;
import com.dc.springboot.core.component.Resource;
import com.dc.summer.config.SummerConfig;
import com.dc.springboot.core.model.chain.ChainBuilder;
import com.dc.summer.model.data.DBDDataDesensitizeProcessor;
import com.dc.summer.model.data.message.SqlExportMessage;
import com.dc.springboot.core.model.exception.ServiceException;
import com.dc.summer.model.exec.DBCExecutionContext;
import com.dc.summer.model.exec.DBCExecutionContextDefaults;
import com.dc.summer.model.sql.SQLQueryContainer;
import com.dc.summer.model.sql.SqlFieldData;
import com.dc.springboot.core.model.type.ColumnDelimiterType;
import com.dc.springboot.core.model.type.ExportType;
import com.dc.summer.model.struct.rdb.DBSSchema;
import com.dc.summer.service.container.WebDataTransferContainer;
import com.dc.summer.service.container.WebSQLQueryDataContainer;
import com.dc.summer.service.data.AsyncExecuteStatistics;
import com.dc.summer.service.receiver.WebAsyncExecuteDataReceiver;
import com.dc.summer.service.result.WorkOrderTransferResult;
import com.dc.summer.service.sql.WebSQLContextInfo;
import com.dc.springboot.core.model.result.WebSQLTransferResult;
import com.dc.summer.data.transfer.DTConstants;
import com.dc.summer.data.transfer.IDataTransferConsumer;
import com.dc.summer.data.transfer.IDataTransferProcessor;
import com.dc.summer.data.transfer.database.DatabaseProducerSettings;
import com.dc.summer.data.transfer.database.DatabaseTransferProducer;
import com.dc.summer.data.transfer.registry.DataTransferProcessorDescriptor;
import com.dc.summer.data.transfer.stream.IStreamDataExporter;
import com.dc.summer.data.transfer.stream.StreamConsumerSettings;
import com.dc.summer.data.transfer.stream.StreamTransferConsumer;
import com.dc.summer.model.preferences.DBPPropertyDescriptor;
import com.dc.summer.model.runtime.DBRProgressMonitor;
import com.dc.summer.model.struct.DBSDataContainer;
import com.dc.summer.service.sql.WebSQLContextStatus;
import com.dc.summer.service.sql.WebSQLDataDraw;
import com.dc.springboot.core.model.result.WebSQLQueryResultSet;
import com.dc.springboot.core.model.result.WebSQLResultsInfo;
import com.dc.springboot.core.model.execution.ExportExecuteModel;
import com.dc.summer.model.impl.data.PrimaryKeyProcessor;
import com.dc.utils.CommonUtils;
import com.dc.utils.io.ByteOrderMark;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.eclipse.core.runtime.IAdaptable;
import org.jetbrains.annotations.NotNull;

import java.io.File;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.function.Supplier;
import java.util.stream.Collectors;

@Slf4j
public class WebDataTransfer {

    private final WebSQLContextInfo contextInfo;

    private final WebDataTransferHelper helper;

    private final int exportRows;

    private final long exportSize;

    private final String dcBackendPath;

    private String queryText;

    private Throwable error;

    private WebSQLQueryResult queryResult;

    /**
     * SQL窗口
     */
    public WebDataTransfer(WebSQLContextInfo contextInfo) {
        this.contextInfo = contextInfo;
        this.helper = new WebDataTransferHelper(contextInfo.getToken(), contextInfo::dispose);
        SummerConfig config = Resource.getBean(SummerConfig.class);
        this.exportRows = config.getExport().getRows();
        this.exportSize = config.getExport().getSize();
        this.dcBackendPath = config.getPath().getDcBackend();
    }

    /**
     * 工单
     */
    public WebDataTransfer(String orderId, WebSQLContextInfo contextInfo) {
        this.contextInfo = contextInfo;
        this.helper = new WebDataTransferHelper(String.format("order_%s", orderId), null);
        SummerConfig config = Resource.getBean(SummerConfig.class);
        this.exportRows = config.getExport().getRows();
        this.exportSize = config.getExport().getSize();
        this.dcBackendPath = config.getPath().getDcBackend();
    }

    public WorkOrderTransferResult exportDataBySql(
            DBRProgressMonitor monitor,
            DataTransferProcessorDescriptor processor,
            SqlExportMessage sqlExportMessage,
            WebDataTransferName transferZipName,
            WebDataTransferName transferFileName,
            boolean needToZipAndUpload,
            boolean deleteResultInfo,
            String sql,
            DBDDataDesensitizeProcessor dataDesensitize,
            long total, Integer consoleType, String dbName, boolean isAsyncExecute) throws DBException {

        helper.reloading(needToZipAndUpload, true, deleteResultInfo, transferZipName, transferFileName);

        WorkOrderTransferResult orderTransferResult = new WorkOrderTransferResult();

        try {

            Map<String, Object> properties = getProperties(sqlExportMessage);

            Path exportFile = helper.getExportFile(1);

            WebSQLQueryDataContainer dataContainer = new WebSQLQueryDataContainer(contextInfo.getDataSource(), contextInfo.getExecutionContext(), sql, total, null, isAsyncExecute);

            contextInfo.getDataSource().getContainer().getPreferenceStore().setValue(ModelPreferences.RESULT_SET_MAX_ROWS_USE_SQL, true);

            WebSQLResultsInfo resultsInfo = new WebSQLResultsInfo(dataContainer, null);
            resultsInfo.setDataDesensitize(dataDesensitize);
            resultsInfo.setPrimaryKeyModel(new PrimaryKeyProcessor());

            if (isAsyncExecute) {
                resultsInfo.setResultFormat(sqlExportMessage.getResultFormat());
            }

            ResultFormat resultFormat = initUseNativeFormat(sqlExportMessage, resultsInfo, dataContainer);

            AtomicReference<Boolean> sensitiveSelect = new AtomicReference<>(null);
            TokenConfig tokenConfig = new TokenConfig();
            tokenConfig.setConsole(consoleType);
            tokenConfig.setDbName(dbName);

            PrimaryKeyProcessor primaryKeyProcessor = resultsInfo.getPrimaryKeyModel();
            File file = this.exec(
                    monitor,
                    processor,
                    dataContainer,
                    sqlExportMessage.getFileCharset(),
                    properties,
                    exportFile,
                    resultsInfo,
                    0,
                    0,
                    resultFormat,
                    (DataDesensitizeProcessor) resultsInfo.getDataDesensitize(),
                    resultsInfo.getSqlFieldDataList(),
                    sqlExportMessage.isExcelUseNumberFormat(),
                    sensitiveSelect,
                    helper,
                    1,
                    null,
                    tokenConfig,
                    exportSize,
                    null,
                    isAsyncExecute,
                    primaryKeyProcessor);
            orderTransferResult.setSensitiveSelect(sensitiveSelect.get());
            orderTransferResult.setMessage(String.format("执行成功，当前返回：[%s]行", resultsInfo.getRowSize() > 0 ? resultsInfo.getRowSize() : 0));
            orderTransferResult.setQueryText(queryText);
            orderTransferResult.setQueryResult(queryResult);
            helper.addExportFile(file, true);
        } catch (Exception e) {
            log.error("导出失败！", e);
            throw new DBException(e.getCause().getMessage());
        } finally {
            orderTransferResult.setError(error);
        }
        if (error == null) {
            orderTransferResult.setFile(helper.toSingleExportFile(getProcessorFileExtension(processor), !isAsyncExecute));
        }

        return orderTransferResult;
    }

    public WebSQLTransferResult exportDataByContext(
            DBRProgressMonitor monitor,
            DataTransferProcessorDescriptor processor,
            Supplier<List<WebSQLQueryResultSet>> supplier,
            SqlExportMessage sqlExportMessage,
            String userId,
            Function<List<WebSQLQueryResultSet>, ChainBuilder<WebSQLTransferResult>> streamFunction,
            WebDataTransferName transferZipName,
            WebDataTransferName transferFileName,
            boolean needToZipAndUpload,
            boolean isSingle,
            boolean deleteResultInfo,
            String customFileName,
            TokenConfig tokenConfig,
            OriginType originType) {

        contextInfo.setStatus(WebSQLContextStatus.NORMAL);

        long startTime = System.currentTimeMillis();

        helper.reloading(needToZipAndUpload, isSingle, deleteResultInfo, transferZipName, transferFileName);

        WebSQLTransferResult transferResult = new WebSQLTransferResult();

        List<WebSQLQueryResultSet> resultSets = null;

        try {

            resultSets = supplier.get();

            Map<String, Object> properties = getProperties(sqlExportMessage);

            // 统一处理导出逻辑，参数内容以 ExportExecuteModel 为准

            if (CollectionUtils.isNotEmpty(sqlExportMessage.getSqlExportModels()) && resultSets.isEmpty()) {

                List<ExportExecuteModel> exportExecuteModels = sqlExportMessage.getSqlExportModels().stream()
                        .map(SqlExportModel::getResultsIndexModels)
                        .flatMap(Collection::stream)
                        .map(ResultsIndexModel::getBatchExecuteModel)
                        .collect(Collectors.toList());

                // 使用 ExportExecuteModel 进行处理
                int batchExecuteSize = exportExecuteModels.size();
                for (ExportExecuteModel executeModel : exportExecuteModels) {
                    log.info("使用executeModel进行导出处理");

                    PrimaryKeyProcessor primaryKeyProcessor = new PrimaryKeyProcessor(executeModel.getPrimaryKeyColumns(), executeModel.getTableName());
                    processExportItem(
                            monitor, processor, sqlExportMessage, properties, customFileName, helper,
                            contextInfo, tokenConfig, exportSize,
                            executeModel.getSql(),
                            executeModel.getTableName(),
                            executeModel.getPageSize(),
                            executeModel.getOffset(),
                            executeModel.getExportLimit(),
                            executeModel.getSqlFieldDataList(),
                            executeModel.getData(),
                            null, // resultsInfo
                            executeModel.getResultName(), // resultName
                            batchExecuteSize,
                            null, // resultSet,
                            primaryKeyProcessor
                    );
                }
            } else {
                // 使用 resultSets 处理，但参数以 ExportExecuteModel 格式为准
                for (WebSQLQueryResultSet resultSet : resultSets) {
                    WebSQLResultsInfo resultsInfo = contextInfo.getResults(resultSet.getResultId());

                    // 获取表名，与 ExportExecuteModel.getTableName() 对应
                    String tableName = null;
                    DBSDataContainer dataContainer = resultsInfo.getDataContainer();
                    if (dataContainer instanceof IAdaptable) {
                        tableName = ((IAdaptable) dataContainer).getAdapter(SQLQueryContainer.class).getTableName();
                    }

                    PrimaryKeyProcessor primaryKeyProcessor = resultsInfo.getPrimaryKeyModel();
                    processExportItem(
                            monitor, processor, sqlExportMessage, properties, customFileName, helper,
                            contextInfo, tokenConfig, exportSize,
                            resultSet.getSql(), // 对应 ExportExecuteModel.getSql()
                            tableName, // 对应 ExportExecuteModel.getTableName()
                            resultSet.getPageSize(), // 对应 ExportExecuteModel.getPageSize()
                            resultsInfo.getOffset(), // 对应 ExportExecuteModel.getOffset()
                            resultSet.getExportRowsLimit(), // 对应 ExportExecuteModel.getExportLimit()
                            resultsInfo.getSqlFieldDataList(), // 对应 ExportExecuteModel.getSqlFieldDataList()
                            resultSet.getData(), // 对应 ExportExecuteModel.getData()
                            resultsInfo,
                            resultSet.getResultName(),
                            resultSets.size(),
                            resultSet, // 传递 resultSet 用于 WebDataTransferContainer
                            primaryKeyProcessor
                    );

                    // 设置结果集统计信息
                    resultSet.setExportRowsCount(resultsInfo.getRowSize());
                    if (!resultSet.isExportLimit()) {
                        resultSet.setExportLimit(resultsInfo.isExportLimit());
                    }
                }
            }

            transferResult.setMessage("导出结果集成功！");
            transferResult.setSuccess(true);
        } catch (Exception e) {
            contextInfo.setLastException(e);
            log.error("导出失败！", e);
            transferResult.setMessage(e.getMessage());
            transferResult.setSuccess(false);
        } finally {
            helper.tryToZipAndUpload(transferResult.isSuccess(), dcBackendPath, userId, sqlExportMessage.getEncryptPassword(), transferResult,
                    getProcessorFileExtension(processor), ApiConfig.UPLOAD.getPath(), originType);
            transferResult.setDuration(System.currentTimeMillis() - startTime);
            streamFunction.apply(resultSets).exec(transferResult);
        }

        return transferResult;
    }

    /**
     * 重载的导出方法 - 使用建造器模式和策略模式优化
     *
     * @param request 封装了所有导出参数的请求对象
     * @return 导出结果
     */
    public WebSQLTransferResult exportDataByContext(ExportDataRequest request) {
        contextInfo.setStatus(WebSQLContextStatus.NORMAL);

        long startTime = System.currentTimeMillis();

        helper.reloading(
                request.isNeedToZipAndUpload(),
                request.isSingle(),
                request.isDeleteResultInfo(),
                request.getTransferZipName(),
                request.getTransferFileName()
        );

        WebSQLTransferResult transferResult = new WebSQLTransferResult();

        List<WebSQLQueryResultSet> resultSets = request.getResultSets();

        try {
            // 使用策略工厂选择合适的处理策略
            ExportDataSourceStrategyFactory strategyFactory = new ExportDataSourceStrategyFactory(this, contextInfo);
            ExportDataSourceStrategy strategy = strategyFactory.selectStrategy(request);

            // 执行策略处理
            strategy.processExportData(request, helper);

            transferResult.setMessage("导出结果集成功！");
            transferResult.setSuccess(true);

        } catch (Exception e) {
            contextInfo.setLastException(e);
            log.error("导出失败！", e);
            transferResult.setMessage(e.getMessage());
            transferResult.setSuccess(false);
        } finally {
            String encryptPassword = request.getExportContext().getSecurityConfig() != null ?
                    request.getExportContext().getSecurityConfig().getEncryptPassword() : null;
            helper.tryToZipAndUpload(
                    transferResult.isSuccess(),
                    dcBackendPath,
                    request.getUserId(),
                    encryptPassword,
                    transferResult,
                    getProcessorFileExtension(request.getProcessor()),
                    ApiConfig.UPLOAD.getPath(),
                    request.getOriginType()
            );
            transferResult.setDuration(System.currentTimeMillis() - startTime);

            List<ExportExecuteModel> exportExecuteModels = request.getExportContext().getSqlExportModels().stream()
                    .map(SqlExportModel::getResultsIndexModels)
                    .flatMap(Collection::stream)
                    .map(ResultsIndexModel::getBatchExecuteModel)
                    .collect(Collectors.toList());

            request.getStreamFunction().apply(resultSets).exec(transferResult);
        }

        return transferResult;
    }

    /**
     * 统一的导出项处理方法，参数以 ExportExecuteModel 为准
     * 修改为 public 以便策略类访问
     */
    public void processExportItem(
            DBRProgressMonitor monitor,
            DataTransferProcessorDescriptor processor,
            SqlExportMessage sqlExportMessage,
            Map<String, Object> properties,
            String customFileName,
            WebDataTransferHelper helper,
            WebSQLContextInfo contextInfo,
            TokenConfig tokenConfig,
            long exportSize,
            String sql, // 对应 ExportExecuteModel.getSql()
            String tableName, // 对应 ExportExecuteModel.getTableName()
            long pageSize, // 对应 ExportExecuteModel.getPageSize()
            long offset, // 对应 ExportExecuteModel.getOffset()
            long exportLimit, // 对应 ExportExecuteModel.getExportLimit()
            List<SqlFieldData> sqlFieldDataList, // 对应 ExportExecuteModel.getSqlFieldDataList()
            List<Object> data, // 对应 ExportExecuteModel.getData()
            WebSQLResultsInfo resultsInfo, // 可能为null（ExportExecuteModel场景）
            String resultName, // 对应 ExportExecuteModel.getResultName()
            int totalSize, // 用于计算fileSize
            WebSQLQueryResultSet resultSet, // 可能为null（ExportExecuteModel场景）
            PrimaryKeyProcessor primaryKeyProcessor
    ) throws DBException, InterruptedException {

        if (StringUtils.isNotBlank(customFileName)) {
            helper.setTransferFileName(new WebDataTransferName.CustomResultSetName(customFileName));
        }

        contextInfo.confirmInterrupted();

        int fileSize = contextInfo.getDataSource().getInfo().supportsMergeExportFile() ? 1 : totalSize;
        Path exportFile = helper.getExportFile(fileSize);

        // 创建数据容器
        DBSDataContainer dataContainer = createDataContainer(
                sqlExportMessage, contextInfo, tokenConfig, sql, tableName, pageSize,
                resultsInfo, properties, resultSet);

        // 处理结果格式
        ResultFormat resultFormat = createResultFormat(sqlExportMessage, resultsInfo, dataContainer);

        // 处理脱敏
        DataDesensitizeProcessor dataDesensitize = createDataDesensitizeProcessor(
                sqlExportMessage, resultsInfo, tokenConfig);

        // 执行导出
        File file = this.exec(
                monitor,
                processor,
                dataContainer,
                sqlExportMessage.getFileCharset(),
                properties,
                exportFile,
                resultsInfo,
                offset,
                exportLimit,
                resultFormat,
                dataDesensitize,
                sqlFieldDataList,
                sqlExportMessage.isExcelUseNumberFormat(),
                new AtomicReference<>(),
                helper,
                fileSize,
                resultName,
                tokenConfig,
                ObjectUtils.getIfNull(sqlExportMessage.getSplitFileSize(), () -> exportSize),
                data,
                false,
                primaryKeyProcessor);

        helper.addExportFile(file, false);

        if (monitor.isCanceled()) {
            throw new DBException(MessageConstants.INTERRUPTED.getMessage());
        }
    }

    /**
     * 重载的导出项处理方法，接受 ExportContext 参数
     * 为策略类提供的公共方法
     */
    public void processExportItem(
            DBRProgressMonitor monitor,
            DataTransferProcessorDescriptor processor,
            ExportContext exportContext,
            Map<String, Object> properties,
            String customFileName,
            WebDataTransferHelper helper,
            TokenConfig tokenConfig,
            String sql,
            String tableName,
            long pageSize,
            long offset,
            long exportLimit,
            List<SqlFieldData> sqlFieldDataList,
            List<Object> data,
            WebSQLResultsInfo resultsInfo,
            String resultName,
            int totalSize,
            WebSQLQueryResultSet resultSet,
            PrimaryKeyProcessor primaryKeyProcessor
    ) throws DBException, InterruptedException {

        if (StringUtils.isNotBlank(customFileName)) {
            helper.setTransferFileName(new WebDataTransferName.CustomResultSetName(customFileName));
        }

        contextInfo.confirmInterrupted();

        int fileSize = contextInfo.getDataSource().getInfo().supportsMergeExportFile() ? 1 : totalSize;
        Path exportFile = helper.getExportFile(fileSize);

        // 创建数据容器
        DBSDataContainer dataContainer = createDataContainer(
                exportContext, contextInfo, tokenConfig, sql, tableName, pageSize,
                resultsInfo, properties, offset, resultSet);

        // 如果是全部页导出，则重置 offset
        if (exportContext.getFormatConfig().getPageSelected() == PageSelectedType.ALL_PAGES) {
            offset = 0;
        }

        // 处理结果格式
        ResultFormat resultFormat = createResultFormat(exportContext, resultsInfo, dataContainer);

        // 处理脱敏
        DataDesensitizeProcessor dataDesensitize = createDataDesensitizeProcessor(exportContext, resultsInfo, tokenConfig);

        // 执行导出
        File file = this.exec(
                monitor,
                processor,
                dataContainer,
                exportContext.getFormatConfig().getFileCharset(),
                properties,
                exportFile,
                resultsInfo,
                offset,
                exportLimit,
                resultFormat,
                dataDesensitize,
                sqlFieldDataList,
                exportContext.getExcelConfig() != null && exportContext.getExcelConfig().isUseNumberFormat(),
                new AtomicReference<>(),
                helper,
                fileSize,
                resultName,
                tokenConfig,
                ObjectUtils.getIfNull(exportContext.getFormatConfig().getSplitFileSize(), () -> exportSize),
                data,
                false,
                primaryKeyProcessor);

        helper.addExportFile(file, false);

        if (monitor != null && monitor.isCanceled()) {
            throw new DBException(MessageConstants.INTERRUPTED.getMessage());
        }
    }

    /**
     * 创建数据容器，统一处理 ValidExecuteModel 和 resultSet 的逻辑差异
     */
    private DBSDataContainer createDataContainer(
            SqlExportMessage sqlExportMessage,
            WebSQLContextInfo contextInfo,
            TokenConfig tokenConfig,
            String sql,
            String tableName,
            long pageSize,
            WebSQLResultsInfo resultsInfo,
            Map<String, Object> properties,
            WebSQLQueryResultSet resultSet) throws DBException {

        DBCExecutionContext context = contextInfo.getExecutionContext();

        switch (sqlExportMessage.getPageSelected()) {
            case CURRENT_PAGE:
                if (resultsInfo != null) {
                    // 原有 resultSet 逻辑：需要判断是否重新查询
                    boolean isUseNativeDateFormat = sqlExportMessage.getExcelUseOriginalFormat() == 1 && resultsInfo.getResultFormat().getUseNativeFormat();
                    boolean isDateType = ExportType.XLSX.equals(sqlExportMessage.getExportType()) && resultsInfo.isDateType() && !isUseNativeDateFormat;
                    boolean isDesensitize = sqlExportMessage.isExportDesensitize();
                    DataDesensitizeProcessor dataDesensitize = (DataDesensitizeProcessor) resultsInfo.getDataDesensitize();
                    boolean isSwitchConsoleType = !ConsoleType.equalsByNullable(dataDesensitize.getConsoleType(),
                            ConsoleType.of(tokenConfig == null ? null : tokenConfig.getConsole()));

                    log.info("当前页导出 : containClob: {}, isDateType: {}, isDesensitize: {}, isSwitchConsoleType: {}",
                            resultsInfo.isContainClob(), isDateType, isDesensitize, isSwitchConsoleType);

                    if (resultsInfo.isContainClob() || isDateType || isDesensitize || isSwitchConsoleType) {
                        log.info("当前页导出 : 重新查询");
                        return new WebSQLQueryDataContainer(context.getDataSource(), context, sql, pageSize, tableName, false);
                    } else {
                        log.info("当前页导出 : 未重新查询");
                        DBSDataContainer dataContainer = new WebDataTransferContainer(context, resultSet, resultsInfo, tableName);
                        properties.put(DTConstants.CONTEXT_SCHEMA_NAME, getContextSchemaName(context, dataContainer.getDataSource()));
                        return dataContainer;
                    }
                } else {
                    // ValidExecuteModel 逻辑：直接创建查询容器
                    log.info("当前页导出 : 使用executeModel，pageSize: {}", pageSize);
                    return new WebSQLQueryDataContainer(context.getDataSource(), context, sql, pageSize, tableName, false);
                }
            case ALL_PAGES:
                if (resultsInfo != null) {
                    log.info("全部页导出 : 使用resultSet");
                } else {
                    log.info("全部页导出 : 使用executeModel");
                }
                return new WebSQLQueryDataContainer(context.getDataSource(), context, sql, 0, tableName, false);
            default:
                throw new ServiceException("Not find pageSelected");
        }
    }

    /**
     * 创建结果格式，统一处理格式设置
     */
    private ResultFormat createResultFormat(
            SqlExportMessage sqlExportMessage,
            WebSQLResultsInfo resultsInfo,
            DBSDataContainer dataContainer) {

        if (resultsInfo != null) {
            // 原有 resultSet 逻辑
            return initUseNativeFormat(sqlExportMessage, resultsInfo, dataContainer);
        } else {
            // ValidExecuteModel 逻辑
            ResultFormat resultFormat =  sqlExportMessage.getResultFormat() != null ?
                    sqlExportMessage.getResultFormat() : new ResultFormat();

            resultFormat.setExcelDateTimeFormat(sqlExportMessage.getExcelDatetimeFormat());

            boolean useNativeFormat = sqlExportMessage.getExcelUseOriginalFormat() == 1;
            try {
                dataContainer.getDataSource().getContainer().setUseNativeDateTimeFormat(useNativeFormat);
            } catch (Exception e) {
                log.error("设置时间日期原格式错误");
            }
            return resultFormat;
        }
    }

    /**
     * 创建脱敏处理器，统一处理脱敏逻辑
     */
    private DataDesensitizeProcessor createDataDesensitizeProcessor(
            SqlExportMessage sqlExportMessage,
            WebSQLResultsInfo resultsInfo,
            TokenConfig tokenConfig) {

        DataDesensitizeProcessor dataDesensitize;

        if (resultsInfo != null) {
            // 原有 resultSet 逻辑：从 resultsInfo 获取
            dataDesensitize = (DataDesensitizeProcessor) resultsInfo.getDataDesensitize();
        } else {
            // ValidExecuteModel 逻辑：创建默认处理器
            dataDesensitize = new DataDesensitizeProcessor();
        }

        // 统一的脱敏包装逻辑
        if (sqlExportMessage.isExportDesensitize()) {
            dataDesensitize = new ExportDesensitizeProcessor(dataDesensitize);
        }

        return dataDesensitize;
    }

    /**
     * 创建数据容器，接受 ExportContext 参数的重载版本
     */
    private DBSDataContainer createDataContainer(
            ExportContext exportContext,
            WebSQLContextInfo contextInfo,
            TokenConfig tokenConfig,
            String sql,
            String tableName,
            long pageSize,
            WebSQLResultsInfo resultsInfo,
            Map<String, Object> properties,
            long offset,
            WebSQLQueryResultSet resultSet) throws DBException {

        DBCExecutionContext context = contextInfo.getExecutionContext();

        switch (exportContext.getFormatConfig().getPageSelected()) {
            case CURRENT_PAGE:
                if (resultsInfo != null) {
                    // 原有 resultSet 逻辑：需要判断是否重新查询
                    boolean isUseNativeDateFormat = exportContext.getExcelConfig() != null &&
                            exportContext.getExcelConfig().getUseOriginalFormat() == 1 &&
                            resultsInfo.getResultFormat().getUseNativeFormat();
                    boolean isDateType = ExportType.XLSX.equals(exportContext.getFormatConfig().getExportType()) &&
                            resultsInfo.isDateType() && !isUseNativeDateFormat;
                    boolean isDesensitize = exportContext.getSecurityConfig() != null &&
                            exportContext.getSecurityConfig().isExportDesensitize();
                    DataDesensitizeProcessor dataDesensitize = (DataDesensitizeProcessor) resultsInfo.getDataDesensitize();
                    boolean isSwitchConsoleType = !ConsoleType.equalsByNullable(dataDesensitize.getConsoleType(),
                            ConsoleType.of(tokenConfig == null ? null : tokenConfig.getConsole()));

                    log.info("当前页导出 : containClob: {}, isDateType: {}, isDesensitize: {}, isSwitchConsoleType: {}",
                            resultsInfo.isContainClob(), isDateType, isDesensitize, isSwitchConsoleType);

                    if (resultsInfo.isContainClob() || isDateType || isDesensitize || isSwitchConsoleType) {
                        log.info("当前页导出 : 重新查询");
                        return new WebSQLQueryDataContainer(context.getDataSource(), context, sql, pageSize, tableName, false);
                    } else {
                        log.info("当前页导出 : 未重新查询");
                        DBSDataContainer dataContainer = new WebDataTransferContainer(context, resultSet, resultsInfo, tableName);
                        properties.put(DTConstants.CONTEXT_SCHEMA_NAME, getContextSchemaName(context, dataContainer.getDataSource()));
                        return dataContainer;
                    }
                } else {
                    // ValidExecuteModel 逻辑：直接创建查询容器
                    log.info("当前页导出 : 使用executeModel，pageSize: {}", pageSize);
                    return new WebSQLQueryDataContainer(context.getDataSource(), context, sql, pageSize, tableName, false);
                }
            case ALL_PAGES:
                log.info("全部页导出");
                return new WebSQLQueryDataContainer(context.getDataSource(), context, sql, 0, tableName, false);
            default:
                throw new ServiceException("Not find pageSelected");
        }
    }

    /**
     * 创建结果格式，接受 ExportContext 参数的重载版本
     */
    private ResultFormat createResultFormat(ExportContext exportContext, WebSQLResultsInfo resultsInfo, DBSDataContainer dataContainer) {

        if (resultsInfo != null) {
            // 原有 resultSet 逻辑
            return initUseNativeFormat(exportContext, resultsInfo, dataContainer);
        } else {
            // ValidExecuteModel 逻辑
            return exportContext.getResultFormat() != null ? exportContext.getResultFormat() : new ResultFormat();
        }
    }

    /**
     * 创建脱敏处理器，接受 ExportContext 参数的重载版本
     */
    private DataDesensitizeProcessor createDataDesensitizeProcessor(
            ExportContext exportContext,
            WebSQLResultsInfo resultsInfo,
            TokenConfig tokenConfig) {

        DataDesensitizeProcessor dataDesensitize;

        if (resultsInfo != null) {
            // 原有 resultSet 逻辑：从 resultsInfo 获取
            dataDesensitize = (DataDesensitizeProcessor) resultsInfo.getDataDesensitize();
        } else {
            // ValidExecuteModel 逻辑：创建默认处理器
            dataDesensitize = new DataDesensitizeProcessor();
        }

        // 统一的脱敏包装逻辑
        if (exportContext.getSecurityConfig() != null && exportContext.getSecurityConfig().isExportDesensitize()) {
            dataDesensitize = new ExportDesensitizeProcessor(dataDesensitize);
        }

        return dataDesensitize;
    }

    private String getContextSchemaName(DBCExecutionContext executionContext, DBPDataSource dataSource) {
        DBSSchema defaultSchema = Optional.ofNullable(executionContext).map(DBCExecutionContext::getContextDefaults).map(DBCExecutionContextDefaults::getDefaultSchema).orElse(null);
        if (defaultSchema != null) {
            return DBUtils.getQuotedIdentifier(dataSource, defaultSchema.getName());
        }
        return null;
    }

    private static ResultFormat initUseNativeFormat(SqlExportMessage sqlExportMessage, WebSQLResultsInfo resultsInfo, DBSDataContainer dataContainer) {
        boolean useNativeFormat;
        ResultFormat resultFormat;
        if (ExportType.XLSX.equals(sqlExportMessage.getExportType())) {
            resultFormat = new ResultFormat();
            resultFormat.setExcelDateTimeFormat(sqlExportMessage.getExcelDatetimeFormat());
            resultFormat.setDateTimeFormat(resultsInfo.getResultFormat().getDateTimeFormat());
            resultFormat.setDateFormat(resultsInfo.getResultFormat().getDateFormat());
            resultFormat.setTimeFormat(resultsInfo.getResultFormat().getTimeFormat());
            useNativeFormat = sqlExportMessage.getExcelUseOriginalFormat() == 1;
        } else {
            resultFormat = resultsInfo.getResultFormat();
            useNativeFormat = resultsInfo.getResultFormat().getUseNativeFormat();
        }
        //导出设置默认采用时间日期原格式
        try {
            dataContainer.getDataSource().getContainer().setUseNativeDateTimeFormat(useNativeFormat);
        } catch (Exception e) {
            log.error("设置时间日期原格式错误");
        }
        return resultFormat;
    }

    private static ResultFormat initUseNativeFormat(ExportContext exportContext, WebSQLResultsInfo resultsInfo, DBSDataContainer dataContainer) {
        boolean useNativeFormat;
        ResultFormat resultFormat;
        if (ExportType.XLSX.equals(exportContext.getFormatConfig().getExportType())) {
            resultFormat = new ResultFormat();
            if (exportContext.getExcelConfig() != null) {
                resultFormat.setExcelDateTimeFormat(exportContext.getExcelConfig().getDatetimeFormat());
            }
            resultFormat.setDateTimeFormat(resultsInfo.getResultFormat().getDateTimeFormat());
            resultFormat.setDateFormat(resultsInfo.getResultFormat().getDateFormat());
            resultFormat.setTimeFormat(resultsInfo.getResultFormat().getTimeFormat());
            useNativeFormat = exportContext.getExcelConfig() != null &&
                    exportContext.getExcelConfig().getUseOriginalFormat() == 1;
        } else {
            resultFormat = resultsInfo.getResultFormat();
            useNativeFormat = resultsInfo.getResultFormat().getUseNativeFormat();
        }
        //导出设置默认采用时间日期原格式
        try {
            dataContainer.getDataSource().getContainer().setUseNativeDateTimeFormat(useNativeFormat);
        } catch (Exception e) {
            log.error("设置时间日期原格式错误");
        }
        return resultFormat;
    }


    @NotNull
    private Map<String, Object> getProperties(SqlExportMessage sqlExportMessage) {
        Map<String, Object> properties = new HashMap<>();
        if (sqlExportMessage.getLineDelimiter() != null) {
            properties.put(DTConstants.LINE_DELIMITER, sqlExportMessage.getLineDelimiter().getValue());
        }
        if (sqlExportMessage.getTextIdentifier() != null) {
            properties.put(DTConstants.TEXT_IDENTIFIER, sqlExportMessage.getTextIdentifier().getValue());
        }
        if (sqlExportMessage.getColumnDelimiter() != null) {
            if (sqlExportMessage.getColumnDelimiter() == ColumnDelimiterType.OTHER) {
                properties.put(DTConstants.COLUMN_DELIMITER, sqlExportMessage.getOtherDelimiter());
            } else {
                properties.put(DTConstants.COLUMN_DELIMITER, sqlExportMessage.getColumnDelimiter().getValue());
            }
        }
        properties.put(DTConstants.RESULT_STAGE, sqlExportMessage.getStage());

        properties.put(DTConstants.PROP_WATERMARK_CONTENT, sqlExportMessage.getWatermarkContent());
        properties.put(DTConstants.PROP_WATERMARK_ANGLE, sqlExportMessage.getWatermarkAngle());
        return properties;
    }

    /**
     * 从 ExportContext 获取导出属性配置
     * 为策略类提供的公共方法
     */
    @NotNull
    public Map<String, Object> getExportProperties(ExportContext exportContext) {
        Map<String, Object> properties = new HashMap<>();

        var formatConfig = exportContext.getFormatConfig();
        if (formatConfig.getLineDelimiter() != null) {
            properties.put(DTConstants.LINE_DELIMITER, formatConfig.getLineDelimiter().getValue());
        }
        if (formatConfig.getTextIdentifier() != null) {
            properties.put(DTConstants.TEXT_IDENTIFIER, formatConfig.getTextIdentifier().getValue());
        }
        if (formatConfig.getColumnDelimiter() != null) {
            if (formatConfig.getColumnDelimiter() == ColumnDelimiterType.OTHER) {
                properties.put(DTConstants.COLUMN_DELIMITER, formatConfig.getOtherDelimiter());
            } else {
                properties.put(DTConstants.COLUMN_DELIMITER, formatConfig.getColumnDelimiter().getValue());
            }
        }

        var securityConfig = exportContext.getSecurityConfig();
        if (securityConfig != null) {
            properties.put(DTConstants.PROP_WATERMARK_CONTENT, securityConfig.getWatermarkContent());
            properties.put(DTConstants.PROP_WATERMARK_ANGLE, securityConfig.getWatermarkAngle());
        }

        return properties;
    }

    private File exec(
            DBRProgressMonitor monitor,
            DataTransferProcessorDescriptor processor,
            DBSDataContainer dataContainer,
            String fileCharset,
            Map<String, Object> properties,
            Path exportFile,
            WebSQLResultsInfo resultsInfo,
            long offset,
            long exportLimit,
            ResultFormat resultFormat,
            DataDesensitizeProcessor dataDesensitize,
            List<SqlFieldData> sqlFieldDataList,
            boolean excelUseNumberFormat,
            AtomicReference<Boolean> sensitiveSelect,
            WebDataTransferHelper helper,
            int fileSize,
            String resultName,
            TokenConfig tokenConfig,
            long exportSize,
            List<Object> data,
            boolean isAsyncExecute,
            PrimaryKeyProcessor primaryKeyProcessor) throws DBException, InterruptedException {

        IDataTransferProcessor processorInstance = processor.getInstance();
        if (!(processorInstance instanceof IStreamDataExporter)) {
            throw new DBException("Invalid processor. " + IStreamDataExporter.class.getSimpleName() + " expected");
        }
        try {

            DataResultVisitor dataResultVisitor = Resource.getBean(DataResultVisitor.class);

            if (dataResultVisitor != null) {
                DataResultNodeInfo dataResultNodeInfo = new DataResultNodeInfo();
                dataResultNodeInfo.setConnectionConfiguration(dataContainer.getDataSource().getContainer().getActualConnectionConfiguration());
                dataResultNodeInfo.setExport(true);
                dataResultNodeInfo.setConsoleType(ConsoleType.of(tokenConfig == null ? null : tokenConfig.getConsole()));
                SummerMapper.INSTANCE.updateDataResultNodeInfo(dataResultNodeInfo, dataDesensitize.getGradedClassifiedModel());
                if (this.contextInfo != null && this.contextInfo.getConnection() != null && StringUtils.isNotBlank(this.contextInfo.getConnection().getDbName())) {
                    dataResultNodeInfo.setDbName(this.contextInfo.getConnection().getDbName());// 导出取env 里的oracle dbName , 仅用于oracle
                }

                if (tokenConfig != null && StringUtils.isNotBlank(tokenConfig.getDbName())) {
                    dataResultNodeInfo.setDbName(tokenConfig.getDbName());//工单导出 导出取applyContent   dbName , 目前仅用于oracle
                }
                dataResultNodeInfo.accept(dataResultVisitor);
            }

            StreamTransferConsumer consumer = null;

            if (isAsyncExecute) {
                consumer = new WebAsyncExecuteDataReceiver(
                        new WebSQLDataDraw(),
                        dataDesensitize,
                        primaryKeyProcessor,
                        resultFormat.getDateTimeFormat(),
                        resultFormat.getTimeFormat(),
                        resultFormat.getDateFormat(),
                        sqlFieldDataList,
                        dataResultVisitor,
                        excelUseNumberFormat,
                        resultName
                );
            } else {
                consumer = new StreamTransferConsumer(
                        new WebSQLDataDraw(),
                        dataDesensitize,
                        primaryKeyProcessor,
                        resultFormat.getDateTimeFormat(),
                        resultFormat.getTimeFormat(),
                        resultFormat.getDateFormat(),
                        sqlFieldDataList,
                        dataResultVisitor,
                        excelUseNumberFormat,
                        resultName);
            }

            IStreamDataExporter exporter = (IStreamDataExporter) processorInstance;

            StreamConsumerSettings settings = new StreamConsumerSettings();

            settings.setOutputFolder(exportFile.getParent().toAbsolutePath().toString());
            //判定是否为自定义文件名类型，如果是，就需要重新初始化开始时每个结果集的文件名（多个结果集的情况）
            if (helper.getTransferFileName() instanceof WebDataTransferName.CustomResultSetName) {
                settings.setOutputFilePattern(helper.getCustomExportFile(fileSize).getFileName().toString());
            } else {
                settings.setOutputFilePattern(exportFile.getFileName().toString());
            }
            settings.setSplitOutFiles(!isAsyncExecute);
            settings.setMaxOutFileSize(exportSize * 1000 * 1000);
            if (processor.getId().equals(ExportType.CSV.getId())) {
                settings.setOutputEncodingBOM(ByteOrderMark.UTF_8.getCharsetName());
            }
            if (StringUtils.isNotBlank(fileCharset)) {
                settings.setOutputEncoding(fileCharset);
            }
            if (contextInfo.getDataSource().getInfo().supportsMergeExportFile()) {
                settings.setAppendToFileEnd(true);
            }

            for (DBPPropertyDescriptor prop : processor.getProperties()) {
                properties.put(prop.getId(), prop.getDefaultValue());
            }

            properties.put("dateFormat", resultFormat.getExcelDateTimeFormat());

            consumer.initTransfer(
                    dataContainer,
                    settings,
                    new IDataTransferConsumer.TransferParameters(processor.isBinaryFormat(), processor.isHTMLFormat(), helper.getExportDate()),
                    exporter,
                    properties);

            DatabaseTransferProducer producer = null;
            if (isAsyncExecute) {
                producer = new AsyncExecuteTransferProducer(
                        dataContainer,
                        null
                );
            } else {
                producer = new DatabaseTransferProducer(
                        dataContainer,
                        null);
            }
            DatabaseProducerSettings producerSettings = new DatabaseProducerSettings();
            producerSettings.setExtractType(DatabaseProducerSettings.ExtractType.SINGLE_QUERY);
            producerSettings.setQueryRowCount(false);
            producerSettings.setOpenNewConnections(false);
            producerSettings.setFetchSize(exportRows);
            producerSettings.setOffset(offset);
            producerSettings.setData(data);
            producerSettings.setExportLimit(exportLimit);
            producerSettings.setStage((int) properties.getOrDefault(DTConstants.RESULT_STAGE, 0));

            producer.transferData(monitor, consumer, null, producerSettings, null, contextInfo.recoverBefore(), contextInfo.recoverAfter());

            queryText = producer.getStatistics().getQueryText();
            error = producer.getStatistics().getError();

            if (isAsyncExecute) {
                AsyncExecuteTransferProducer asyncProducer = (AsyncExecuteTransferProducer) producer;
                queryResult = asyncProducer.getProducerStatistics().getWebSQLQueryResult();
            }

            if (resultsInfo != null) {
                resultsInfo.setRowSize(producer.getStatistics().getRowsFetched());
                resultsInfo.setExportLimit(producer.getStatistics().isExportLimit());
            }

            consumer.finishTransfer(monitor, false);

            if (dataResultVisitor != null) {
                DBDAttributeBinding[] columnBindings = consumer.getColumnBindings();
                if (columnBindings != null) {
                    sensitiveSelect.set(
                            Arrays.stream(columnBindings)
                                    .filter(Objects::nonNull)
                                    .anyMatch(DBDAttributeBinding::isDesensitized)
                    );
                }
            }
            //如果是自定义文件名类型，就需要在consumer执行完成后，把真实的文件名设置到helper中，
            //以便上传的时候能找到文件，并且根据真实的文件名返回真实的文件路径
            if (helper.getTransferFileName() instanceof WebDataTransferName.CustomResultSetName) {
                helper.setTransferFileName(new WebDataTransferName.CustomResultSetName(consumer.getCustomFileName()));
                exportFile = helper.getDataExportFolder().resolve(consumer.getCustomFileName());
            }

            return exportFile.toFile();
        } catch (Exception e) {
            contextInfo.setLastException(e);

            if (Files.exists(exportFile)) {
                try {
                    Files.delete(exportFile);
                } catch (IOException ex) {
                    log.error("Error deleting export file " + exportFile.toAbsolutePath(), e);
                }
            } else {
                for (Throwable cause = e; cause != null; cause = cause.getCause()) {
                    if (cause instanceof FileNotFoundException && cause.getMessage().contains("File name too long")) {
                        throw new DBException("文件名过长，导出失败", e);
                    }
                }
            }

            contextInfo.confirmInterrupted(e);

            if (e instanceof DBException) {
                throw e;
            }
            throw new DBException("Error exporting data: " + e.getMessage(), e);
        }

    }

    private String getProcessorFileExtension(DataTransferProcessorDescriptor processor) {
        DBPPropertyDescriptor extProperty = processor.getProperty("extension");
        String ext = extProperty == null ? processor.getAppFileExtension() : CommonUtils.toString(extProperty.getDefaultValue(), null);
        return CommonUtils.isEmpty(ext) ? "data" : ext;
    }

}
