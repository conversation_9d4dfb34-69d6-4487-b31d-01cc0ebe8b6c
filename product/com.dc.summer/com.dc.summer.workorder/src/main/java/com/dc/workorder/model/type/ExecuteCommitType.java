package com.dc.workorder.model.type;

import lombok.AllArgsConstructor;
import lombok.Getter;

@AllArgsConstructor
@Getter
public enum ExecuteCommitType {
    /**
     * 遇到错误终止并提交
     */
    ERROR_COMMIT(1),
    /**
     * 遇到错误终止并回滚
     */
    ERROR_ROLLBACK(2),
    /**
     * 遇到错误继续
     */
    ERROR_CONTINUE(3);

    private final Integer value;

    public static ExecuteCommitType of(Integer value) {
        for (ExecuteCommitType executeCommitType : ExecuteCommitType.values()) {
            if (executeCommitType.value.equals(value)) {
                return executeCommitType;
            }
        }
        return null;
    }

}
