package com.dc.workorder.utils;

import com.dc.repository.mysql.column.OrderApplyContent;
import com.dc.springboot.core.model.type.SqlScriptType;
import com.dc.springboot.core.model.type.OrderTypeKey;

public class SqlScriptUtils {

    private SqlScriptUtils() {
    }

    public static String getScriptName(OrderApplyContent orderApplyContent, OrderTypeKey orderTypeKey, SqlScriptType sqlScriptType) {

        String scriptName;

        if (orderTypeKey.isImportData()) {
            scriptName = sqlScriptType == SqlScriptType.ROLLBACK_SCRIPT ? orderApplyContent.getRollbackImport().getName() : orderApplyContent.getSqlImport().getName();
        } else {
            scriptName = sqlScriptType == SqlScriptType.ROLLBACK_SCRIPT ? orderApplyContent.getRollbackScripts().get(0).getName() : orderApplyContent.getSqlScripts().get(0).getName();
        }

        return scriptName;
    }

}
