package com.dc.workorder.service.parser;


import com.dc.springboot.core.model.parser.ParserParamDto;
import com.dc.summer.parser.sql.constants.OperationAuthConstant;
import com.dc.summer.parser.sql.constants.SqlConstant;
import com.dc.summer.parser.sql.model.*;
import com.dc.summer.parser.utils.model.*;
import com.dc.summer.parser.utils.*;
import com.dc.type.DatabaseType;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.*;

@Slf4j
public class SqlActionParser {

    public List<SqlParseModel> sqlParser(ParserParamDto preCheckParamDTO) {

        // 执行步骤：先splitSql再preCheckSql再executeSql
        List<SqlParseModel> returnList = new ArrayList<>();
        try {
            if (null != preCheckParamDTO) {

                List<SqlParseResult> sqlStatements = SqlParserUtil.parser(preCheckParamDTO.getSql(), preCheckParamDTO.getDbType());
                if (sqlStatements.size() == 0) {
                    return returnList;
                }

                List<SqlParseStrengthen> list = SqlSplitUtil.mergeErrorSplitSql(sqlStatements, preCheckParamDTO.getDbType());
                for (int k = 0; k < list.size(); k++) {
                    SqlParseStrengthen sqlParserStrengthenModel = list.get(k);

                    SqlParseModel sqlParserModel = new SqlParseModel();

                    sqlParserModel.setSql(sqlParserStrengthenModel.getSql());
                    sqlParserModel.settCustomSqlStatement(sqlParserStrengthenModel.gettCustomSqlStatement());
                    sqlParserModel.setAst(sqlParserStrengthenModel.getAst());
                    sqlParserModel.setOperation(sqlParserStrengthenModel.getOperation());
                    sqlParserModel.setCustomSqlStatement(sqlParserStrengthenModel.getCustomSqlStatement());

                    this.actionParser(preCheckParamDTO, sqlParserModel);

                    // operation修改
                    if (sqlParserModel.getAction().isSelectInto()) {
                        sqlParserModel.setOperation(SqlConstant.KEY_CREATE);
                    }
                    if (StringUtils.isNotBlank(sqlParserModel.getAction().getCkOperation())) {
                        sqlParserModel.setOperation(sqlParserModel.getAction().getCkOperation());
                    }
                    if (sqlParserModel.getAction().getExplainOperation() != null) {
                        sqlParserModel.setOperation(sqlParserModel.getAction().getExplainOperation());
                    }

                    returnList.add(sqlParserModel);

                }
            }
        } catch (Exception e) {
            log.error("sqlParser error : ", e);
            throw new RuntimeException(e.getMessage());
        }

        return returnList;

    }

    public List<SqlAuthModel> buildSqlAuthModelList(Set<String> tables, String operation, ParserParamDto paramDTO, String type, boolean canBackup) {
        List<SqlAuthModel> SqlAuthModels = new ArrayList<>();

        for (String table : tables) {
            SqlAuthModel sqlAuthModel = new SqlAuthModel();

            String splitRegex = CommonUtil.useColonSplit(paramDTO.getDbType()) ? ":" : "\\.";
            String[] split = table.split(splitRegex);

            if (Arrays.asList(DatabaseType.ORACLE.getValue(), DatabaseType.DM.getValue()).contains(paramDTO.getDbType()) && split[split.length - 1].contains("@")) {
                continue; // dbLink不鉴权，放行
            }

            String catalogName = CommonUtil.getTrueCatalogName(paramDTO.getDbType(), split, paramDTO.getCatalogName());
            String schemaName = CommonUtil.getTrueSchemaName(paramDTO.getDbType(), split, paramDTO.getSchemaName());
            String frameworkName = CommonUtil.getTrueFrameworkName(paramDTO.getDbType(), split, paramDTO.getFrameworkName());
            String tableName = CommonUtil.getTrueTableName(paramDTO.getDbType(), split);

            if (DatabaseType.MONGODB.getValue().equals(paramDTO.getDbType())) {
                schemaName = paramDTO.getSchemaName();
                tableName = table;
            }

            sqlAuthModel.setCatalogName(catalogName);
            sqlAuthModel.setSchemaName(schemaName);
            sqlAuthModel.setName(tableName);
            sqlAuthModel.setType(type);
            sqlAuthModel.setFrameworkName(frameworkName);
            sqlAuthModel.setOperation(operation);
            sqlAuthModel.setCanBackup(canBackup);

            SqlAuthModels.add(sqlAuthModel);
        }

        return SqlAuthModels;
    }

    public void actionParser(ParserParamDto paramDTO, SqlParseModel sqlParserModel) {

        List<SqlAuthModel> sqlAuthModels = new ArrayList<>();
        SqlActionModel sqlActionModel = new SqlActionModel();
        sqlParserModel.setSqlAuthModelList(sqlAuthModels);
        sqlParserModel.setAction(sqlActionModel);

        // redis没有对象,只到数据库级别
        if (DatabaseType.REDIS.getValue().equals(paramDTO.getDbType())) {
            // -------------------build SqlAuthModels------------------
            sqlAuthModels.add(getRedisSqlAuthModel(paramDTO, sqlParserModel.getOperation()));
            return;
        }

        // DDL
        if (Arrays.asList(SqlConstant.CALL_DDL_UTIL).contains(sqlParserModel.getOperation())) {
            SqlDdlResult sqlDdlResultModel = DdlUtil.getObject(sqlParserModel, paramDTO.getDbType());

            String objectType = sqlDdlResultModel.getObjectType();
            String[] objectName = CommonUtil.getDdlObjectName(sqlDdlResultModel, paramDTO.getDbType(), objectType);
            List<String> array = CommonUtil.getDDLArray(objectName, objectType, paramDTO.getDbType(), paramDTO.getSchemaName(), paramDTO.getCatalogName());

            MatchParseModel matchParesModel = CommonUtil.dealMatch(array, paramDTO.getDbType(), paramDTO.getFrameworkName(), paramDTO.getSchemaName(), paramDTO.getCatalogName());

            String schemaName = matchParesModel.getSchemaName() == null ? paramDTO.getSchemaName() : matchParesModel.getSchemaName();
            if (Arrays.asList(SqlConstant.KEY_USER, SqlConstant.KEY_DATABASE).contains(objectType.toUpperCase(Locale.ROOT))) {
                schemaName = matchParesModel.getObjectName();
            }

            // -------------------build SqlAuthModels------------------
            SqlAuthModel sqlAuthModel = new SqlAuthModel();
            sqlAuthModels.add(sqlAuthModel);
            sqlAuthModel.setCatalogName(matchParesModel.getCatalogName());
            sqlAuthModel.setSchemaName(schemaName);
            sqlAuthModel.setFrameworkName(matchParesModel.getFrameworkName());
            sqlAuthModel.setName(matchParesModel.getObjectName());
            sqlAuthModel.setType(objectType);
            String operation = sqlParserModel.getOperation();
            String ckOperation = sqlDdlResultModel.getCkOperation(); // clickhouse operation: alter table ... update/delete ...
            if (!ckOperation.isEmpty()) {
                operation = ckOperation;
                sqlActionModel.setCkOperation(ckOperation);
            }
            sqlAuthModel.setOperation(operation);

            if (StringUtils.isNotBlank(sqlAuthModel.getDdlSubdivideOperation())) {
                sqlActionModel.setNeedOtherOperations(CommonUtil.buildNeedOtherOperations(sqlActionModel.getNeedOtherOperations(),
                        sqlAuthModel.getDdlSubdivideOperation().toLowerCase(Locale.ROOT)));
            }

            Set<String> selectTables = new LinkedHashSet<>();
            if (StringUtils.isNotBlank(sqlDdlResultModel.getAsTableName())) {
                selectTables.add(sqlDdlResultModel.getAsTableName()); // create table tab1 as table tab2
            }
            if (StringUtils.isNotBlank(sqlDdlResultModel.getLikeTableName())) {
                selectTables.add(sqlDdlResultModel.getLikeTableName()); // create table tab1 like table tab2
            }
            selectTables.addAll(sqlDdlResultModel.getAsSelectTables()); // create table tab1 as select * from tab2
            selectTables.addAll(sqlDdlResultModel.getCkUpdateSelectTables()); // clickhouse: update语句中带有select

            sqlAuthModels.addAll(buildSqlAuthModelList(selectTables, SqlConstant.KEY_SELECT, paramDTO, SqlConstant.KEY_TABLE, false));

            if (selectTables.size() > 0) {
                sqlActionModel.setNeedOtherOperations(CommonUtil.buildNeedOtherOperations(sqlActionModel.getNeedOtherOperations(), OperationAuthConstant.select));
            }

            Set<String> dropObjects = sqlDdlResultModel.getDropObjects(); // drop view view1,view2...
            sqlAuthModels.addAll(buildSqlAuthModelList(dropObjects, SqlConstant.KEY_DROP, paramDTO, objectType, false));

            Set<String> truncateTables = sqlDdlResultModel.getTruncateTables(); // truncate tab1,tab2...
            sqlAuthModels.addAll(buildSqlAuthModelList(truncateTables, SqlConstant.KEY_TRUNCATE, paramDTO, objectType, false));

            // clickhouse/mysql/impala/hive: rename table s1.tab1 to s2.tab2
            String newObjectName = sqlDdlResultModel.getNewObjectName();
            if (Arrays.asList(DatabaseType.CLICKHOUSE.getValue(), DatabaseType.MYSQL.getValue(), DatabaseType.IMPALA.getValue(),
                    DatabaseType.HIVE.getValue()).contains(paramDTO.getDbType()) && !newObjectName.isEmpty()) {
                String renameNewTableOperation = getRenameNewTableOperation(newObjectName, paramDTO, schemaName);
                if (!renameNewTableOperation.isEmpty()) {
                    Set<String> tables = new LinkedHashSet<>();
                    tables.add(newObjectName);
                    sqlAuthModels.addAll(buildSqlAuthModelList(tables, renameNewTableOperation, paramDTO, SqlConstant.KEY_TABLE, false));

                    sqlActionModel.setNeedOtherOperations(CommonUtil.buildNeedOtherOperations(sqlActionModel.getNeedOtherOperations(), renameNewTableOperation.toLowerCase(Locale.ROOT)));
                    sqlActionModel.setNeedOtherOperations(CommonUtil.buildNeedOtherOperations(sqlActionModel.getNeedOtherOperations(), OperationAuthConstant.create_table));
                }
            }

            // -----------------build sqlActionModel----------------
            Set<String> functions = new LinkedHashSet<>();
            functions.addAll(CommonUtil.getFunctions(sqlDdlResultModel.getAsSelectFunctions())); // create table tab1 as select fun1() from tab2
            functions.addAll(CommonUtil.getFunctions(sqlDdlResultModel.getCkUpdateSelectFunctions())); // clickhouse: update语句中带有select function
            sqlActionModel.setFunctions(functions);

            if (functions.size() > 0) {
                sqlActionModel.setNeedOtherOperations(CommonUtil.buildNeedOtherOperations(sqlActionModel.getNeedOtherOperations(), OperationAuthConstant.select));
            }

            sqlActionModel.setDefaultSchema(matchParesModel.getSchemaName() == null); // 是否不带schemaName
            sqlActionModel.setChangeSchema(sqlDdlResultModel.isChangeSchema()); // 是否切换了schema

            String ckWhere = sqlDdlResultModel.getCkWhere(); // clickhouse: alter table ... update/delete ... where ...
            if (!ckWhere.isEmpty()) {
                sqlActionModel.setCkWhere(ckWhere);
            }

            return;
        }

        // DQL、DML、EXPLAIN
        if (Arrays.asList(SqlConstant.CALL_CRUD_UTIL).contains(sqlParserModel.getOperation())) {

            sqlActionModel = CrudUtil.getObject(sqlParserModel, paramDTO.getDbType());
            sqlParserModel.setAction(sqlActionModel);

            // -------------------build SqlAuthModels------------------
            if (sqlActionModel.isSelectInto() || sqlActionModel.isInsertIntoSelect() || sqlActionModel.isMerge()) {
                String splitRegex = CommonUtil.useColonSplit(paramDTO.getDbType()) ? ":" : "\\.";
                String[] objectName = sqlActionModel.getObjectName().split(splitRegex);
                MatchParseModel matchParesModel = CommonUtil.dealMatch(Arrays.asList(objectName), paramDTO.getDbType(), paramDTO.getFrameworkName(), paramDTO.getSchemaName(), paramDTO.getCatalogName());

                String schemaName = matchParesModel.getSchemaName() == null ? paramDTO.getSchemaName() : matchParesModel.getSchemaName();

                SqlAuthModel sqlAuthModel = new SqlAuthModel();
                sqlAuthModels.add(sqlAuthModel);
                sqlAuthModel.setCatalogName(matchParesModel.getCatalogName());
                sqlAuthModel.setSchemaName(schemaName);
                sqlAuthModel.setFrameworkName(matchParesModel.getFrameworkName());
                sqlAuthModel.setName(matchParesModel.getObjectName());
                sqlAuthModel.setType(SqlConstant.KEY_TABLE);
                String operation = sqlParserModel.getOperation();
                if (sqlActionModel.isSelectInto()) {
                    operation = SqlConstant.KEY_CREATE;
                } else if (sqlActionModel.isMerge()) {
                    operation = SqlConstant.KEY_INSERT;
                }
                sqlAuthModel.setOperation(operation);
            }

            // DML/DQL操作的表
            String operation = sqlParserModel.getOperation();
            if (sqlActionModel.isSelectInto() || sqlActionModel.isInsertIntoSelect() || sqlActionModel.isMerge()) {
                operation = SqlConstant.KEY_SELECT;
            } else if (StringUtils.isNotBlank(sqlActionModel.getExplainOperation())) {
                operation = sqlActionModel.getExplainOperation();
            }

            if (sqlActionModel.getTables() != null) {
                sqlAuthModels.addAll(this.buildSqlAuthModelList(sqlActionModel.getTables(), operation, paramDTO, "TABLE", false));
            }

            Set<String> selectTables = new LinkedHashSet<>();
            // oracle: insert all/first select ... 语句中的表
            if (sqlActionModel.getInsertAllFirstTables() != null) {
                selectTables.addAll(sqlActionModel.getInsertAllFirstTables());
            }
            // where条件中的表
            if (sqlActionModel.getTablesInWhereClause() != null) {
                selectTables.addAll(sqlActionModel.getTablesInWhereClause());
            }
            operation = SqlConstant.KEY_SELECT;
            if (sqlActionModel.isDuplicateKeyUpdate()) {
                operation = SqlConstant.KEY_UPDATE;
            }
            sqlAuthModels.addAll(this.buildSqlAuthModelList(selectTables, operation, paramDTO, "TABLE", false));

            return;
        }

        // CALL、EXEC、SHOW、DESC、VALUES、LOCK、SAVE
        if (Arrays.asList(SqlConstant.CALL_FUNCTION_UTIL).contains(sqlParserModel.getOperation())) {
            SqlCallFunctionResult sqlCallFunctionResultModel = CallFunctionUtil.getObject(sqlParserModel, paramDTO.getDbType());

            String objectType = sqlCallFunctionResultModel.getObjectType();
            String splitRegex = CommonUtil.useColonSplit(paramDTO.getDbType()) ? ":" : "\\.";
            String[] objectName = sqlCallFunctionResultModel.getObjectName().split(splitRegex);

            MatchParseModel matchParesModel = CommonUtil.dealMatch(Arrays.asList(objectName), paramDTO.getDbType(), paramDTO.getFrameworkName(), paramDTO.getSchemaName(), paramDTO.getCatalogName());

            String schemaName = matchParesModel.getSchemaName() == null ? paramDTO.getSchemaName() : matchParesModel.getSchemaName();

            // -------------------build SqlAuthModels------------------
            SqlAuthModel sqlAuthModel = new SqlAuthModel();
            sqlAuthModels.add(sqlAuthModel);
            sqlAuthModel.setCatalogName(matchParesModel.getCatalogName());
            sqlAuthModel.setSchemaName(CommonUtil.replace(schemaName.trim()));
            sqlAuthModel.setFrameworkName(matchParesModel.getFrameworkName());
            sqlAuthModel.setName(CommonUtil.replace(matchParesModel.getObjectName().trim()));
            sqlAuthModel.setType(objectType);
            sqlAuthModel.setOperation(sqlParserModel.getOperation());

            return;
        }

        // use、database
        if (Arrays.asList(SqlConstant.KEY_USE, SqlConstant.KEY_DATABASE).contains(sqlParserModel.getOperation())) {
            SqlCallFunctionResult sqlCallFunctionResultModel = CallFunctionUtil.getUseObject(sqlParserModel);
            String objectType = sqlCallFunctionResultModel.getObjectType();
            String schemaName = sqlCallFunctionResultModel.getObjectName();

            schemaName = schemaName.isEmpty() ? paramDTO.getSchemaName() : schemaName;

            // -------------------build SqlAuthModels------------------
            SqlAuthModel sqlAuthModel = new SqlAuthModel();
            sqlAuthModels.add(sqlAuthModel);
            sqlAuthModel.setName(CommonUtil.replace(schemaName.trim()));
            sqlAuthModel.setSchemaName(CommonUtil.replace(schemaName.trim()));
            sqlAuthModel.setType(objectType);
            sqlAuthModel.setOperation(sqlParserModel.getOperation());

            return;
        }

        // set
        if (SqlConstant.KEY_SET.equalsIgnoreCase(sqlParserModel.getOperation())) {
            SqlCallFunctionResult sqlCallFunctionResultModel = CallFunctionUtil.getObject(sqlParserModel, paramDTO.getDbType());

            String objectType = sqlCallFunctionResultModel.getObjectType();

            String splitRegex = CommonUtil.useColonSplit(paramDTO.getDbType()) ? ":" : "\\.";
            String[] objectName = sqlCallFunctionResultModel.getObjectName().split(splitRegex);

            MatchParseModel matchParesModel = CommonUtil.dealMatch(Arrays.asList(objectName), paramDTO.getDbType(), paramDTO.getFrameworkName(), paramDTO.getSchemaName(), paramDTO.getCatalogName());

            String schemaName = matchParesModel.getSchemaName() == null ? paramDTO.getSchemaName() : matchParesModel.getSchemaName();

            // -------------------build SqlAuthModels------------------
            SqlAuthModel sqlAuthModel = new SqlAuthModel();
            sqlAuthModels.add(sqlAuthModel);

            sqlAuthModel.setCatalogName(matchParesModel.getCatalogName());
            sqlAuthModel.setSchemaName(CommonUtil.replace(schemaName.trim()));
            sqlAuthModel.setFrameworkName(matchParesModel.getFrameworkName());
            sqlAuthModel.setName(CommonUtil.replace(matchParesModel.getObjectName().trim()));
            sqlAuthModel.setType(objectType);
            sqlAuthModel.setOperation(sqlParserModel.getOperation());

            return;
        }

        // oracle 定时任务job
        if (Arrays.asList(DatabaseType.ORACLE.getValue(), DatabaseType.OCEAN_BASE_ORACLE.getValue()).contains(paramDTO.getDbType())
                && StringUtils.isNotBlank(paramDTO.getPreOperation()) && SqlConstant.KEY_ORACLE_JOB.equalsIgnoreCase(sqlParserModel.getOperation())) {
            sqlParserModel.setOperation(paramDTO.getPreOperation().toUpperCase());
            // -------------------build SqlAuthModels------------------
            sqlAuthModels.add(getJobSqlAuthModel(paramDTO, sqlParserModel.getOperation()));
            return;
        }

    }

    private String getRenameNewTableOperation(String newObjectName, ParserParamDto preCheckParamDTO, String schemaName) {
        String[] newObjectNameArr = newObjectName.split("\\.");
        MatchParseModel matchParesModel = CommonUtil.dealMatch(Arrays.asList(newObjectNameArr), preCheckParamDTO.getDbType(), preCheckParamDTO.getFrameworkName(), preCheckParamDTO.getSchemaName(), preCheckParamDTO.getCatalogName());
        String newSchemaName = matchParesModel.getSchemaName() == null ? preCheckParamDTO.getSchemaName() : matchParesModel.getSchemaName();
        String renameNewTableOperation = "";
        if (!schemaName.equalsIgnoreCase(newSchemaName)) {
            renameNewTableOperation = "CREATE";
        }
        return renameNewTableOperation;
    }

    public SqlAuthModel getJobSqlAuthModel(ParserParamDto paramDTO, String operation) {

        SqlAuthModel sqlAuthModel = new SqlAuthModel();
        sqlAuthModel.setCatalogName(paramDTO.getCatalogName());
        sqlAuthModel.setSchemaName(paramDTO.getSchemaName());
        sqlAuthModel.setFrameworkName(paramDTO.getFrameworkName());
        sqlAuthModel.setName(SqlConstant.KEY_JOB);
        sqlAuthModel.setType(SqlConstant.KEY_JOB);
        sqlAuthModel.setOperation(operation);
        sqlAuthModel.setSys(1 == paramDTO.getSchema().getIs_sys());
        sqlAuthModel.setCharset(paramDTO.getSchema().getCharset());
        sqlAuthModel.setSchemaUniqueKey(paramDTO.getSchema().getUnique_key());

        return sqlAuthModel;
    }

    public SqlAuthModel getRedisSqlAuthModel(ParserParamDto preCheckParamDTO, String operation) {

        SqlAuthModel sqlAuthModel = new SqlAuthModel();
        sqlAuthModel.setName("");
        sqlAuthModel.setSchemaName(preCheckParamDTO.getSchemaName());
        sqlAuthModel.setType("");
        sqlAuthModel.setOperation(operation);

        return sqlAuthModel;
    }


}
