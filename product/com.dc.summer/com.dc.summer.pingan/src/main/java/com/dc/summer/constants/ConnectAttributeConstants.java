package com.dc.summer.constants;

import java.util.Map;

/**
 * 实例属性常量
 */
public class ConnectAttributeConstants {

    //是否科技DBA运维
    public static final String IS_MANAGED = "is_managed";
    //主管DA
    public static final String MANAGER_DA = "manager_da";
    //备选DA
    public static final String BACK_DA = "back_da";
    //基础架构类型
    public static final String INFRA_TYPE = "infra_type";
    //状态
    public static final String STATUS = "status";
    //默认角色
    public static final String DEFAULT_ROLE = "default_role";
    //应用接口人
    public static final String SERVICE_USER = "service_user";
    //数据库版本
    public static final String DATABASE_VERSION = "database_version";
    //Cyberark实体名
    public static final String CYBERARK_ENTITY_NAME = "cyberark_entity_name";
    //cyberark用户名
    public static final String CYBERARK_USERNAME = "cyberark_username";
    //架构类型
    public static final String ARCHITECTURE_TYPE = "architecture_type";
    //实体UUID
    public static final String ENTITY_UUID = "entity_uuid";
    //是否分级分类
    public static final String IS_CLASSIFICATION = "is_classification";
    //是否明文复制
    public static final String IS_COPY_AUTH = "is_copy_auth";
    //创建方式
    public static final String CREATE_METHOD = "create_method";
    //部署生态
    public static final String DEPLOY_ECOLOGY = "deploy_ecology";
    //系统名称
    public static final String SYSTEM_NAME = "system_name";
    //实例UUID
    public static final String INSTANCE_UUID = "instance_uuid";
    //是否分片
    public static final String IS_SEPARATE = "is_separate";
    //BU
    public static final String BU = "bu";


    public static final Map<String, String> getAttributeZH() {
        return Map.ofEntries(
                Map.entry(IS_MANAGED, "是否科技DBA运维"),
                Map.entry(MANAGER_DA, "主管DA"),
                Map.entry(BACK_DA, "备选DA"),
                Map.entry(INFRA_TYPE, "基础架构类型"),
                Map.entry(INFRA_TYPE, "基础架构类型"),
                Map.entry(STATUS, "状态"),
                Map.entry(DEFAULT_ROLE, "默认角色"),
                Map.entry(SERVICE_USER, "应用接口人"),
                Map.entry(DATABASE_VERSION, "数据库版本"),
                Map.entry(CYBERARK_ENTITY_NAME, "Cyberark实体名"),
                Map.entry(CYBERARK_USERNAME, "cyberark用户名"),
                Map.entry(ARCHITECTURE_TYPE, "架构类型"),
                Map.entry(ENTITY_UUID, "实体UUID"),
                Map.entry(IS_CLASSIFICATION, "是否分级分类"),
                Map.entry(IS_COPY_AUTH, "是否明文复制"),
                Map.entry(CREATE_METHOD, "创建方式"),
                Map.entry(DEPLOY_ECOLOGY, "部署生态"),
                Map.entry(SYSTEM_NAME, "系统名称"),
                Map.entry(INSTANCE_UUID, "实例UUID"),
                Map.entry(IS_SEPARATE, "是否分片"),
                Map.entry(BU, "BU")
        );
    }
}
