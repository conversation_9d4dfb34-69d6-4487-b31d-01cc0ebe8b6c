package com.dc.summer.service;


import com.baomidou.mybatisplus.extension.service.IService;
import com.dc.summer.model.EntityMessage;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

public interface PaInstanceService extends IService {

    SseEmitter syncInstance(SseEmitter sseEmitter) throws Exception;

    void syncInstanceByEntity(EntityMessage entityMessage) throws Exception;

}
