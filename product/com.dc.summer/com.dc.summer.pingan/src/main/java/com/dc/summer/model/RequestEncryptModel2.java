package com.dc.summer.model;

import com.dc.repository.redis.model.EnvConnection;
import com.dc.repository.redis.model.EnvSchema;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
public class RequestEncryptModel2 {
    EnvConnection connection;
    EnvSchema schema;
    String table;
    String columnName;
    /**
     * 需要加密的数据,可传多个
     */
    List<String> plainList;
}
