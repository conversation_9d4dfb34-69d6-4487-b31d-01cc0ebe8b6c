package com.dc.summer.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.dc.repository.mysql.mapper.*;
import com.dc.repository.mysql.model.PaUser;
import com.dc.repository.mysql.model.PaUserChange;
import com.dc.repository.mysql.model.PaUserSuperior;
import com.dc.repository.mysql.model.SysOrg;
import com.dc.summer.service.PaUserService;
import com.dc.summer.service.SysOrgService;
import com.google.gson.Gson;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import javax.annotation.Resource;
import java.io.IOException;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

@Transactional
@Service
@Slf4j
public class SysOrgServiceImpl extends BaseServiceImpl<SysOrgMapper, SysOrg> implements SysOrgService {



}
