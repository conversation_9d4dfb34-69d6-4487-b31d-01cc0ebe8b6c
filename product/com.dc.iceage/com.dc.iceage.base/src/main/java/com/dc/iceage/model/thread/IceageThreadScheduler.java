package com.dc.iceage.model.thread;

import com.dc.iceage.config.MetaDataContextHolder;
import com.dc.iceage.model.type.ExecuteType;
import com.dc.springboot.core.model.exception.ServiceException;
import com.dc.springboot.core.model.thread.BaseThreadScheduler;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.stereotype.Component;

import java.util.Objects;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.CountDownLatch;
import java.util.function.IntConsumer;

@Slf4j
@Component
public class IceageThreadScheduler extends BaseThreadScheduler<ExecuteType, String> {

    public void block(ExecuteType executeType, Integer threadNum, IntConsumer consumer) {

        if (threadNum == 1) {
            consumer.accept(0);
            return;
        }

        CopyOnWriteArrayList<Exception> exceptions = new CopyOnWriteArrayList<>();

        CountDownLatch latch = new CountDownLatch(threadNum);

        String result = getResult();
        for (int num = 0; num < threadNum; num++) {
            final int i = num;
            super.exec(executeType, () -> {
                try {
                    setResult(result);
                    consumer.accept(i);
                } catch (Exception e) {
                    exceptions.add(e);
                    throw e;
                } finally {
                    delResult(result);
                    latch.countDown();
                }
            });
        }

        try {
            latch.await();
        } catch (InterruptedException e) {
            log.error("latch await interrupted.", e);
            Thread.currentThread().interrupt();
        }

        if (!exceptions.isEmpty()) {
            String message = exceptions.stream()
                    .map(ExceptionUtils::getThrowableList)
                    .filter(CollectionUtils::isNotEmpty)
                    .map(list -> list.stream()
                            .map(Throwable::getMessage)
                            .filter(Objects::nonNull)
                            .reduce((s, s2) -> s + "(" + s2 + ")")
                            .orElse(null))
                    .filter(Objects::nonNull)
                    .distinct()
                    .reduce((e, e2) -> String.format("%s; %s", e, e2))
                    .orElse("Null Exception.");
            ServiceException serviceException = new ServiceException(message);
            exceptions.forEach(serviceException::addSuppressed);
            throw serviceException;
        }

    }

    @Override
    protected void delResult(String result) {
        MetaDataContextHolder.contextHolder.remove();
    }

    @Override
    protected void setResult(String result) {
        MetaDataContextHolder.contextHolder.set(result);
    }

    @Override
    protected String getResult() {
        return MetaDataContextHolder.contextHolder.get();
    }
}
