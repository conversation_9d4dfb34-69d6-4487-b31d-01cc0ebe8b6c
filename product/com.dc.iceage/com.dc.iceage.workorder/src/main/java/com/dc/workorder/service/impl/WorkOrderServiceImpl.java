package com.dc.workorder.service.impl;

import com.dc.proxy.service.MetaDataService;
import com.dc.repository.mysql.column.OrderApplyContent;
import com.dc.springboot.core.model.database.TableFieldsMessage;
import com.dc.springboot.core.model.exception.IdempotentException;
import com.dc.springboot.core.model.exception.ServiceException;
import com.dc.springboot.core.model.type.OrderTypeKey;
import com.dc.springboot.core.model.type.OriginType;
import com.dc.springboot.core.model.type.ParserExecuteType;
import com.dc.springboot.core.model.workorder.ScriptWarehouseMessage;
import com.dc.springboot.core.model.workorder.WorkOrderBatchMessage;
import com.dc.springboot.core.model.workorder.WorkOrderMessage;
import com.dc.summer.exec.handler.DataSourceConnectionHandler;
import com.dc.summer.exec.model.data.ConnectionConfiguration;
import com.dc.summer.model.DBPDataSourceInfo;
import com.dc.summer.model.sql.SqlFieldData;
import com.dc.workorder.service.check.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.function.Supplier;
import java.util.stream.Collectors;

@Slf4j
@Service
public class WorkOrderServiceImpl extends AbstractWorkOrderService {

    @Resource
    private WorkOrderCheckService workOrderScriptService;

    @Resource
    private MetaDataService metaDataService;

    private static final String NOT_SUPPORTS_TYPE = "当前方法不支持此工单类型： ";

    @Override
    public Runnable asyncScriptChanges(WorkOrderMessage message) {

        if (!waitSet.add(message.getWorkOrderModel().getId())) {
            throw new IdempotentException("脚本变更工单正在执行中");
        }

        return () -> super.checking(message.getWorkOrderModel(), (order, orderTypeKey) -> {

            if (orderTypeKey == OrderTypeKey.APPLY_SQL) {
                return workOrderScriptService.saveSingleWorkOrderScript(
                        order,
                        new WorkOrderCheckSqlContent(OriginType.SCRIPT, List.of(ParserExecuteType.SQL_AFFECTED_ROWS, ParserExecuteType.SQL_BACKUP, ParserExecuteType.PRIVILEGES_MANAGEMENT, ParserExecuteType.SQL_AUDIT))
                );
            } else if (orderTypeKey == OrderTypeKey.APPLY_SQL_SCRIPT) {
                return workOrderScriptService.saveBatchWorkOrderScript(
                        order,
                        new WorkOrderCheckSqlScript(OriginType.SCRIPT, List.of(ParserExecuteType.SQL_AFFECTED_ROWS, ParserExecuteType.SQL_BACKUP, ParserExecuteType.PRIVILEGES_MANAGEMENT, ParserExecuteType.SQL_AUDIT))
                );
            } else {
                throw new ServiceException(NOT_SUPPORTS_TYPE + orderTypeKey.name());
            }

        });

    }

    @Override
    public Runnable asyncScriptWarehouse(ScriptWarehouseMessage message) {

        if (!waitSet.add(message.getWorkOrderModel().getId())) {
            throw new IdempotentException("脚本仓库工单正在执行中");
        }

        return () -> super.checking(message.getWorkOrderModel(), (order, orderTypeKey) -> {

            if (orderTypeKey == OrderTypeKey.SCRIPT_WAREHOUSE_UPDATE) {
                return workOrderScriptService.saveSingleWorkOrderScript(
                        order,
                        new WorkOrderCheckSqlContent(null, null)
                );
            } else if (orderTypeKey == OrderTypeKey.SCRIPT_WAREHOUSE_SCRIPT) {
                return workOrderScriptService.saveBatchWorkOrderScript(
                        order,
                        new WorkOrderCheckSqlScript(null, null)
                );
            } else if (orderTypeKey == OrderTypeKey.SCRIPT_WAREHOUSE_TEXT) {
                return workOrderScriptService.saveBatchWorkOrderScript(
                        order,
                        new WorkOrderCheckSqlContent(null, null)
                );
            } else {
                throw new ServiceException(NOT_SUPPORTS_TYPE + orderTypeKey.name());
            }

        });

    }

    @Override
    public Runnable asyncDataImport(WorkOrderMessage message) {

        if (!waitSet.add(message.getWorkOrderModel().getId())) {
            throw new IdempotentException("数据导入工单正在执行中");
        }

        return () -> super.checking(message.getWorkOrderModel(), (order, orderTypeKey) -> {

            OrderApplyContent applyContent = order.getApply_content();

            OrderApplyContent.FieldMap fieldMapping = applyContent.getField_mapping();

            Supplier<List<SqlFieldData>> supplier = () -> {

                List<SqlFieldData> sqlFieldDataList = null;

                //目前仅支持Oracle  MySQL DB2，其他类型数据库fieldMapping为空，兼容原有的导入逻辑
                if (fieldMapping == null) {

                    String schemaName = applyContent.getSchema_name();
                    if (StringUtils.isNotBlank(schemaName) && schemaName.contains(".")) {
                        schemaName = schemaName.split("\\.")[1];
                    }

                    TableFieldsMessage tableFieldsMessage = new TableFieldsMessage();
                    tableFieldsMessage.setConnectionConfig(message.getConnectionConfig());
                    tableFieldsMessage.setSchemaName(schemaName);
                    tableFieldsMessage.setTableName(applyContent.getTable_name());
                    sqlFieldDataList = metaDataService.getTableFields(tableFieldsMessage);

                } else {
                    sqlFieldDataList = fieldMapping.getFields()
                            .stream()
                            .map(field -> {
                                SqlFieldData sqlFieldData = new SqlFieldData();
                                sqlFieldData.setFieldName(field.is_skip() ? "" : field.getTarget_field());
                                String fieldType = field.getTarget_field_type() == null ? "VARCHAR" : field.getTarget_field_type();
                                sqlFieldData.setFieldType(fieldType);
                                return sqlFieldData;
                            }).collect(Collectors.toList());
                }
                return sqlFieldDataList;
            };

            boolean supported = fieldMapping != null;

            ConnectionConfiguration connectionConfiguration = message.getConnectionConfig().getConnectionConfiguration();
            DataSourceConnectionHandler handler = DataSourceConnectionHandler.handle(connectionConfiguration);
            DBPDataSourceInfo dataSourceInfo = handler.getDataSource().getInfo();

            boolean originStatue;
            if (orderTypeKey == OrderTypeKey.IMPORT_DATA_EXCEL) {
                originStatue = workOrderScriptService.saveSingleWorkOrderImport(
                        order,
                        new WorkOrderCheckSqlExcel(dataSourceInfo,
                                supplier,
                                supported,
                                OriginType.IMPORT,
                                List.of(ParserExecuteType.SQL_BACKUP, ParserExecuteType.SQL_AUDIT)
                        ),
                        false
                );
            } else if (orderTypeKey == OrderTypeKey.IMPORT_DATA_CSV) {
                originStatue = workOrderScriptService.saveSingleWorkOrderImport(
                        order,
                        new WorkOrderCheckSqlCsv(dataSourceInfo,
                                supplier,
                                supported,
                                OriginType.IMPORT,
                                List.of(ParserExecuteType.SQL_BACKUP, ParserExecuteType.SQL_AUDIT)
                        ),
                        false
                );
            } else if (orderTypeKey == OrderTypeKey.IMPORT_DATA_TEXT) {
                originStatue = workOrderScriptService.saveSingleWorkOrderImport(
                        order,
                        new WorkOrderCheckSqlText(dataSourceInfo,
                                supplier,
                                supported,
                                OriginType.IMPORT,
                                List.of(ParserExecuteType.SQL_BACKUP, ParserExecuteType.SQL_AUDIT)
                        ),
                        false
                );
            } else {
                throw new ServiceException(NOT_SUPPORTS_TYPE + orderTypeKey.name());
            }
            boolean backStatue = workOrderScriptService.saveSingleWorkOrderImport(
                    order,
                    new WorkOrderCheckSqlScript(OriginType.IMPORT, List.of(ParserExecuteType.SQL_BACKUP, ParserExecuteType.SQL_AFFECTED_ROWS, ParserExecuteType.SQL_AUDIT)),
                    true
            );
            return originStatue && backStatue;

        });
    }

    @Override
    public Runnable asyncResultExport(WorkOrderMessage message) {

        if (!waitSet.add(message.getWorkOrderModel().getId())) {
            throw new IdempotentException("结果导出工单正在执行中");
        }

        return () -> super.checking(message.getWorkOrderModel(), (order, orderTypeKey) -> {

            if (orderTypeKey == OrderTypeKey.EXPORT_SQL || orderTypeKey == OrderTypeKey.EOA_EXPORT_SQL) {
                return workOrderScriptService.saveSingleWorkOrderScript(
                        order,
                        new WorkOrderCheckSqlContent(OriginType.ORDER_EXPORT, List.of(ParserExecuteType.DATA_MASK_PARSER))
                );
            } else {
                throw new ServiceException(NOT_SUPPORTS_TYPE + orderTypeKey.name());
            }

        });

    }

    @Override
    public Runnable asyncPrivilegeChange(WorkOrderMessage message) {

        if (!waitSet.add(message.getWorkOrderModel().getId())) {
            throw new IdempotentException("账号权限变更工单正在执行中");
        }

        return () -> super.checking(message.getWorkOrderModel(), (order, orderTypeKey) -> {
            if (orderTypeKey == OrderTypeKey.PRIVILEGE_CHANGE) {
                return workOrderScriptService.genAndSaveWorkOrderScript(
                        order,
                        new WorkOrderCheckSqlPrivilege(OriginType.PRIVILEGE_CHANGE, List.of(ParserExecuteType.PRIVILEGES_MANAGEMENT), message.getConnectionConfig())
                );
            } else if (orderTypeKey == OrderTypeKey.ACCOUNT_APPLICATION) {
                return workOrderScriptService.genAndSaveWorkOrderScript(
                        order,
                        new WorkOrderAccountAccept(OriginType.ACCOUNT_APPLICATION, List.of(ParserExecuteType.PRIVILEGES_MANAGEMENT), message.getConnectionConfig())
                );
            } else {
                throw new ServiceException(NOT_SUPPORTS_TYPE + orderTypeKey.name());
            }
        });
    }

    @Override
    public Runnable asyncScriptChangesBatch(WorkOrderBatchMessage message) {
        waitSet.add(message.getWorkOrderModel().getId());

        return () -> super.checking(message.getWorkOrderModel(), (order, orderTypeKey) -> {
            if (orderTypeKey == OrderTypeKey.APPLY_SQL_SCRIPT_BATCH) {
                return workOrderScriptService.saveWorkOrderScriptBatchSchema(
                        order,
                        message
                );
            } else {
                throw new ServiceException(NOT_SUPPORTS_TYPE + orderTypeKey.name());
            }
        });
    }

}
