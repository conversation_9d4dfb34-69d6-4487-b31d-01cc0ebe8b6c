package com.dc.workorder.controller;

import com.dc.iceage.model.thread.IceageThreadScheduler;
import com.dc.iceage.model.type.ExecuteType;
import com.dc.springboot.core.model.data.Result;
import com.dc.springboot.core.model.workorder.ScriptWarehouseMessage;
import com.dc.springboot.core.model.workorder.WorkOrderBatchMessage;
import com.dc.springboot.core.model.workorder.WorkOrderMessage;
import com.dc.workorder.service.WorkOrderService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

@Api(tags = "工单控制器")
@Slf4j
@RequestMapping("/work-order")
public class WorkOrderController {

    @Resource
    private WorkOrderService workOrderService;
    
    @Resource
    private IceageThreadScheduler scheduler;

    @ApiOperation("脚本变更工单")
    @PostMapping(value = {"/script-changes"})
    public Result<Object> scriptChanges(@RequestBody @Valid WorkOrderMessage message) {
        scheduler.exec(ExecuteType.SCRIPT_CHANGES, workOrderService.asyncScriptChanges(message));
        return Result.success();
    }

    @ApiOperation("脚本仓库工单")
    @PostMapping(value = {"/script-warehouse"})
    public Result<Object> scriptWarehouse(@RequestBody @Valid ScriptWarehouseMessage message) {
        scheduler.exec(ExecuteType.SCRIPT_WAREHOUSE, workOrderService.asyncScriptWarehouse(message));
        return Result.success();
    }

    @ApiOperation("数据导入工单")
    @PostMapping(value = {"/data-import"})
    public Result<Object> dataImport(@RequestBody @Valid WorkOrderMessage message) {
        scheduler.exec(ExecuteType.DATA_IMPORT, workOrderService.asyncDataImport(message));
        return Result.success();
    }

    @ApiOperation("结果集导出工单")
    @PostMapping(value = {"/result-export"})
    public Result<Object> resultExport(@RequestBody @Valid WorkOrderMessage message) {
        scheduler.exec(ExecuteType.RESULT_EXPORT, workOrderService.asyncResultExport(message));
        return Result.success();
    }

    @ApiOperation("权限变更工单")
    @PostMapping(value = {"/privilege-change"})
    public Result<Object> privilegeChange(@RequestBody @Valid WorkOrderMessage message) {
        scheduler.exec(ExecuteType.PRIVILEGE_CHANGE, workOrderService.asyncPrivilegeChange(message));
        return Result.success();
    }

    @ApiOperation("批量变更工单")
    @PostMapping(value = {"/script-changes-batch"})
    public Result<Object> scriptChangesBatch(@RequestBody @Valid WorkOrderBatchMessage message) {
        scheduler.exec(ExecuteType.SCRIPT_CHANGES_BATCH, workOrderService.asyncScriptChangesBatch(message));
        return Result.success();
    }

}
