package com.dc.parser.model.chain.impl;

import com.dc.parser.model.chain.SqlCheckParserChain;
import com.dc.parser.service.SqlAffectedRowsService;
import com.dc.parser.service.sql.WebSQLParserInfo;
import com.dc.parser.util.ExecuteSqlUtil;
import com.dc.repository.mysql.mapper.UserLimitMapper;
import com.dc.repository.mysql.model.UserLimit;
import com.dc.springboot.core.component.Resource;
import com.dc.springboot.core.model.parser.ParserParamDto;
import com.dc.springboot.core.model.parser.dto.ParamsConfigDto;
import com.dc.springboot.core.model.result.WebSQLParserResult;
import com.dc.springboot.core.model.type.OriginType;
import com.dc.springboot.core.model.type.SqlExecuteStatus;
import com.dc.summer.parser.sql.constants.SqlConstant;
import com.dc.summer.parser.utils.model.SqlParseModel;
import com.dc.type.DatabaseType;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.*;

@Slf4j
public class SqlAffectedRowsChain extends SqlCheckParserChain {

    private final SqlAffectedRowsService sqlAffectedRowsService = Resource.getBeanRequireNonNull(SqlAffectedRowsService.class);

    public SqlAffectedRowsChain(ParserParamDto parserParamDto, SqlParseModel sqlParseModel, WebSQLParserInfo webSQLParserInfo) {
        super(parserParamDto, sqlParseModel, webSQLParserInfo);
    }

    @Override
    public boolean proceed(WebSQLParserResult webSQLParserResult) {
        try {

            // 工单只需要 sql 影响行数
            OriginType originType = OriginType.of(parserParamDto.getOrigin());
            if (originType == OriginType.SCRIPT || originType == OriginType.IMPORT) {
                int rows = sqlAffectedRowsService.getRows(parserParamDto, sqlParseModel);
                webSQLParserResult.setAffectedRows(rows);
                return true;
            }

            // es、hBase不受影响行数限制
            if (Arrays.asList(DatabaseType.ELASTIC_SEARCH.getValue(), DatabaseType.H_BASE.getValue()).contains(parserParamDto.getDbType())) {
                return true;
            }

            int rowsLimit = this.getRowsLimit(parserParamDto, sqlParseModel);
            webSQLParserResult.setRowsLimit(rowsLimit);

            int exportLimit = this.getLocalExportRowsLimit(parserParamDto, sqlParseModel);
            webSQLParserResult.setExportLimit(exportLimit);

            // 0表示无限制
            if (0 == rowsLimit) {
                return true;
            }

            // explain/select function/select sequence不需要查影响行数
            if (sqlParseModel.getAction().getExplainOperation() != null || sqlParseModel.getAction().isSelectFunction()
                    || sqlParseModel.getAction().isSelectSequence() || sqlParseModel.getAction().isSelectValue()) {
                return true;
            }

            if (Arrays.asList(SqlConstant.KEY_UPDATE, SqlConstant.KEY_DELETE, SqlConstant.KEY_INSERT).contains(sqlParseModel.getOperation())) {
                int rows = sqlAffectedRowsService.getRows(parserParamDto, sqlParseModel);
                webSQLParserResult.setAffectedRows(rows);
                if (rows > rowsLimit) {
                    return buildUpperLimitResult(webSQLParserResult, rowsLimit);
                }
            } else if (sqlParseModel.getAction().isSelectInto()) {
                // sqlserver : select * into student4 from lwc_test_student;commit; // 执行后不commit会锁库
                int rows = ExecuteSqlUtil.getAffectedRows(parserParamDto, "SELECT_INTO", sqlParseModel.gettCustomSqlStatement());
                if (rows > rowsLimit) {
                    return buildUpperLimitResult(webSQLParserResult, rowsLimit);
                }
            } else if (sqlParseModel.getAction().isInsertIntoSelect()) {
                // mysql : replace into t1 select id,name,sale,now() from t2;
                int rows = ExecuteSqlUtil.getAffectedRows(parserParamDto, "INSERT", sqlParseModel.gettCustomSqlStatement());
                if (rows > rowsLimit) {
                    return buildUpperLimitResult(webSQLParserResult, rowsLimit);
                }
            } else {
                // SELECT 分页重构查询
                if (SqlConstant.KEY_SELECT.equalsIgnoreCase(sqlParseModel.getOperation())) {
                    if (parserParamDto.getOffset() != null && parserParamDto.getLimit() != null) {
                        int span = rowsLimit - parserParamDto.getOffset();
                        int limit = parserParamDto.getLimit();
                        if (span < limit) {
                            webSQLParserResult.setIsLimit(true);
                            limit = Math.max(span, 0);
                        }
                        webSQLParserResult.setExecuteLimit(limit);
                    }
                }

            }

        } catch (Exception e) {
            log.error("影响行数，解析异常：", e);
            webSQLParserResult.setStatus(SqlExecuteStatus.FAIL.getValue());
            webSQLParserResult.setMessage("影响行数-解析异常! " + e.getMessage());
            return false;
        }
        
        return true;
    }

    private int getLocalExportRowsLimit(ParserParamDto parserParamDto, SqlParseModel sqlParseModel) {
        String operation = sqlParseModel.getOperation();
        if (SqlConstant.KEY_SELECT.equalsIgnoreCase(operation)) {
            Integer instanceSecurityRuleExportLimit = null; //实例安全规则的本地导出行数最大限制
            Integer userRuleExportLimit = null; //用户规则的本地导出行数最大限制

            //尝试赋值 实例安全规则的本地导出最大行数
            if (webSQLParserInfo.getParamsConfig() != null) {
                ParamsConfigDto paramsConfig = webSQLParserInfo.getParamsConfig();
                String exportLimit = paramsConfig.getExportLimit();
                if (exportLimit != null) {
                    instanceSecurityRuleExportLimit = Integer.parseInt(exportLimit);
                }
            }
            //尝试赋值 用户规则的本地导出最大行数
            UserLimitMapper userLimitMapper = Resource.getBeanRequireNonNull(UserLimitMapper.class);
            UserLimit userLimit = userLimitMapper.getUserLimitByPdKey(parserParamDto.getUserId(), "export_limit");
            if (userLimit != null && userLimit.getPd_value() != null) {
                userRuleExportLimit = Integer.parseInt(userLimit.getPd_value());
            }

            //考虑两者优先级后，决定返回值。
            if (userRuleExportLimit != null) {
                return userRuleExportLimit;
            } else if (instanceSecurityRuleExportLimit != null) {
                return instanceSecurityRuleExportLimit;
            }

        }
        return 0;
    }

    private static boolean buildUpperLimitResult(WebSQLParserResult webSQLParserResult, int rowsLimit) {
        webSQLParserResult.setIsLimit(true);
        webSQLParserResult.setStatus(SqlExecuteStatus.UPPER_LIMIT.getValue());
        webSQLParserResult.setMessage("执行失败，该操作影响行数大于" + rowsLimit + "!");
        return false;
    }

    public int getRowsLimit(ParserParamDto paramDTO, SqlParseModel sqlParserModel) {
        String operation;
        if (Arrays.asList(SqlConstant.KEY_UPDATE, SqlConstant.KEY_DELETE, SqlConstant.KEY_INSERT).contains(sqlParserModel.getOperation())) {
            operation = sqlParserModel.getOperation().toLowerCase();
        } else if (sqlParserModel.getAction().isSelectInto()) {
            operation = "insert"; // sqlserver : select * into student4 from lwc_test_student;commit; // 执行后不commit会锁库
        } else if (sqlParserModel.getAction().isInsertIntoSelect()) {
            operation = "insert"; // mysql : replace into t1 select id,name,sale,now() from t2;
        } else {
            operation = "select";
        }

        if (webSQLParserInfo.getParamsConfig() != null) {
            return getRowsLimitByEnv(operation, webSQLParserInfo.getParamsConfig());
        } else {
            return getRowsLimitByDB(operation, paramDTO, webSQLParserInfo);
        }
    }

    private static int getRowsLimitByEnv(String operation, ParamsConfigDto paramsConfig) {
        try {
            if (paramsConfig.getUpdateLimit() != null && SqlConstant.KEY_UPDATE.equalsIgnoreCase(operation)) {
                return Integer.parseInt(paramsConfig.getUpdateLimit());
            } else if (paramsConfig.getDeleteLimit() != null && SqlConstant.KEY_DELETE.equalsIgnoreCase(operation)) {
                return Integer.parseInt(paramsConfig.getDeleteLimit());
            } else if (paramsConfig.getInsertLimit() != null && SqlConstant.KEY_INSERT.equalsIgnoreCase(operation)) {
                return Integer.parseInt(paramsConfig.getInsertLimit());
            } else if (paramsConfig.getSelectLimit() != null && SqlConstant.KEY_SELECT.equalsIgnoreCase(operation)) {
                return Integer.parseInt(paramsConfig.getSelectLimit());
            }
        } catch (Exception e) {
            log.error("get env rows limit error!", e);
        }
        return 0;
    }

    private static int getRowsLimitByDB(String operation, ParserParamDto paramDTO, WebSQLParserInfo webSQLParserInfo) {
        try {
            List<UserLimit> userLimit = Resource.getBeanRequireNonNull(UserLimitMapper.class)
                    .getUserLimit(paramDTO.getUserId()); // 获取个人配置

            Map<String, String> userLimitMap = getUserLimitMap(userLimit);

            String limit = operation + "_limit";
            String rowsLimit = userLimitMap.get(limit) != null ? userLimitMap.get(limit) : webSQLParserInfo.getSystemParamConfig().get(limit);
            if (rowsLimit != null) {
                return Integer.parseInt(rowsLimit);
            }
        } catch (Exception e) {
            log.error("get db rows limit error!", e);
        }
        return 0;
    }

    private static Map<String, String> getUserLimitMap(List<UserLimit> userLimit) {
        Map<String, String> userLimitMap = new HashMap<>();
        if (userLimit != null) {
            for (UserLimit limit : userLimit) {
                if (StringUtils.isNotBlank(limit.getPd_value())) {
                    userLimitMap.put(limit.getPd_key(), limit.getPd_value());
                }
            }
        }
        return userLimitMap;
    }

}
