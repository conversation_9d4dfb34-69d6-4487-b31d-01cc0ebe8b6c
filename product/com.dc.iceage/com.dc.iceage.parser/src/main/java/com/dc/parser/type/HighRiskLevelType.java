package com.dc.parser.type;

public enum HighRiskLevelType {

    fail(0, "除了高危和有权限之外的情况"),
    success(1, "有权限"),
    high_risk(4, "高危"); // 优先级最高

    private final Integer value;
    private final String name;

    HighRiskLevelType(Integer value, String name) {
        this.value = value;
        this.name = name;
    }

    public Integer getValue() {
        return value;
    }

    public String getName() {
        return name;
    }
}
