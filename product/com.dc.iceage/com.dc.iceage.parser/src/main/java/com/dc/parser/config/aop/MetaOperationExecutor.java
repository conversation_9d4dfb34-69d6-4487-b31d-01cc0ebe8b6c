package com.dc.parser.config.aop;

import com.dc.iceage.config.MetaDataContextHolder;
import com.dc.springboot.core.model.parser.ParserParamDto;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

@Slf4j
@Aspect
public class MetaOperationExecutor {

    @Around("@annotation(com.dc.parser.config.annotation.MetaStore)")
    public Object around(ProceedingJoinPoint joinPoint) throws Throwable {
        ConcurrentMap<String, ConcurrentMap<String, Object>> context = MetaDataContextHolder.getContext();
        if (context == null) {
            return joinPoint.proceed();
        }
        try {
            String methodName = joinPoint.getSignature().getName();

            List<String> argList = new ArrayList<>(joinPoint.getArgs().length);
            for (Object arg : joinPoint.getArgs()) {
                if (arg instanceof ParserParamDto) {
                    argList.add(((ParserParamDto) arg).getConnectId());
                } else if (arg == null) {
                    argList.add("_");
                } else {
                    argList.add(arg.toString());
                }
            }
            String key = String.join("-", argList);
            log.debug("Computed cache key {}", key);

            // 使用putIfAbsent来确保线程安全
            Object cachedResult = context.computeIfAbsent(methodName, k -> new ConcurrentHashMap<>())
                    .computeIfAbsent(key, k -> {
                        try {
                            Object proceed = joinPoint.proceed();
                            log.debug("No cache entry for key {}, new entry created", key);
                            return proceed;
                        } catch (Throwable ignored) {
                            // nothing to do here
                            return null;
                        }
                    });

            if (cachedResult != null) {
                log.debug("Cache entry for key {} found", key);
                return cachedResult;
            }
        } catch (Exception e) {
            log.error("元操作执行器执行失败。", e);
        }

        return joinPoint.proceed();
    }
}
