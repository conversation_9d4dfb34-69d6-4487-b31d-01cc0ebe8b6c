package com.dc.parser.model.chain.impl;

import com.dc.parser.component.ParserMapper;
import com.dc.parser.model.chain.SqlCheckChain;
import com.dc.parser.util.ParserConstant;
import com.dc.springboot.core.model.parser.ParserParamDto;
import com.dc.springboot.core.model.result.WebSQLParserResult;
import com.dc.summer.parser.sql.constants.SqlConstant;
import com.dc.summer.parser.utils.CommonUtil;
import com.dc.summer.parser.utils.model.ActionModel;
import com.dc.summer.parser.utils.model.SqlParseModel;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Map;

@Slf4j
public class TransmitPrepareCheckChain extends SqlCheckChain {

    public TransmitPrepareCheckChain(ParserParamDto parserParamDto, SqlParseModel sqlParseModel) {
        super(parserParamDto, sqlParseModel);
    }

    @Override
    public boolean proceed(WebSQLParserResult webSQLParserResult) {

        webSQLParserResult.setSql(sqlParseModel.getSql());

        if (sqlParseModel.getAction().getExplainOperation() != null) {
            webSQLParserResult.setOperation(SqlConstant.KEY_EXPLAIN);
        } else {
            webSQLParserResult.setOperation(sqlParseModel.getOperation());
        }

        if (sqlParseModel.getErrorMessage() != null) {
            log.debug(ParserConstant.NOT_SUPPORT_MESSAGE + ": {}", sqlParseModel.getErrorMessage());
            webSQLParserResult.setMessage(ParserConstant.NOT_SUPPORT_MESSAGE); // 无法解析的sql弹窗提示
        } else {
            webSQLParserResult.setMessage("");
        }

        // 构建actions
        List<ActionModel> actionList = CommonUtil.getActions(parserParamDto.getDbType(), sqlParseModel);
        webSQLParserResult.setActionList(ParserMapper.INSTANCE.toActionModelList(actionList));
        Map<String, List<String>> actions = CommonUtil.mergeActionList(actionList, parserParamDto.getDbType());
        webSQLParserResult.setActions(actions);

        return true;
    }

}
