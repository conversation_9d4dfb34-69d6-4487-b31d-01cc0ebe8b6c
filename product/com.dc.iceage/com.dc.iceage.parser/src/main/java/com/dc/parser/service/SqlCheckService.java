package com.dc.parser.service;


import com.dc.parser.service.sql.WebSQLParserInfo;
import com.dc.springboot.core.model.chain.ChainBuilder;
import com.dc.springboot.core.model.parser.ParserCacheMessage;
import com.dc.springboot.core.model.parser.ParserCheckMessage;
import com.dc.springboot.core.model.parser.ParserParamDto;
import com.dc.springboot.core.model.result.WebSQLParserResult;
import com.dc.summer.parser.utils.model.SqlParseModel;

import java.util.List;

public interface SqlCheckService {

    List<WebSQLParserResult> prepareCheckBatch(ParserCacheMessage message);

    List<WebSQLParserResult> executeCheckBatch(ParserCacheMessage message);

    List<WebSQLParserResult> prepareCheckStream(ParserCheckMessage message);

    List<WebSQLParserResult> executeCheckStream(ParserCheckMessage message);

    List<WebSQLParserResult> crossDatabaseQuery(ParserCacheMessage message);

    ChainBuilder<WebSQLParserResult> prepareCheck(ParserParamDto parserParamDto, SqlParseModel sqlParseModel, WebSQLParserInfo webSQLParserInfo);

    ChainBuilder<WebSQLParserResult> executeCheck(ParserParamDto parserParamDto, SqlParseModel sqlParseModel, WebSQLParserInfo webSQLParserInfo);

    List<WebSQLParserResult> checkAuthResultExportOrder(ParserCheckMessage message);
}
