package com.dc.parser.sensitive.impl;

import com.dc.sqlparser.nodes.TExpression;
import com.dc.sqlparser.nodes.TParseTreeNode;

import java.util.List;

public abstract class AbstractExpression implements SQLExpression {

    private StatementContext context;

    public StatementContext getContext() {
        return context;
    }

    @Override
    public void interpret(StatementContext ctx) {

        this.context = ctx;

        List<TParseTreeNode> nodes = ctx.getNodes();

        for (TParseTreeNode treeNode : nodes) {
            if (null != treeNode && treeNode instanceof TExpression) {
                TExpression tExpr = (TExpression) treeNode;
                this.handle(tExpr);
            }
        }
    }

    public abstract void handle(TExpression expression);
}
