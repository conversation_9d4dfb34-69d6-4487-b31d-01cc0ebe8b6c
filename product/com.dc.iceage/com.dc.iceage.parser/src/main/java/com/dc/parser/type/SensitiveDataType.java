package com.dc.parser.type;

public enum SensitiveDataType {

    FULL(0, "全脱"),
    HALF(1, "半脱"),
    CLEAR(2, "明文");

    private Integer value;
    private String name;

    SensitiveDataType(Integer value, String name) {
        this.value = value;
        this.name = name;
    }

    public Integer getValue() {
        return value;
    }

    public String getName() {
        return name;
    }
}
