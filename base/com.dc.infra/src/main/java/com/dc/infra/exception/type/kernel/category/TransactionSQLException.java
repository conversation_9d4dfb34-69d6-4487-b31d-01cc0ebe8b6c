
package com.dc.infra.exception.type.kernel.category;

import com.dc.infra.exception.sqlstate.SQLState;
import com.dc.infra.exception.type.kernel.KernelSQLException;

/**
 * Transaction SQL exception.
 */
public abstract class TransactionSQLException extends KernelSQLException {
    
    private static final long serialVersionUID = 1340041110360641483L;
    
    private static final int KERNEL_CODE = 4;
    
    protected TransactionSQLException(final SQLState sqlState, final int errorCode, final String reason, final Object... messageArgs) {
        super(sqlState, KERNEL_CODE, errorCode, reason, messageArgs);
    }
    
    protected TransactionSQLException(final SQLState sqlState, final int errorCode, final String reason, final Exception cause) {
        super(sqlState, KERNEL_CODE, errorCode, reason, cause);
    }
}
