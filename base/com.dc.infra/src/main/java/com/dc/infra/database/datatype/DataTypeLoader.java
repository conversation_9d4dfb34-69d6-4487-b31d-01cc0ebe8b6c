
package com.dc.infra.database.datatype;

import com.dc.infra.database.type.DatabaseType;
import com.dc.infra.database.type.DatabaseTypeRegistry;
import com.dc.infra.utils.CaseInsensitiveMap;

import java.sql.DatabaseMetaData;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.Map;

/**
 * Data type loader.
 */
public final class DataTypeLoader {
    
    /**
     * Load data type.
     *
     * @param databaseMetaData database meta data
     * @param databaseType database type
     * @return data type map
     * @throws SQLException SQL exception
     */
    public Map<String, Integer> load(final DatabaseMetaData databaseMetaData, final DatabaseType databaseType) throws SQLException {
        Map<String, Integer> result = loadStandardDataTypes(databaseMetaData);
        result.putAll(new DatabaseTypeRegistry(databaseType).getDialectDatabaseMetaData().getExtraDataTypes());
        return result;
    }
    
    private Map<String, Integer> loadStandardDataTypes(final DatabaseMetaData databaseMetaData) throws SQLException {
        Map<String, Integer> result = new CaseInsensitiveMap<>();
        try (ResultSet resultSet = databaseMetaData.getTypeInfo()) {
            while (resultSet.next()) {
                result.put(resultSet.getString("TYPE_NAME"), resultSet.getInt("DATA_TYPE"));
            }
        }
        return result;
    }
}
