package com.dc.springboot.base.stream;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpServletResponseWrapper;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.PrintWriter;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class SteamHttpServletResponseWrapper extends HttpServletResponseWrapper {
    private final Map<String, List<String>> headers = new HashMap<>();
    private final ByteArrayOutputStream cachedBody = new ByteArrayOutputStream();
    private final ServletOutputStream outputStream = new CachedServletOutputStream(cachedBody);
    private PrintWriter writer;

    public SteamHttpServletResponseWrapper(HttpServletResponse response) {
        super(response);
    }

    // Override methods to capture headers
    @Override
    public void addHeader(String name, String value) {
        headers.computeIfAbsent(name, k -> new ArrayList<>()).add(value);
        super.addHeader(name, value);
    }

    @Override
    public void setHeader(String name, String value) {
        List<String> values = new ArrayList<>();
        values.add(value);
        headers.put(name, values);
        super.setHeader(name, value);
    }

    @Override
    public Collection<String> getHeaderNames() {
        return headers.keySet();
    }

    @Override
    public String getHeader(String name) {
        List<String> values = headers.get(name);
        return values != null && !values.isEmpty() ? values.get(0) : null;
    }

    public Map<String, List<String>> getCapturedHeaders() {
        return headers;
    }

    // Override methods to capture body
    @Override
    public ServletOutputStream getOutputStream() throws IOException {
        return outputStream;
    }

    @Override
    public PrintWriter getWriter() throws IOException {
        if (writer == null) {
            writer = new PrintWriter(outputStream, true);
        }
        return writer;
    }

    public byte[] getCachedBody() {
        return cachedBody.toByteArray();
    }

    static class CachedServletOutputStream extends ServletOutputStream {
        private final ByteArrayOutputStream cachedStream;

        public CachedServletOutputStream(ByteArrayOutputStream cachedStream) {
            this.cachedStream = cachedStream;
        }

        @Override
        public boolean isReady() {
            return true;
        }

        @Override
        public void setWriteListener(javax.servlet.WriteListener writeListener) {
            throw new UnsupportedOperationException();
        }

        @Override
        public void write(int b) throws IOException {
            cachedStream.write(b);
        }
    }
}
