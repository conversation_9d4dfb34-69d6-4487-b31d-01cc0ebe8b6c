package com.dc.config;

import com.dc.utils.bean.ReflectUtils;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.io.InputStream;
import java.lang.reflect.Field;
import java.util.Map;
import java.util.Properties;
import java.util.concurrent.ConcurrentHashMap;

@Slf4j
public abstract class ConfigInstance {

    private static final Map<String, Properties> PROPERTIES_MAP = new ConcurrentHashMap<>();

    private static final Map<String, Object> INSTANCE_MAP = new ConcurrentHashMap<>();

    /**
     * 1. 初始化的时候，先加载文件中的属性，再加载实例中的属性。
     * 2. 根据加载好的 properties 生成具体的实例对象
     */
    protected synchronized <T extends ConfigInstance> void setProperties() {

        Class<?> clazz = getClass().getSuperclass();
        String className = clazz.getName();

        PROPERTIES_MAP.computeIfAbsent(className, aClass -> {
            Properties properties = new Properties();
            try (InputStream inputStream = Thread.currentThread().getContextClassLoader().getResourceAsStream(getFolder() + getType().getResource())) {
                properties.load(inputStream);
            } catch (IOException e) {
                log.error("读取配置文件失败", e);
                return null;
            }
            ReflectUtils.handledMethodFieldValue(this, (s, o) -> properties.setProperty(s, String.valueOf(o)));
            return properties;
        });

        INSTANCE_MAP.computeIfAbsent(className, aClass -> {
            Field[] declaredFields = clazz.getDeclaredFields();
            for (Field declaredField : declaredFields) {
                declaredField.setAccessible(true);
                String declaredFieldName = declaredField.getName();
                String property = PROPERTIES_MAP.get(aClass).getProperty(declaredFieldName);
                ReflectUtils.setField(clazz, this, declaredFieldName, property);
            }
            return this;
        });
    }

    /**
     * resources 下的 文件夹名称
     */
    protected abstract String getFolder();

    /**
     * 单连接还是多连接
     */
    public abstract ConfigInstanceType getType();

    public static <T extends ConfigInstance> Properties getProperties(Class<T> clazz) {
        return (Properties) PROPERTIES_MAP.get(clazz.getName()).clone();
    }

    public static <T extends ConfigInstance> T getInstance(Class<T> clazz) {
        return (T) INSTANCE_MAP.get(clazz.getName());
    }

}
