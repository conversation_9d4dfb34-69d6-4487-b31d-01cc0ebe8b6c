package com.dc.repository.mysql.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

@Data
@TableName("pa_user_superior")
public class PaUserSuperior {

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @TableField("emp_um")
    private String empUm;

    @TableField("emp_id")
    private String empId;

    @TableField("chr_out_date")
    private String chrOutDate;

    @TableField("report_um")
    private String reportUm;

    @TableField("report_emp_id")
    private String reportEmpId;

    @TableField("gmt_create")
    private Date gmtCreate;

    @TableField("gmt_modified")
    private Date gmtModified;

    @TableField("is_delete")
    private Integer isDelete;

}
