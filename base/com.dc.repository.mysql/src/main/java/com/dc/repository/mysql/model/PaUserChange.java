package com.dc.repository.mysql.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

@Data
@TableName("pa_user_change")
public class PaUserChange {

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @TableField("um")
    private String um;

    @TableField("emp_id")
    private String empId;

    @TableField("bu_id")
    private String buId;

    @TableField("bu_name")
    private String buName;

    @TableField("chr_out_date")
    private String chrOutDate;

    @TableField("deleted")
    private String deleted;

    @TableField("eff_date")
    private Date effDate;

    @TableField("eff_seq")
    private String effSeq;

    @TableField("old_bu_id")
    private String oldBuId;

    @TableField("old_bu_name")
    private String oldBuName;

    @TableField("gmt_create")
    private Date gmtCreate;

    @TableField("gmt_modified")
    private Date gmtModified;

    @TableField("enable_change_clear")
    private Integer enableChangeClear;

    @TableField("is_delete")
    private Integer isDelete;

}
