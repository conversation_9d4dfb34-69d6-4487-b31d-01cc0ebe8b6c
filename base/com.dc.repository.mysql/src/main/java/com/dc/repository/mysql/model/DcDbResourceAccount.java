package com.dc.repository.mysql.model;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.time.LocalDateTime;

@Data
@TableName("dc_db_resource_account")
public class DcDbResourceAccount {
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id; // 主键

    @TableField("unique_key")
    private String uniqueKey; // 唯一标识

    @TableField("verified")
    private Short verified; // 连接验证: 1 已验证，0 未验证

    @TableField("password")
    private String password; // 连接密码

    @TableField("username")
    private String username; // 用户名

    @TableField("connection")
    private String connection; // 连接信息

    @TableField("connect_id")
    private String connectId; // 实例 unique_key

    @TableField("resource_id")
    private String resourceId; // 父 unique_key

    @TableField("verify_time")
    private String verifyTime; // 验证时间

    @TableField("password_update_time")
    private String passwordUpdateTime; // 密码更新

    @TableField("period")
    private Integer period; // 周期

    @TableField("user_type")
    private Short userType; // 用户类型: 1 特权, 2 业务

    @TableField("password_strength")
    private Short passwordStrength; // 密码强度: 1 强, 2 中, 3 弱

    @TableField("timing_time")
    private String timingTime; // 计时时间

    @TableField("user_source_type")
    private Short userSourceType; // 用户来源类型: 1 数据源同步, 2 手动录入

    @TableField("creator_id")
    private String creatorId; // 创建者

    @TableField("driver_id")
    private String driverId; // Summer 引擎

    @TableField("gmt_create")
    private LocalDateTime gmtCreate; // 创建时间

    @TableField("gmt_modified")
    private LocalDateTime gmtModified; // 更新时间

    @TableField("is_delete")
    private Boolean isDelete; // 是否删除

    @TableField("is_main")
    private Boolean isMain; // 是否主账号

    @TableField("db_role")
    private String dbRole; // oracle 系统角色

    @TableField("host")
    private String host; // mysql_host

    @TableField("privilege_sync_state")
    private Boolean privilegeSyncState; // 权限模型同步状态

    @TableField(exist = false)
    private DcDbResource resource;

}
