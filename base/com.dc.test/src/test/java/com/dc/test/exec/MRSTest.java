package com.dc.test.exec;

import com.dc.summer.model.DBConstants;
import com.dc.summer.model.exec.DBCExecutionPurpose;
import com.dc.summer.model.exec.jdbc.JDBCDatabaseMetaData;
import com.dc.summer.model.exec.jdbc.JDBCResultSet;
import com.dc.summer.model.exec.jdbc.JDBCSession;
import com.dc.summer.registry.center.Global;
import com.dc.type.DatabaseType;
import org.junit.jupiter.api.Test;

public class MRSTest extends BaseExecTest {
/*
    {
        "driver_id": null,
            "database_type": 33,
            "host_name": "*************:22550,*************:22551,*************:22552",
            "host_port": "",
            "server_name": "",
            "auth_model_id": "hive_kerberos",
            "user_name": "hetu_user@2528EB92_9EC4_4D94_953F_971971F2BCCA.COM",
            "auth_properties": {
        "@summer-auth-keytab@": "/Users/<USER>/Downloads/user.keytab",
                "@summer-auth-principal@": "spark2x/hadoop.2528eb92_9ec4_4d94_953f_971971f2bcca.com@2528EB92_9EC4_4D94_953F_971971F2BCCA.COM"
    }
    */
    // **************************************?
    // serviceDiscoveryMode=hsbroker&deploymentMode=on_yarn&user=hetu_user&SSL=true&SSLTrustStorePath=C:/Users/<USER>/Desktop/huawei/hetu/hetuserver.jks&KerberosConfigPath=C:/ProgramData/MIT/Kerberos5/krb5.ini&KerberosPrincipal=hetu_user&KerberosKeytabPath=C:/Users/<USER>/Desktop/huawei/user.keytab&KerberosRemoteServiceName=HTTP&KerberosServicePrincipalPattern=%24%7BSERVICE%7D%40%24%7BHOST%7D

    {

        Global.Config config = new Global.Config();
        config.setKrb5("C:/ProgramData/MIT/Kerberos5/krb5.ini");
        Global.setConfig(config);
        connectionConfiguration.setDriverId("hetu_engine");
        connectionConfiguration.setDatabaseType(DatabaseType.HETU);
        connectionConfiguration.setHostName("*************");
        connectionConfiguration.setHostPort("29860");
//        connectionConfiguration.setServerName("hive");
        connectionConfiguration.setDatabaseName("hive");
        connectionConfiguration.setUserName("hetu_user");
//        String decrypt = CipherUtils.decrypt("RNp2hvU7ydnKirNroRyh0w==");
//        connectionConfiguration.setUserPassword(decrypt);
//        connectionConfiguration.setAuthModelId("hw_hive_kerberos");
        connectionConfiguration.setAuthProperty(DBConstants.AUTH_PROP_KEYTAB, "C:/Users/<USER>/Desktop/huawei/user.keytab");
        connectionConfiguration.setAuthProperty(DBConstants.AUTH_PROP_PRINCIPAL, "hetu_user");
        connectionConfiguration.setAuthProperty(DBConstants.AUTH_PROP_SSL, "C:/Users/<USER>/Desktop/huawei/hetu/hetuserver.jks");

    }

    @Test
    public void testConnection() {
        execute("select 1");
    }

    @Test
    public void testThreeSelect() {
//        print("select * from hive.bobo.bobo_student");
        print("select * from system.runtime.nodes");
//        print("select * from systemremote.information_schema.applicable_roles");
    }

    @Test
    public void testMetaData() {

        try (JDBCSession session = (JDBCSession) getExecutionContext().openSession(monitor, DBCExecutionPurpose.UTIL, "testMetaData")) {
            JDBCDatabaseMetaData metaData = session.getMetaData();
            JDBCResultSet catalogs = metaData.getCatalogs();
            while (catalogs.nextRow()) {
                System.out.println("------------");
                System.out.println(catalogs.getAttributeValue(0));
                JDBCResultSet tables = metaData.getTables(catalogs.getAttributeValue(0).toString(), "%", "%", new String[]{"TABLE", "INDEX_TABLE"});
                int columnCount = tables.getMetaData().getColumnCount();
                while (tables.nextRow()) {
                    System.out.print("\t");
                    for (int i = 0; i < columnCount; i++) {
                        System.out.print(tables.getAttributeValue(i)+" ");
                    }
                    System.out.println();
                }
                System.out.println();
            }
            System.out.println(metaData);

        } catch (Throwable e) {
            e.printStackTrace();
        }

    }

}
