package com.dc.repository.h2.loader;

import com.dc.repository.h2.mapper.TableSqlMapper;
import com.dc.repository.mysql.mapper.SchemaMapper;
import com.dc.repository.mysql.model.Schema;
import org.apache.ibatis.session.SqlSession;
import org.apache.ibatis.session.SqlSessionFactory;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.sql.SQLException;

@Component
public class SchemaLoader {

    @Resource
    private SqlSessionFactory sqlSessionFactory;

    @PostConstruct
    public void init() {

        try (SqlSession session = sqlSessionFactory.openSession()) {

            SchemaMapper userMapper = session.getMapper(SchemaMapper.class);

            // 删除表
//            TableSqlMapper.executeDropTableSql(session, Schema.class);

            // 创建表
            TableSqlMapper.executeCreateTableSql(session, Schema.class);

            // 插入数据
            Schema schema = new Schema();
            schema.setId(1L);
            schema.setDb_type(1);
            schema.setUnique_key("abc");
            schema.setConnect_id("123");
            schema.setCatalog_name("catalogName");
            schema.setSchema_name("schemaName");
            userMapper.insert(schema);

            Schema schema1 = userMapper.selectById(1L);
            System.out.println(schema1);
            session.commit();
        } catch (SQLException e) {
            throw new RuntimeException(e);
        }
    }

}
