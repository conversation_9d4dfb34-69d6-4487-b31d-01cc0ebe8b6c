package com.dc.springboot.core.model.database;

import com.dc.summer.model.exec.DBCExecutionPurpose;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.UUID;

@Data
@ApiModel("连接信息")
public class ConnectionMessage {

    @Valid
    @NotNull
    @ApiModelProperty(value = "连接配置 - 每次传入参数不同的时候，会重新连接")
    private ConnectionConfig connectionConfig;

    public ConnectionTokenMessage generateConnectionTokenMessage(boolean autoCommit, boolean autoConnect, String tokenPrefix) {
        return generateConnectionTokenMessage(null, autoCommit, autoConnect, tokenPrefix);
    }

    public ConnectionTokenMessage generateConnectionTokenMessage(TokenConfig tokenConfig, boolean autoCommit, boolean autoConnect, String tokenPrefix) {

        if (tokenConfig == null) {
            tokenConfig = new TokenConfig();
            tokenConfig.setPurpose(DBCExecutionPurpose.USER.getId());
            tokenConfig.setAutoCommit(autoCommit);
            tokenConfig.setAutoConnect(autoConnect);
        }

        ConnectionTokenMessage connectionTokenMessage = new ConnectionTokenMessage();
        connectionTokenMessage.setToken(tokenPrefix + " " + UUID.randomUUID());
        connectionTokenMessage.setConnectionConfig(connectionConfig);
        connectionTokenMessage.setTokenConfig(tokenConfig);

        return connectionTokenMessage;
    }

}
