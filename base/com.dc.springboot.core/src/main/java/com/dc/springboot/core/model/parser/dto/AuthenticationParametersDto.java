package com.dc.springboot.core.model.parser.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
public class AuthenticationParametersDto {

    /**
     * 0: 无，1: kerberos认证
     */
    private int mode;

    @JsonProperty("license")
    private String keytab;

    private String principal;

    private String hosts;

    @JsonProperty("cert")
    private String krb5Conf;

    /**
     * 0: 无，1: SSL认证
     */
    private int isSsl;

    private String sslPath;

}
