package com.dc.springboot.core.model.parser.dto;

import lombok.Data;

@Data
public class ParamsConfigDto {

    // 单次SELECT SQL最大的行数
    private String selectLimit;

    // 单次UPDATE SQL最大的行数
    private String updateLimit;

    // 单次DELETE SQL最大的行数
    private String deleteLimit;

    // 单次INSERT SQL最大的行数
    private String insertLimit;

    // 本地导出的最大行数限制
    private String exportLimit;

    // 单次查询数据量大小
    private String dataSize;

    // 单实例访问次数上限
    private String instanceAccessLimit;

    // 单实例访问次数上限单位
    private String instanceAccessLimitUnit;

    // CLOB查询大小
    private String clobSize;

    // 结果集缓存时间
    private String resultSetCacheTimeout;

    // 复核模式
    private String reviewMode;

    // 操作备份行数限制
    private String backupDataRowLimit;

    // 默认全脱敏符号
    private String dcSensitiveDesensitizeRule;

    // 全脱敏开关(0:off 1:on)
    private Integer enableDesensiteType;

    // 全脱敏符号
    private String symbol;

}
