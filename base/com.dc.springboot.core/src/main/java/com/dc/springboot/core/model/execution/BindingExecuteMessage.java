package com.dc.springboot.core.model.execution;

import com.dc.springboot.core.model.data.Message;
import com.dc.springboot.core.model.message.ExecuteEvent;
import com.dc.springboot.core.model.result.WebSQLResultsUpdateRow;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Data
@ApiModel("绑定执行信息")
public class BindingExecuteMessage extends Message {

    @NotNull
    @ApiModelProperty(value = "查询执行模型", required = true)
    private SingleExecuteModel singleExecuteModel;

    @NotNull
    @ApiModelProperty(value = "更新的行信息")
    private WebSQLResultsUpdateRow updatedRows;

    @Valid
    @ApiModelProperty(value = "告警消息")
    private ExecuteEvent executeEvent = new ExecuteEvent();

}
