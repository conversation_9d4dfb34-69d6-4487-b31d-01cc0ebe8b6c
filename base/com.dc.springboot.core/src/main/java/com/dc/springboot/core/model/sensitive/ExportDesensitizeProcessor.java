package com.dc.springboot.core.model.sensitive;

import com.dc.springboot.core.utils.DesensitizationUtil;
import com.dc.springboot.core.model.type.BigDataType;
import com.dc.summer.model.data.DBDAttributeBinding;

public class ExportDesensitizeProcessor extends DataDesensitizeProcessor {


    public ExportDesensitizeProcessor(DataDesensitizeProcessor dataDesensitize) {
        super(dataDesensitize.getDataSource(),
                dataDesensitize.isAllowRun() ? Exec.ALLOW_RUN.getValue() : Exec.NOT_ALLOW_RUN.getValue(),
                dataDesensitize.getRules(),
                dataDesensitize.getSymbol(),
                dataDesensitize.getNodes(),
                dataDesensitize.getGradedClassifiedModel(),
                dataDesensitize.getConsoleType());
    }

    @Override
    public Object execDesensitization(Object value, int i, int isBigDataType, DBDAttributeBinding[] bindings) {
        if (value == null) {
            return null;
        }
        if (super.getIndexDataMaskMap().containsKey(i)) {
            DataMask dataMask = super.getIndexDataMaskMap().get(i);
            if (super.isAllowRun()) {
                return super.getSymbol();
            } else if (isBigDataType == BigDataType.NO.getValue()) {
                return DesensitizationUtil.desensitization(String.valueOf(value), dataMask.getAlgorithmType(), dataMask.getAlgorithmParam());
            }
        }
        return super.execDesensitization(value, i, isBigDataType, bindings);
    }

    @Override
    public Object obtainDesensitizationValue(Object value, DataMask dataMask, int isBigDataType) {
        if (super.isAllowRun()) {
            return super.getSymbol();
        } else {
            return DesensitizationUtil.desensitization(String.valueOf(value), dataMask.getAlgorithmType(), dataMask.getAlgorithmParam());
        }
    }
}
